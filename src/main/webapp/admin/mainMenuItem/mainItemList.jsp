<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>
</head>
<body>

<%
    Date now = new Date();
    int hour = now.getHours();
    String greeting;
    if (hour < 12) {
        greeting = "Good morning!";
    } else if (hour < 18) {
        greeting = "Good afternoon!";
    } else {
        greeting = "Good evening!";
    }
%>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">System User Roles</h5>
        </div>
    </div>

    <div class="row">
    <div class="col-sm-12 py-2 text-right">
        <button class="btn btn-success btn-sm" data-toggle="modal" data-target="#addRoleModal">
            + User Role
        </button>
    </div>
    </div>
    <!-- Data Table -->
    <table id="user-privileges-table" class="table table-sm table-hover" cellspacing="0" style="cursor:pointer" width="100%">
        <thead class="blueheader">
        <tr>
            <th>No.</th>
            <th>Role Name</th>
            <th style="width: 10%">Action</th>
        </tr>
        </thead>
        <tbody id="roles-table-body">
        <tr>
            <td>1</td>
            <td>Admin</td>
            <td>
                <div style="display: flex;gap: 5px">
                    <button class='btn btn-sm btn-danger'><i class="fa fa-trash"></i></button>
                    <button class='btn-primary btn btn-sm'> <i class="fa fa-edit"></i></button>
                </div>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<!-- Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" role="dialog" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">Add New Role</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="roleForm">
                    <div class="form-group">
                        <label for="roleName">Role Name</label>
                        <input type="text" class="form-control" id="roleName" placeholder="Enter role name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Close</button>
                <button type="button" id="saveRoleBtn" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>
</body>
<script>
    hideLoader()
</script>
</html>
