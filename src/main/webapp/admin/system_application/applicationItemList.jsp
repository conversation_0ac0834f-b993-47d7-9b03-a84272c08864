<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>

    <style>
        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .form-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row input[type=text] {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .form-row button:hover {
            background: #0056b3;
        }

        select[multiple] {
            width: 100%;
            height: 120px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .transfer-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .transfer-list {
            width: 250px;
            height: 300px;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow-y: auto;
            padding: 10px;
            background: #f9f9f9;
        }

        .transfer-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .transfer-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .transfer-buttons button {
            padding: 8px 12px;
            border: none;
            background: #007bff;
            color: white;
            cursor: pointer;
            border-radius: 5px;
            transition: 0.2s;
        }

        .transfer-buttons button:hover {
            background: #0056b3;
        }
    </style>


</head>
<body>

<%
    // Java code block (scriptlet)
    Date now = new Date();
    int hour = now.getHours();
    String greeting;

    if (hour < 12) {
        greeting = "Good morning!";
    } else if (hour < 18) {
        greeting = "Good afternoon!";
    } else {
        greeting = "Good evening!";
    }
%>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">System User Roles</h5>
        </div>
    </div>

    <div class="row header-bg mt-2">
        <div class="col-sm-6">
            <div class="section-box">
                <p>Role Information</p>
                <div class="form-row">
                    <input type="text" id="roleName" placeholder="Enter role name"/>
                    <button type="button" class="btn btn-primary" onclick="clearRole()">Clear</button>
                    <button type="button" class="btn btn-primary" onclick="updateRole()">Update Role</button>
                </div>
            </div>

            <div class="section-box">
                <p>Select Side Menu</p>
                <select id="sideMenu" multiple>
                    <option value="dashboard">Dashboard</option>
                    <option value="users">User Management</option>
                    <option value="reports">Reports</option>
                    <option value="settings">Settings</option>
                    <option value="logs">Logs</option>
                </select>
                <div style="margin-top:10px;text-align: right!important;">
                    <button type="button" class=" btn btn-primary" onclick="saveSideMenu()">Save Selection</button>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="transfer-container">
                <!-- Left List -->
                <div class="transfer-list" id="available">
                    <p>Available Roles</p>
                    <div class="transfer-item"><input type="checkbox" value="Admin"> Admin</div>
                    <div class="transfer-item"><input type="checkbox" value="Editor"> Editor</div>
                    <div class="transfer-item"><input type="checkbox" value="Viewer"> Viewer</div>
                    <div class="transfer-item"><input type="checkbox" value="Manager"> Manager</div>
                </div>

                <!-- Buttons -->
                <div class="transfer-buttons">
                    <button onclick="moveSelected('available','assigned')"> &gt; </button>
                    <button onclick="moveSelected('assigned','available')"> &lt; </button>
                </div>

                <!-- Right List -->
                <div class="transfer-list" id="assigned">
                    <p>Assigned Roles</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Clear role input
    function clearRole() {
        document.getElementById("roleName").value = "";
    }

    // Update role (example alert, replace with AJAX/save later)
    function updateRole() {
        const role = document.getElementById("roleName").value;
        if (role.trim() === "") {
            alert("Please enter a role name.");
            return;
        }
        alert("Role updated: " + role);
    }

    // Save side menu selection
    function saveSideMenu() {
        const selected = Array.from(document.getElementById("sideMenu").selectedOptions).map(opt => opt.value);
        alert("Saved side menu items: " + selected.join(", "));
    }

    // Transfer roles
    function moveSelected(fromId, toId) {
        const from = document.getElementById(fromId);
        const to = document.getElementById(toId);

        const checkedBoxes = from.querySelectorAll("input[type=checkbox]:checked");
        checkedBoxes.forEach(cb => {
            cb.checked = false;
            const parent = cb.parentElement;
            to.appendChild(parent);
        });
    }

    // Hide loader (your existing function call)
    hideLoader();
</script>

</body>
<script>
    hideLoader()
</script>




</html>
