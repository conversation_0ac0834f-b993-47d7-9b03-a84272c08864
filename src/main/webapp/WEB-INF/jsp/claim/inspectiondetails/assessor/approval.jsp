<%--
  Created by IntelliJ IDEA.
  User: Tharuka
  Date: 7/18/2018
  Time: 1:15 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

    String ERROR = "";
    String str_v_status_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_status_para ", "n_ref_id", "v_status_desc", "v_type IN(0,1)", "");
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/select.dataTables.min.css" rel="stylesheet"
          type="text/css"/>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <style type="text/css">
        table.dataTable th.select-checkbox::after {

        }

        /*table.dataTable tr th.select-checkbox.selected::after {*/
        /*content: "✔";*/
        /*margin-top: -11px;*/
        /*margin-left: -4px;*/
        /*text-align: center;*/
        /*text-shadow: rgb(176, 190, 217) 1px 1px, rgb(176, 190, 217) -1px -1px, rgb(176, 190, 217) 1px -1px, rgb(176, 190, 217) -1px 1px;*/
        /*}*/

        .check-align {
            margin: 0 0px;
            position: relative;
            left: 0.55rem;
        }
    </style>

    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/payment-approval.js"></script>


</head>
<body class="scroll" onload="hideLoader()">
<div class="container-fluid">
    <form name="frmMain" id="frmMain" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Assessor Payment Approval</h5>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> From Date</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" value="${txtFromDate}" name="txtFromDate"
                                       id="txtFromDate">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> To Date</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" value="${txtToDate}" name="txtToDate"
                                       id="txtToDate">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> Inspection Type</label>
                            <div class="col-sm-12">
                                <select name="txtInspectionType" id="txtInspectionType"
                                        class="form-control form-control-sm">
                                    <option value="">Please Select One</option>
                                    <option value="1">On site Inspection</option>
                                    <option value="2">Off site Inspection</option>
                                    <option value="3">Underwiting Inspection</option>
                                    <option value="4">Garage Inspection</option>
                                    <option value="5">DR Insepection</option>
                                    <option value="6">Supplimantary Inspection</option>
                                    <option value="7">After Repair inspection</option>
                                    <option value="8">Desktop Assesment</option>
                                    <option value="9">Salvage Inspection</option>
                                    <option value="10">Investigation</option>
                                    <option value="10">Investigation</option>
                                    <option value="11">Call Estimate</option>
                                </select>
                                <script type="text/javascript">
                                    $('#txtInspectionType').val('${txtInspectionType}');
                                </script>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 ml-auto">
                        <div class="p-2 bg-badge-infoo clearfix">
                            <span class="float-left" style="font-weight: 600; font-size: 20px;">Total Amount</span>
                            <span id="totalAmount1" class="label_Value " style="font-weight: 600; font-size: 18px; ">
                                ${totalAmount}</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> Payment Type</label>
                            <div class="col-sm-12">
                                <select class="form-control form-control-sm" id="paymentType" name="paymentType">
                                    <option value="">All</option>
                                    <option value="A">Assessor</option>
                                    <option value="I">Investigation</option>
                                </select>
                                <script type="text/javascript">
                                    $('#paymentType').val('${paymentType}');
                                </script>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> Status</label>
                            <div class="col-sm-12">
                                <select class="form-control form-control-sm" name="status" id="status">
                                    <option value="">All</option>
                                    <option value="P">Pending</option>
                                    <option value="A">Approve</option>
                                    <option value="R">Reject</option>
                                </select>
                                <script type="text/javascript">
                                    $('#status').val('${status}');
                                </script>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> Job Number</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" value="${txtJobNumber}" name="txtJobNumber"
                                       id="txtJobNumber">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> Vehicle Number</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" value="${txtVehicleNumber}"
                                       name="txtVehicleNumber"
                                       id="txtVehicleNumber">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> Claim Number</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" value="${txtClaimNumber}" name="txtClaimNumber"
                                       id="txtClaimNumber" onchange="removeDateSearch()" onkeyup="removeDateSearch()"
                                       onclick="removeDateSearch()">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label">Azentio Claim No</label>
                            <div class="col-sm-12">
                                <select class="form-control form-control-sm" name="claimType" id="claimType">
                                    <option value="">All</option>
                                    <option value="P">Pending</option>
                                    <option value="A">Available</option>
                                </select>
                                <script type="text/javascript">
                                    $('#claimType').val('${claimType}');
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> RTE Name</label>
                            <div class="col-sm-12">
                                <select class=" form-control form-control-sm chosen" id="rteCode"
                                        name="rteCode" onchange="search()">
                                    <option value="">Please Select One</option>
                                    <c:forEach var="listDto" items="${rteList}">
                                        <option value="${listDto.userId}">${listDto.userId}</option>
                                    </c:forEach>
                                </select>
                                <script>
                                    $("#rteCode").val("${searchRteCode}");
                                </script>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label">Assessor Name</label>
                            <div class="col-sm-12">
                                <select class="form-control form-control-sm chosen"
                                        name="assessorName"
                                        id="assessorName" onchange="search()">
                                    <option value="">Please Select One</option>
                                    <c:forEach var="listDto" items="${assessorList}">
                                        <option value="${listDto.userId}">${listDto.userId}</option>
                                    </c:forEach>
                                </select>
                                <script>
                                    $("#assessorName").val("${searchAssessorName}");
                                </script>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-sm-12 col-form-label">Policy
                                Channel
                                Type </label>
                            <div class="col-sm-12">
                                <select name="policyChannelType" id="policyChannelType"
                                        class="form-control form-control-sm">
                                    <option value="">Please Select</option>
                                    <option value="TAKAFUL">TAKAFUL</option>
                                    <option value="CONVENTIONAL">CONVENTIONAL</option>
                                </select>
                                <script>
                                    $("#policyChannelType").val("${policyChannelType}");
                                </script>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group ">
                            <button type="button" name="cancel" id="remarkButton" class="btn btn-primary"
                                    style="margin-top: 33px!important;" onclick="search();">
                                Search
                            </button>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mt-2 ">
                    <h6>Claim Result</h6>
                    <div class="mt-2" style="overflow-x: auto">
                        <table id="approvaltable" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer">
                            <thead>
                            <tr>
                                <c:if test="${IS_APPROVAL_TEAM == 'true'}">
                                    <th><input type="checkbox" id="allCheckBox" onclick="checkAllCheckBoxs();"
                                               class="check-align" value="true"/></th>
                                </c:if>
                                <th width="40px" hidden>Ref Number</th>
                                <th>Job Number</th>
                                <th>Policy Number</th>
                                <th>ISF Claim Number</th>
                                <th>Claim Number</th>
                                <th>Vehicle Number</th>
                                <th>Date & Time of Inspection</th>
                                <th>Inspection Type</th>
                                <th>Place of Inspection</th>
                                <th>Assessor Name</th>
                                <th>Allocated Professional Fee</th>
                                <th>Approved Professional Fee</th>
                                <th>Mileage</th>
                                <th>Travel Fee</th>
                                <th>Cost Of Call</th>
                                <th>Other Fee</th>
                                <th>Deductions</th>
                                <th>Total Fee</th>
                                <c:if test="${IS_APPROVAL_TEAM == 'true'}">
                                    <th>Action</th>
                                </c:if>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

                <input type="hidden" id="selectedIds" name="selectedIds">
                <c:if test="${IS_APPROVAL_TEAM == 'true'}">
                <div class="row">
                    <c:if test="${paymentType eq 'A'}">
                        <div class="col-lg-12 mt-2">
                            <button class="btn-success btn btn-lg float-right btn-xs" type="button"
                                    id="btnSendToFinance"
                                    onclick="getSelectedIds(document.getElementsByName('checkBoxs'))">Send To Finance
                                <i class="fa fa-paper-plane"></i></button>
                        </div>
                    </c:if>
                </div>
                </c:if>
                <div class="row">
                    <div class="col-md-3 ml-auto mt-4">
                        <div class="p-2 bg-badge-infoo clearfix">
                            <span class="float-left" style="font-weight: 600; font-size: 20px;">Total Amount</span>
                            <span id="totalAmount2" class="label_Value " style="font-weight: 600; font-size: 18px; ">
                                ${totalAmount}</span>
                        </div>
                    </div>
                </div>

                <c:if test="${successMessage!=null && successMessage!=''}">
                <script type="text/javascript">
                    notify('${successMessage}', "success");
                </script>
                </c:if>
                <c:if test="${errorMessage!=null && errorMessage!=''}">
                <script type="text/javascript">
                    notify('${errorMessage}', "danger");
                </script>
                </c:if>


    </form>
</div>

<div class="modal fade animate slideDown" id="rejectModal" role="dialog"
     aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Reject reason :</label>
                    <div class="col-sm-8">
                        <textarea class="form-control form-control-sm" id="remark" name="remark" type="text"
                                  onkeyup="showButton(this.value)"></textarea>
                        <div id="rejectReasonDiv" style="display: none">
                            <small class="text-danger">This Field is Required</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="reject()" id="saveBtn" disabled>Reject</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/apporove.js"></script>

<script type="text/javascript">

    $(document).ready(function () {
        if ('A' == $('#status').val() || 'R' == $('#status').val() || '' == $("#assessorName").val()) {
            $('#btnSendToFinance').hide();
        } else {
            $('#btnSendToFinance').show();
        }
    });

    function save() {
        document.frmMain.action = contextPath + "/AssessorPaymentDetailsController/saveToFinnace";
        document.frmMain.submit();
    }

    $("#assessorName").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });

    $("#rteCode").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });

    function showButton(remark) {
        if ('' != remark) {
            document.getElementById("saveBtn").disabled = false;
            $('#rejectReasonDiv').hide();
        } else {
            document.getElementById("saveBtn").disabled = true;
            $('#rejectReasonDiv').show();
        }

    }


    function getSelectedIds(elements) {

        $('#btnSendToFinance').hide();
        showLoader();

        var selectedIds = "";
        for (var i = 0; i < elements.length; i++) {

            var id = elements[i].getAttribute('id');
            if (document.getElementById(id).checked) {
                var val = id;
                selectedIds += val + ",";
            }
        }
        $('#selectedIds').val(selectedIds);
        if (selectedIds != '') {
            save();
        } else {
            notify('Please Select One', "danger");
            $('#btnSendToFinance').show();
            hideLoader();
        }

    }

    function approve(id) {
        $('.approveBtn' + id).hide();
        $('#selectedIds').val(id);
        document.frmMain.action = contextPath + "/AssessorPaymentDetailsController/saveToFinnace";
        document.frmMain.submit();
    }

    function reject() {
        if ('' != $('#remark').val()) {
            $('#rejectModal').modal('hide');
            document.frmMain.action = contextPath + "/AssessorPaymentDetailsController/reject";
            document.frmMain.submit();
        }
    }

    function search() {
        document.frmMain.action = contextPath + "/AssessorPaymentDetailsController/search";
        document.frmMain.submit();
    }

    function showRejectBox(id) {
        $('#selectedIds').val(id);
        $('#rejectModal').modal('show');
    }

    function closeModal() {
        location.href = contextPath + "/AssessorPaymentDetailsController/assessorPayments"
    }

    function checkAllCheckBoxs() {
        if (document.getElementById('allCheckBox').checked) {
            $('.allchecks').prop('checked', true);
        } else {
            $('.allchecks').prop('checked', false);
        }

    }

    function removeDateSearch() {
        $('#txtFromDate').val('');
        $('#txtToDate').val('');
    }

    $('#txtFromDate').datepicker({dateFormat: 'yy-mm-dd'});
    $('#txtToDate').datepicker({dateFormat: 'yy-mm-dd'});

</script>
</body>
</html>
