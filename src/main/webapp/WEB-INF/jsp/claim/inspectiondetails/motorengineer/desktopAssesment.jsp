<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="">
    <input class="form-control form-control-sm text-right" id="isOnsitePending"
           name="desktopInspectionDetailsDto.isOnsitePending" type="hidden">
    <fieldset class="border p-2 my-2">
        <c:if test="${motorEngineerDto.desktopInspection eq 'Y'}">
            <c:set var="isEmptyValue" value="false" scope="request"/>
        </c:if>
        <script type="text/javascript">
            $(document).ready(function () {
                try {
                    if (${motorEngineerDto.inspectionDetailsDto.recordStatus eq 9}) {
                        $('#acr').removeAttr("readonly");
                    }
                } catch (e) {

                }

            });
        </script>

        <h6> Offer</h6>
        <hr class="my-1">
        <div class="form-group row" style="display: none">
            <label class="col-sm-4 col-form-label">Provide Offer?
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.provideOffer eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.provideOffer" type="radio" value="Yes"
                                class="align-middle " onclick="setOfferType(1)"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.provideOffer eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.provideOffer" type="radio" value="No"
                                class="align-middle " onclick="setOfferType(3)"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row" style="display: none">
            <label class="col-sm-4 col-form-label">Offer Type
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8">
                <select name="desktopInspectionDetailsDto.offerType" class="form-control form-control-sm"
                        id="offerType">
                    <option value="0">Please Select</option>
                    <option value="1">On site Offer</option>
                    <option value="3">Call Estimate</option>
                </select>
            </div>
            <script>document.getElementById("offerType").value = "${motorEngineerDto.desktopInspectionDetailsDto.offerType}";</script>
        </div>
    </fieldset>
    <fieldset class="border p-2 mt-2">
        <h6>Desktop Review</h6>
        <hr class="my-1">
        <div class="form-group row" style="display: none">
            <label class="col-sm-4 col-form-label">Approximate Cost Report :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.appCostReport" placeholder="Approximate Cost Report"
                       value="${isEmptyValue? '' : motorEngineerDto.desktopInspectionDetailsDto.appCostReport}"
                       class="form-control form-control-sm " id="appCostReport">
            </div>
        </div>
        <div class="form-group row" style="display: none;">
            <label class="col-sm-4 col-form-label">Pre Accident Value (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.preAccidentValue" placeholder="Pre Accident Value"
                       value="${isEmptyValue? '' : motorEngineerDto.desktopInspectionDetailsDto.preAccidentValue}"
                       class="form-control form-control-sm  text-right" id="preAccidentValue">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label text-danger font-weight-bold">Total Approve ACR (Rs.) :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-danger font-weight-bold"></span>
                <span class="label_Value input-view text-danger font-weight-bold" id="totalApproveAcr"><fmt:formatNumber
                        value="${motorEngineerDto.totalApproveAcrAmount}"
                        pattern="###,##0.00;"
                        type="number"/></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.acr" placeholder="ACR"
                       value="${isEmptyValue? '' : motorEngineerDto.desktopInspectionDetailsDto.acr}"
                       class="form-control form-control-sm acr text-right" id="acr" autocomplete="off">
            </div>
            <c:if test="${!(motorEngineerDto.inspectionDetailsDto.recordStatus eq 33
                                || motorEngineerDto.inspectionDetailsDto.recordStatus eq 34 ||
                                motorEngineerDto.inspectionDetailsDto.recordStatus eq 9)}">
            </c:if>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Excess (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.excess" placeholder="Excess"
                       value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                       class="form-control form-control-sm text-right " id="excess" readonly>
            </div>
        </div>
        <input type="hidden" id="boldPercent" name="desktopInspectionDetailsDto.boldPercent"/>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Bald Tyre Penalty
                :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.boldTyrePenaltyAmount" placeholder="Bald Tyre Penalty"
                       value="${isEmptyValue? '' : motorEngineerDto.desktopInspectionDetailsDto.boldTyrePenaltyAmount}"
                       class="form-control form-control-sm underpenaltyamount text-right" id="boldTyrePenaltyAmount">
            </div>
        </div>
        <input type="hidden" id="underPenaltyPercent" name="desktopInspectionDetailsDto.underPenaltyPercent"/>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Under Insurance Penalty: </label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.underInsurancePenaltyAmount"
                       placeholder="Under Insurance Penalty"
                       value="${isEmptyValue? '' : motorEngineerDto.desktopInspectionDetailsDto.underInsurancePenaltyAmount}"
                       class="form-control form-control-sm underpenaltyamount text-right"
                       id="underInsurancePenaltyAmount">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Payable Amount (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.payableAmount" placeholder="Payable Amount"
                       value="${isEmptyValue? '' : motorEngineerDto.desktopInspectionDetailsDto.payableAmount}"
                       class="form-control form-control-sm payableamount text-right" id="payableAmount">
            </div>
        </div>

        <c:if test="${motorEngineerDto.inspectionDetailsDto.recordStatus != 8 && motorEngineerDto.inspectionDetailsDto.recordStatus != 80 && motorEngineerDto.inspectionDetailsDto.recordStatus != 0}">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label text-danger font-weight-bold">Current Approved Advance Amount
                    (Rs.) :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <span class="label_Value input-view text-danger font-weight-bold"
                          id="currentAdvanceAmount"><fmt:formatNumber
                            value="${motorEngineerDto.currentApprovedAdvanceAmount}"
                            pattern="###,##0.00;"
                            type="number"/></span>
                </div>
            </div>
        </c:if>

        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Advance Amount (Rs.)
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <input placeholder="Advance Amount" class="form-control form-control-sm advancedamount text-right"
                       id="advanceAmount"
                       value="0.00"
                       name="desktopInspectionDetailsDto.advancedAmount" autocomplete="off">
            </div>
        </div>
        <div class="form-group row">
            <div class="col-sm-8 input-group offset-sm-4">
                <span class="label_Value input-view text-primary"></span>
                <span class="label_Value input-view text-danger font-weight-bold"></span>
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input id="advanceAdd" onchange="setBalanceAdvanceAmount()"
                               name="desktopInspectionDetailsDto.advanceChange" type="radio" value="ADD"
                               class="align-middle advance" checked/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Add</span>
                    </label>
                    <c:if test="${motorEngineerDto.inspectionDetailsDto.recordStatus eq 10 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 14
                    || motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 29 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 9}">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input id="advanceReduce" onchange="setBalanceAdvanceAmount()"
                                   name="desktopInspectionDetailsDto.advanceChange" type="radio" value="REDUCE"
                                   class="align-middle advance"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Reduce</span>
                        </label>
                    </c:if>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Paid Advance Amount (Rs.)
                :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-danger font-weight-bold"></span>
                <input value="${isEmptyValue? '0.00':motorEngineerDto.paidAdvanceAmount}"
                       placeholder="Paid Advance Amount" class="form-control form-control-sm  text-right"
                       id="paidAdvanceAmount" name="desktopInspectionDetailsDto.paidAdvanceAmount" disabled>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Balance Advance Amount (Rs.)
                :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-danger font-weight-bold"></span>
                <input value="${isEmptyValue? '0.00':motorEngineerDto.balanceAdvanceAmount}"
                       placeholder="Balance Advance Amount" class="form-control form-control-sm  text-right"
                       id="balanceAdvanceAmount" name="desktopInspectionDetailsDto.balanceAdvanceAmount" disabled>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Total Approved Advance Amount (Rs.):</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-danger font-weight-bold"></span>
                <input value="${isEmptyValue? '0.00':motorEngineerDto.totalApprovedAdvanceAmount}"
                       placeholder="Total Approved Advance Amount" class="form-control form-control-sm  text-right"
                       id="totalApprovedAdvanceAmount" name="desktopInspectionDetailsDto.totalApprovedAdvanceAmount"
                       disabled>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label text-danger font-weight-bold">Current Provision (Rs.)
                :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-danger font-weight-bold"></span>
                <input value="${isEmptyValue? '0.00':motorEngineerDto.currentProvision}"
                       placeholder="Current Provision" class="form-control form-control-sm  text-right"
                       id="currentProvision" name="desktopInspectionDetailsDto.currentProvision" disabled>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Desktop Offer
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.desktopOffer eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.desktopOffer" type="radio" value="Yes"
                                id="desktopOfferYes"
                                class="align-middle desktopoffer"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.desktopOffer eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.desktopOffer" type="radio" value="No"
                                id="desktopOfferNo"
                                class="align-middle desktopoffer"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">ARI Salvage
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.ariSalvage eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.ariSalvage" type="radio" value="Yes"
                                class="align-middle arisalvage"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.ariSalvage eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.ariSalvage" type="radio" value="No"
                                class="align-middle arisalvage"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
    </fieldset>
    <fieldset class="border p-2 mt-2">
        <h6> Assessor Remarks</h6>
        <hr class="my-1">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Settlement Methods
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8">
                <select name="desktopInspectionDetailsDto.settlementMethod"
                        class="form-control form-control-sm settlementMethod"
                        id="settlementMethod">
                    ${DbRecordCommonFunctionBean.getPopupList("claim_settlement_method WHERE settlement_id NOT IN (5,6)", "settlement_id", "value")}
                </select>
            </div>
            <script>document.getElementById("settlementMethod").value = "${ empty motorEngineerDto.desktopInspectionDetailsDto.settlementMethod ? '0':motorEngineerDto.desktopInspectionDetailsDto.settlementMethod }";</script>
            <script>
                $('#settlementMethod').change(function () {
                    if (document.getElementById("settlementMethod").value == '2') {
                        $("#desktopOfferNo").prop("checked", true);
                        $("#desktopOfferYes").prop('disabled', true);
                    } else if ('${motorEngineerDto.inspectionDetailsDto.recordStatus}' != '10' || '${motorEngineerDto.inspectionDetailsDto.recordStatus}' != '14') {
                        $("#desktopOfferYes").prop('disabled', false);
                    }
                });

            </script>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Inspection Remarks :</label>
            <div class="col-sm-8">
                <textarea name="desktopInspectionDetailsDto.inspectionRemark" class="form-control form-control-sm "
                >${motorEngineerDto.desktopInspectionDetailsDto.inspectionRemark}</textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Police Report Request<span
                    class="text-danger font-weight-bold">  *</span> :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.policeReportRequested eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.policeReportRequested" type="radio" value="Yes"
                                class="align-middle policerequested"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.policeReportRequested eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.policeReportRequested" type="radio" value="No"
                                class="align-middle policerequested"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Special Remarks :</label>
            <div class="col-sm-8">
                <textarea name="desktopInspectionDetailsDto.specialRemark"
                          class="form-control form-conInvestigate Claimtrol-sm "
                          id="specialRemark">${motorEngineerDto.desktopInspectionDetailsDto.specialRemark}</textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Investigate Claim
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.investigaedClaim eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.investigaedClaim" type="radio" value="Yes"
                                class="align-middle investigate"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${motorEngineerDto.desktopInspectionDetailsDto.investigaedClaim eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.investigaedClaim" type="radio" value="No"
                                class="align-middle investigate"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="mt-3">
            <div class="float-right">
                <%--${motorEngineerDto.inspectionDetailsDto.recordStatus eq 9--%>
                <%--|| motorEngineerDto.inspectionDetailsDto.recordStatus eq 33--%>
                <%--|| motorEngineerDto.inspectionDetailsDto.recordStatus eq 34--%>
                <%--? 'disabled' : ''}>--%>
                <c:if test="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.recordStatus ne 23 && motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.recordStatus ne 4}">
                    <c:if test="${motorEngineerDto.inspectionDetailsDto.fwdTcDesktopUser ne G_USER.userId  and ((G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && (motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 14)) || (G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser && (motorEngineerDto.inspectionDetailsDto.recordStatus eq 29 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 10)) || G_USER.accessUserType eq 1 || G_USER.accessUserType eq 23 || G_USER.accessUserType eq 24)}">

                        <c:if test="${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && (motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 14))}">
                            <button id="btnReturn" value="return"
                                    class=" btn btn-danger">
                                Return
                            </button>
                        </c:if>


                        <c:set var="isAuthorizeDisabled"
                               value="${motorEngineerDto.inspectionDetailsDto.recordStatus eq 33
                                || motorEngineerDto.inspectionDetailsDto.recordStatus eq 34 ||
                                motorEngineerDto.inspectionDetailsDto.recordStatus eq 9
                                || motorEngineerDto.inspectionDetailsDto.recordStatus eq 4}"/>


                        <div class="row">
                            <div class="col-lg-12" style="padding: 12px">
                                <div class="d-flex align-items-center gap-3">
                                    <!-- ✅ Show checkbox only if Authorize button is enabled -->
                                        <%--                                    && motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.typeOnlineInspection eq 'Y'--%>
                                    <c:if test="${not isAuthorizeDisabled}">
                                        <div class="form-check mb-0">
                                            <input class="form-check-input" type="checkbox" id="onsiteReviewCheck"
                                                   name="chkConfirm">
                                            <label class="form-check-label" for="onsiteReviewCheck">
                                                Onsite Review
                                            </label>
                                        </div>
                                    </c:if>

                                    <button type="submit" class="btn btn-primary ml-1" id="cmdAuth"
                                            class="btn btn-primary" ${isAuthorizeDisabled ? 'disabled' : ''}>
                                        Authorize
                                    </button>
                                </div>
                            </div>
                        </div>


                        <button style="display: none" class="btn btn-primary ml-1"
                                type="submit" id="forward"
                                value="forward"
                                class="btn btn-primary"  ${motorEngineerDto.inspectionDetailsDto.recordStatus eq 33
                            || motorEngineerDto.inspectionDetailsDto.recordStatus eq 34 ||
                            motorEngineerDto.inspectionDetailsDto.recordStatus eq 9
                            || motorEngineerDto.inspectionDetailsDto.recordStatus eq 4
                            ? 'disabled' : ''}>
                            Forward
                        </button>

                        <button onclick="returnDesktop()" type="button"
                                class="btn btn-primary ml-1" ${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' and (motorEngineerDto.inspectionDetailsDto.recordStatus eq 9 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 34 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 33 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 4) ? 'disabled' : ''}>
                            Reject Desktop
                        </button>
                        <button onclick="forwardToInformDesktop()" type="button"
                                class="btn btn-primary ml-1 skipReadOnly" ${(motorEngineerDto.inspectionDetailsDto.recordStatus eq 0 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 33 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 34 || motorEngineerDto.desktopInspectionDetailsDto.isInformed eq 'YES' || motorEngineerDto.inspectionDetailsDto.recordStatus eq 4) ? 'disabled' : ''}>
                            Forward To Inform Amendment
                        </button>
                        <button onclick="recallDesktop()" type="button"
                                class="btn btn-primary ml-1 skipReadOnly" ${motorEngineerDto.desktopInspectionDetailsDto.isInformed eq 'YES' || (motorEngineerDto.inspectionDetailsDto.recordStatus eq 0 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 29 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 9 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 34 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 4) ? 'disabled' : ''}>
                            Recall Inform Desktop
                        </button>
                    </c:if>
                </c:if>

                <input type="hidden" name="forwardToInformDesktopUser" id="forwardToInformDesktopUser"/>
            </div>
        </div>
    </fieldset>

    <fieldset class="border p-2 mt-2">
        <div class="mt-3">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Inform To Garage - Name :</label>
                <div class="col-sm-8">
                    <input name="desktopInspectionDetailsDto.informToGarageName" placeholder=""
                           value="${motorEngineerDto.desktopInspectionDetailsDto.informToGarageName}"
                           class="form-control form-control-sm skipReadOnly"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Inform To Garage - Contact :</label>
                <div class="col-sm-8">
                    <input name="desktopInspectionDetailsDto.informToGarageContact" placeholder=""
                           value="${motorEngineerDto.desktopInspectionDetailsDto.informToGarageContact}"
                           class="form-control form-control-sm skipReadOnly"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label"><span
                        class="text-danger font-weight-bold">  </span>
                </label>
                <div class="col-sm-8 input-group">
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.desktopInspectionDetailsDto.isAgreeGarage eq 'Agree' and not isEmptyValue ? 'checked' : ''}
                                    name="desktopInspectionDetailsDto.isAgreeGarage" type="radio" value="Agree"
                                    class="align-middle skipReadOnly radiogroup1" id="radio3"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description radioname1">Agree</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.desktopInspectionDetailsDto.isAgreeGarage eq 'Disagree' and not isEmptyValue ? 'checked' : ''}
                                    name="desktopInspectionDetailsDto.isAgreeGarage" type="radio" value="Disagree"
                                    class="align-middle skipReadOnly radiogroup1" id="radio4"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description radioname1">Disagree</span>
                        </label>
                        <small class="help-block radiomessege1" style="color: red;margin-left: 17px;">Please select
                        </small>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Inform to Customer - Name :</label>
                <div class="col-sm-8">
                    <input name="desktopInspectionDetailsDto.informToCustomerName" placeholder=""
                           value="${motorEngineerDto.desktopInspectionDetailsDto.informToCustomerName}"
                           class="form-control form-control-sm skipReadOnly"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Inform to Customer - Contact :</label>
                <div class="col-sm-8">
                    <input name="desktopInspectionDetailsDto.informToCustomerContact" placeholder=""
                           value="${motorEngineerDto.desktopInspectionDetailsDto.informToCustomerContact}"
                           class="form-control form-control-sm skipReadOnly"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label"><span
                        class="text-danger font-weight-bold">  </span>
                </label>
                <div class="col-sm-8 input-group">
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.desktopInspectionDetailsDto.isAgreeCustomer eq 'Agree' and not isEmptyValue ? 'checked' : ''}
                                    name="desktopInspectionDetailsDto.isAgreeCustomer" type="radio" value="Agree"
                                    class="align-middle  skipReadOnly radiogroup2" id="radio1" "/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description radioname">Agree</span>
                        </label>

                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.desktopInspectionDetailsDto.isAgreeCustomer eq 'Disagree' and not isEmptyValue ? 'checked' : ''}
                                    name="desktopInspectionDetailsDto.isAgreeCustomer" type="radio" value="Disagree"
                                    class="align-middle  skipReadOnly radiogroup2" id="radio2"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description radioname">Disagree</span>
                        </label>

                        <small class="help-block radiomessege2" style="color: red;margin-left: 17px;">Please select
                            One
                        </small>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Comment :</label>
                <div class="col-sm-8">
                    <input name="desktopInspectionDetailsDto.desktopComment" placeholder=""
                           value="${motorEngineerDto.desktopInspectionDetailsDto.desktopComment}"
                           class="form-control form-control-sm skipReadOnly"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Reason :</label>
                <div class="col-sm-8">
                    <input name="desktopInspectionDetailsDto.reasonForDisagree" placeholder=""
                           value="${motorEngineerDto.desktopInspectionDetailsDto.reasonForDisagree}"
                           class="form-control form-control-sm skipReadOnly"/>
                </div>
            </div>
            <c:if test="${G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser}">
                <div class="mt-3">
                    <div class="float-right">
                        <c:set var="buttonDisable" value=""/>

                        <c:if test="${((motorEngineerDto.inspectionDetailsDto.recordStatus eq 33 &&
                     motorEngineerDto.inspectionDetailsDto.fwdTcDesktopUser ne G_USER.userId) or
                    motorEngineerDto.desktopInspectionDetailsDto.isInformed eq 'YES') ||
                     (motorEngineerDto.inspectionDetailsDto.recordStatus ne 9 &&
                     motorEngineerDto.inspectionDetailsDto.recordStatus ne 80 &&
                      motorEngineerDto.inspectionDetailsDto.recordStatus ne 33)}">

                            <c:set var="buttonDisable" value="disabled"/>
                        </c:if>
                            <%--<button onclick="informDesktop()" type="button" class="btn btn-primary skipReadOnly" ${buttonDisable}>--%>
                            <%--Informed--%>
                            <%--</button>--%>
                        <button onclick="myFunctionvalidateInfromdbtn()" type="button"
                                class="btn btn-primary skipReadOnly" ${buttonDisable} >
                            Informed
                        </button>
                    </div>
                </div>
            </c:if>
        </div>
    </fieldset>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $('.radiomessege1').hide();
        $('.radiomessege2').hide();
        if (${(motorEngineerDto.inspectionDetailsDto.recordStatus =='10' || motorEngineerDto.inspectionDetailsDto.recordStatus =='14')} &&
        ${motorEngineerDto.desktopInspectionDetailsDto.desktopOffer eq 'No'})
        {
            $("#desktopOfferYes").prop('disabled', true);
        }


        if (${motorEngineerDto.inspectionDetailsDto.recordStatus == 80 && G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser}) {
            $('#advanceAmount').val('${motorEngineerDto.desktopInspectionDetailsDto.advancedAmount}');
            if (${motorEngineerDto.garageInspectionDetailsDto.advanceChange == 'ADD'}) {
                document.getElementById('advanceAdd').checked = true;
                setBalanceAdvanceAmount();
            } else {
                document.getElementById('advanceReduce').checked = true;
                setBalanceAdvanceAmount();
            }
        }
    });

    $("#advanceAmount").keyup(function () {
        setBalanceAdvanceAmount();
    });

    $("#advanceAmount").change(function () {
        setBalanceAdvanceAmount();
    });

    function setBalanceAdvanceAmount() {
        var balanceAdvanceAmount = '${motorEngineerDto.balanceAdvanceAmount}';
        var advanceAmountChange = $('#advanceAmount').val();
        var calType = 'ADD';
        if (document.getElementById('advanceAdd').checked) {
            calType = 'ADD';
        } else {
            calType = "REDUCE";
        }

        var dataObj = {
            balanceAdvanceAmount: balanceAdvanceAmount,
            advanceAmountChange: advanceAmountChange,
            calType: calType
        };

        var URL = "${pageContext.request.contextPath}/MotorEngineerController/setBalanceAdvanceAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj == "ERROR") {
                    notify('Balance Advance Amount Cannot be less than zero', "danger");
                    $('#advanceAmount').val('0.00');
                    $('#balanceAdvanceAmount').val(balanceAdvanceAmount)
                } else {
                    $('#balanceAdvanceAmount').val(obj);
                }
            }
        });
    }

   function setAddOrReduce(){
       var advanceChange = '${motorEngineerDto.desktopInspectionDetailsDto.advanceChange}';
       if(advanceChange == "ADD"){
           document.getElementById('advanceAdd').checked = true;
           setBalanceAdvanceAmount();
       }else{
           document.getElementById('advanceReduce').checked = true;
           setBalanceAdvanceAmount();
       }
   }


    function myFunctionvalidateInfromdbtn() {
        if (($('#radio1').is(':checked') || $('#radio2').is(':checked')) && ($('#radio3').is(':checked') || $('#radio4').is(':checked'))) {
            informDesktop();
        } else {
            infromdbtn1();
            infromdbtn2();
        }
    }

    function infromdbtn1() {
        if (($('#radio3').is(':checked') || $('#radio4').is(':checked'))) {
            $(".radioname1").css("color", "black");
            $('.radiomessege1').hide();
        } else {
            $(".radioname1").css("color", "red");
            $('.radiomessege1').show();

        }
    }

    function infromdbtn2() {
        if (($('#radio1').is(':checked') || $('#radio2').is(':checked'))) {
            $(".radioname").css("color", "black");
            $('.radiomessege2').hide();
        } else {
            $(".radioname").css("color", "red");
            $('.radiomessege2').show();

        }
    }
</script>

<script type="text/javascript">
    function forwardToInformDesktop() {


        $.ajax({
            url: contextPath + "/MotorEngineerController/getTcUserList",
            type: 'GET',
            success: function (result) {

                var optionArr = [];
                var userArr = JSON.parse(result);
                var option1 = {text: 'Please Select', value: ''};
                optionArr.push(option1);
                for (var i = 0; i < userArr.length; i++) {
                    var option = {text: userArr[i].userId, value: userArr[i].userId};
                    optionArr.push(option);
                }

                bootbox.prompt({
                    title: "Please select user to forward",
                    inputType: 'select',
                    inputOptions: optionArr,
                    callback: function (result) {
                        if (result === null) {
                            // Prompt dismissed
                        } else {
                            if (result === '') {
                                return false;
                            }
                            $('#forwardToInformDesktopUser').val(result);
                            document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/forwardToInformDesktop";
                            document.frmMain.method = "POST";
                            document.frmMain.submit();
                        }
                    }
                });
            }
        });


    }

    $('input[type=button]').click(function () {
        if ($("#buttons input:radio:checked").length > 0)
            alert("Selected");
        else
            alert("Not selected");
    });

    function informDesktop() {
        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/informDesktop";
        document.frmMain.method = "POST";
        document.frmMain.submit();
    }

    function recallDesktop() {
        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/recallDesktop";
        document.frmMain.method = "POST";
        document.frmMain.submit();
    }

    function returnDesktop() {
        bootbox.confirm({
            message: 'Do you want to Reject Desktop?',
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary float-right'
                }
            },
            callback: function (result) {
                if (result == true) {
                    document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/returnDesktop";
                    document.frmMain.method = "POST";
                    document.frmMain.submit();
                } else {
                    return;
                }
            }
        });
    }

    function returnToRte() {
        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/taskReturnDesktop";
        document.frmMain.method = "POST";
        document.frmMain.submit();
    }
</script>

<script>

    function enableDisableBoldTyrePenalty(stat) {
        if (stat == "Y") {
            $(".boldtirepenaltyamount").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
            $(".boldpercent").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.boldTirePenaltyAmount', true);
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.boldPercent', true);
        } else {
            $('#boldTirePenaltyAmount').val('');
            $('#boldPercent').val('');
            $(".boldpercent").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
            $(".boldtirepenaltyamount").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.boldTirePenaltyAmount', false);
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.boldPercent', false);
        }
    }

    function enableDisableUnderInsuradPenalty(stat) {
        if (stat == "Y") {
            $(".underpenaltyamount").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
            $(".underpenaltypercent").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.underPenaltyPercent', true);
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.underPenaltyAmount', true);
        } else {
            $('#underPenaltyAmount').val('');
            $('#underPenaltyPercent').val('');
            $(".underpenaltyamount").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
            $(".underpenaltypercent").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.underPenaltyAmount', false);
            $('#frmMain').data('formValidation').enableFieldValidators('desktopInspectionDetailsDto.underPenaltyPercent', false);
        }
    }

    $(document).ready(function () {
        if ("No" == $("input[name='desktopInspectionDetailsDto.underInsuradPenalty']:checked").val()) {
            enableDisableUnderInsuradPenalty('N');
        }
        if ("No" == $("input[name='desktopInspectionDetailsDto.boldTyrePenalty']:checked").val()) {
            enableDisableBoldTyrePenalty('N');
        }
    });

    $(document).ready(function () {
        if ("Yes" == $("input[name='desktopInspectionDetailsDto.provideOffer']:checked").val()) {
            setOfferType(1);
        } else if ("No" == $("input[name='desktopInspectionDetailsDto.provideOffer']:checked").val()) {
            setOfferType(3);
        } else {
            setOfferType(0);
        }

    });

    function setOfferType(offerTypeId) {
        $('#offerType > option').each(function () {
            if (offerTypeId == $(this).val()) {
                $(this).prop('disabled', false);
                $(this).prop('selected', true);
            } else {
                $(this).prop('disabled', true);
            }
        });
    }


    function calculateBaldTyrePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            boldPercent: $("#boldPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=BaldTyrePenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#boldTyrePenaltyAmount").val(result);
                calculatePayableAmount();
            }
        });
    }

    function calculateUnderInsurancePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            underPenaltyPercent: $("#underPenaltyPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=UnderPenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underInsurancePenaltyAmount").val(result.trim());
                calculatePayableAmount();
            }
        });
    }

    function calculatePayableAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            excess: $("#excess").val(),
            underPenaltyAmount: $("#underInsurancePenaltyAmount").val(),
            boldTirePenaltyAmount: $("#boldTyrePenaltyAmount").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=PayableAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#payableAmount").val(result);
            }
        });
    }

    function calculateUnderInsPenaltyPerc() {
        var dataObj = {
            pav: $("#pav").val(),
            sumInsured: $("#sumInsuredVal").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateUnderInsPenaltyPerc";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underPenaltyPercent").val(parseFloat(result.trim()) < 0 ? '0.00' : result.trim());
                calculateUnderInsurancePenaltyAmount();
            }
        });
    }

    // $("#acr").keyup(function () {
    //     isForward();
    // });

    $("#pav").change(function () {
        calculateUnderInsPenaltyPerc();
    });


    <%--function myFunction() {--%>
    <%--if (${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'P' and (motorEngineerDto.inspectionDetailsDto.recordStatus eq 33 && motorEngineerDto.inspectionDetailsDto.fwdTcDesktopUser ne G_USER.userId) || (motorEngineerDto.inspectionDetailsDto.recordStatus ne 9 && motorEngineerDto.inspectionDetailsDto.recordStatus ne 33)) {--%>
    <%--}--%>
    <%--if ((($('#radio1').is(':checked') || $('#radio2').is(':checked')) && ($('#radio3').is(':checked') || $('#radio4').is(':checked'))) &&) {--%>
    <%--$('#Informedbtn').prop('disabled', false);--%>
    <%--}--%>
    <%--}--%>

    $("#acr").change(function () {
        calculateBoldTyerPenaltyPerc();
        calculateUnderInsPenaltyPerc();
    });

    $("#boldTyrePenaltyAmount,#underInsurancePenaltyAmount").change(function () {
        calculatePayableAmount();
    });

    function calculateBoldTyerPenaltyPerc() {
        var wheelCount = 0;
        var boldWheelCount = 0;
        var boldTyrePercentage = "0";
        for (var i = 0; i < 7; i++) {
            var val = $("#cot_0_" + i).val();
            if ("N/A" != val) {
                wheelCount++;
            }
            if ("Bald" == val) {
                boldWheelCount++;
            }
        }
        switch (wheelCount) {
            case 2:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "20.00";
                        break;
                    case 2:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 3:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "10.00";
                        break;
                    case 2:
                        boldTyrePercentage = "30.00";
                        break;
                    case 3:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 4:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "20.00";
                        break;
                    case 3:
                        boldTyrePercentage = "30.00";
                        break;
                    case 4:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 6:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "10.00";
                        break;
                    case 3:
                        boldTyrePercentage = "20.00";
                        break;
                    case 4:
                        boldTyrePercentage = "30.00";
                        break;
                    case 5:
                        boldTyrePercentage = "40.00";
                        break;

                }
                break;
        }
        $("#boldPercent").val(parseFloat(boldTyrePercentage) < 0 ? '0.00' : boldTyrePercentage);
        calculateBaldTyrePenaltyAmount();
    }

    $(".cot_0").change(function () {
        calculateBoldTyerPenaltyPerc();
    });

    $(document).ready(function(){
        setAddOrReduce();
        setBalanceAdvanceAmount();
    });
</script>
