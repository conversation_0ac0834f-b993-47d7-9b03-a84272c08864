<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="">
    <input class="form-control form-control-sm text-right" id="isOnsitePending"
           name="garageInspectionDetailsDto.isOnsitePending" type="hidden">

    <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
        <fieldset class="border p-2 mt-2">
            <h6>Garage Assessment</h6>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label text-danger font-weight-bold">Total Approve ACR (Rs.) :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <span class="label_Value input-view text-danger font-weight-bold" id="totalAcr"><fmt:formatNumber
                            value="${motorEngineerDto.totalApproveAcrAmount}"
                            pattern="###,##0.00;"
                            type="number"/></span>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.acr}"
                        pattern="###,##0.00;"
                        type="number"/> </span>
                    <input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.acr}" placeholder="ACR"
                           class="form-control form-control-sm acr text-right" id="acr"
                           name="garageInspectionDetailsDto.acr" autocomplete="off">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Excess (Rs.) :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary">
                    <fmt:formatNumber
                            value="${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.excess}"
                            pattern="###,##0.00;"
                            type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.excess}"
                           placeholder="Excess" class="form-control form-control-sm text-right" id="excess"
                           name="garageInspectionDetailsDto.excess">
                </div>
            </div>
            <input type="hidden" id="boldPercent" name="garageInspectionDetailsDto.boldPercent"/>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Bald Tyre Penalty (Rs.)
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.boldTyrePenaltyAmount}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.boldTyrePenaltyAmount}"
                           placeholder="Bald Tyre Penalty" class="form-control form-control-sm excess text-right"
                           id="boldTyrePenaltyAmount" name="garageInspectionDetailsDto.boldTyrePenaltyAmount">
                </div>
            </div>
            <input type="hidden" id="underPenaltyPercent" name="garageInspectionDetailsDto.underPenaltyPercent"/>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Under Insurance Penalty (Rs.) :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.underInsurancePenaltyAmount}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.underInsurancePenaltyAmount}"
                           placeholder="Under Insurance Penalty"
                           class="form-control form-control-sm underpenaltyamount text-right"
                           id="underInsurancePenaltyAmount"
                           name="garageInspectionDetailsDto.underInsurancePenaltyAmount">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Payable Amount (Rs.) :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.payableAmount}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.payableAmount}"
                           placeholder="Payable Amount" class="form-control form-control-sm payableamount text-right"
                           id="payableAmount" name="garageInspectionDetailsDto.payableAmount">
                </div>
            </div>

            <c:if test="${motorEngineerDto.inspectionDetailsDto.recordStatus != 8 && motorEngineerDto.inspectionDetailsDto.recordStatus != 80}">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label text-danger font-weight-bold">Current Approved Advance Amount
                        (Rs.) :</label>
                    <div class="col-sm-8 input-group">
                        <span class="label_Value input-view text-danger font-weight-bold"></span>
                        <span class="label_Value input-view text-danger font-weight-bold"
                              id="currentAdvanceAmount"><fmt:formatNumber
                                value="${motorEngineerDto.currentApprovedAdvanceAmount}"
                                pattern="###,##0.00;"
                                type="number"/></span>
                    </div>
                </div>
            </c:if>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Advance Amount (Rs.)
                    <span class="text-danger font-weight-bold"> *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary"><fmt:formatNumber
                            value="${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.advancedAmount}"
                            pattern="###,##0.00;"
                            type="number"/></span>
                    <input value="0.00"
                           placeholder="Advance Amount" class="form-control form-control-sm  text-right"
                           id="advanceAmount" name="garageInspectionDetailsDto.advancedAmount" autocomplete="off">
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-8 input-group offset-sm-4">
                    <span class="label_Value input-view text-primary"></span>
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input id="advanceAdd" onchange="setBalanceAdvanceAmount()"
                                   name="garageInspectionDetailsDto.advanceChange" type="radio" value="ADD"
                                   class="align-middle advance" checked/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Add</span>
                        </label>
                        <c:if test="${motorEngineerDto.inspectionDetailsDto.recordStatus eq 10 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 80
                         || motorEngineerDto.inspectionDetailsDto.recordStatus eq 14 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 8 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 9}">
                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                <input id="advanceReduce" onchange="setBalanceAdvanceAmount()"
                                       name="garageInspectionDetailsDto.advanceChange" type="radio" value="REDUCE"
                                       class="align-middle advance"/>
                                <span class="radiomark"></span>
                                <span class="custom-control-description">Reduce</span>
                            </label>
                        </c:if>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Paid Advance Amount (Rs.)
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <input value="${motorEngineerDto.paidAdvanceAmount}"
                           placeholder="Paid Advance Amount" class="form-control form-control-sm  text-right"
                           id="paidAdvanceAmount" name="paidAdvanceAmount" disabled>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Balance Advance Amount (Rs.)
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <input value="${motorEngineerDto.balanceAdvanceAmount}"
                           placeholder="Balance Advance Amount" class="form-control form-control-sm  text-right"
                           id="balanceAdvanceAmount" name="balanceAdvanceAmount" disabled>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Total Approved Advance Amount (Rs.):</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <input value="${motorEngineerDto.totalApprovedAdvanceAmount}"
                           placeholder="Total Approved Advance Amount" class="form-control form-control-sm  text-right"
                           id="totalApprovedAdvanceAmount" name="totalApprovedAdvanceAmount"
                           disabled>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label text-danger font-weight-bold">Current Provision (Rs.)
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <input value="${motorEngineerDto.currentProvision}"
                           placeholder="Current Provision" class="form-control form-control-sm  text-right"
                           id="currentProvision" name=".currentProvision" disabled>
                </div>
            </div>
        </fieldset>
        <fieldset class="border p-2 mt-2">
            <h6> Assessor Remarks</h6>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Settlement Methods
                    <span class="text-danger font-weight-bold"> *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary">
                        ${DbRecordCommonFunctionBean.getValue("claim_settlement_method", "value", "settlement_id", motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.settlementMethod)}
                </span>
                    <select name="garageInspectionDetailsDto.settlementMethod"
                            class="form-control form-control-sm settlementMethod"
                            id="settlementMethod">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_settlement_method WHERE settlement_id NOT IN (6)", "settlement_id", "value")}
                    </select>
                </div>
                <script>document.getElementById("settlementMethod").value = "${motorEngineerDto.garageInspectionDetailsDto.settlementMethod}";</script>
            </div>

            <script>
                if ('${motorEngineerDto.garageInspectionDetailsDto.settlementMethod}' != '5' && ('${motorEngineerDto.inspectionDetailsDto.recordStatus}' == '10' || '${motorEngineerDto.inspectionDetailsDto.recordStatus}' == '14')) {
                    $("select option[value*='5']").prop('disabled', true);
                }
            </script>

            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Offer Amount (Rs.) :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.offerAmount}</span>
                    <input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.offerAmount}"
                           placeholder="Offer Amount" class="form-control form-control-sm offeramount text-right" id=" "
                           name="garageInspectionDetailsDto.offerAmount">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Inspection Remarks :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.inspectionRemark}</span>
                    <textarea name="garageInspectionDetailsDto.inspectionRemark" class="form-control form-control-sm "
                              id=" ">${motorEngineerDto.garageInspectionDetailsDto.inspectionRemark}</textarea>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ARI and Salvage<span
                        class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.ariAndSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.garageInspectionDetailsDto.ariAndSalvage eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                    name="garageInspectionDetailsDto.ariAndSalvage" type="radio" value="Yes"
                                    class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.garageInspectionDetailsDto.ariAndSalvage eq 'No' and not isEmptyValue ? 'checked' : ''}
                                    name="garageInspectionDetailsDto.ariAndSalvage" type="radio" value="No"
                                    class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>
            <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
                <fieldset class="border p-2 mt-2 my-2">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Special Remarks :</label>
                        <div class="col-sm-8">
                <textarea name="garageInspectionDetailsDto.specialRemark" class="form-control form-control-sm "
                          id="specialRemark">${motorEngineerDto.garageInspectionDetailsDto.specialRemark}</textarea>
                        </div>
                    </div>
                </fieldset>
            </c:if>

            <div class="mt-6">

                <c:if test="${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && (motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 14))  || (G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser && motorEngineerDto.inspectionDetailsDto.recordStatus ne 80) || G_USER.accessUserType eq 1 || G_USER.accessUserType eq 23 || G_USER.accessUserType eq 24}">

                    <div style="display: none" class="float-right" id="forwardDiv">
                        <button type="submit" name="cmdReject" id="forward"
                                value="forward"
                                class="btn btn-primary"  ${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 80 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14) ? 'disabled' : ''}>
                            Forward
                        </button>
                    </div>

                    <div class="float-right">
                        <c:if test="${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && motorEngineerDto.inspectionDetailsDto.recordStatus eq 80)}">
                            <button id="btnReturn" value="return"
                                    class=" btn btn-danger">
                                Return
                            </button>
                        </c:if>
                        <button type="submit" name="cmdReject" id="cmdAuth"
                                value="Authorize"
                                class="btn btn-primary"  ${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14) ? 'disabled' : ''}>
                            Authorize
                        </button>
                    </div>
                </c:if>
            </div>

        </fieldset>
    </c:if>
    <fieldset class="border p-2 mt-2 my-2">
        <jsp:include page="../motorengineer/assessorPayment.jsp"/>
    </fieldset>
</div>

<script type="text/javascript">

    $(document).ready(function () {
        $("#paidAdvanceAmount").prop('disabled', true);
        $("#balanceAdvanceAmount").prop('disabled', true);
        $("#totalApprovedAdvanceAmount").prop('disabled', true);
        $("#currentProvision").prop('disabled', true);

        if (${motorEngineerDto.inspectionDetailsDto.recordStatus == 80 && G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser}) {
            $('#advanceAmount').val('${motorEngineerDto.garageInspectionDetailsDto.advancedAmount}');
            if (${motorEngineerDto.garageInspectionDetailsDto.advanceChange == 'ADD'}) {
                document.getElementById('advanceAdd').checked = true;
                setBalanceAdvanceAmount();
            } else {
                document.getElementById('advanceReduce').checked = true;
                setBalanceAdvanceAmount();
            }
        }
        calculateBoldTyerPenaltyPerc();
    });

    $("#advanceAmount").keyup(function () {
        setBalanceAdvanceAmount();
    });

    $("#advanceAmount").change(function () {
        setBalanceAdvanceAmount();
    });
    $("#acr").keyup(function () {
        setBalanceAdvanceAmount();
    });

    function setBalanceAdvanceAmount() {
        var balanceAdvanceAmount = '${motorEngineerDto.balanceAdvanceAmount}';
        var advanceAmountChange = $('#advanceAmount').val();
        var calType = 'ADD';
        if (document.getElementById('advanceAdd').checked) {
            calType = 'ADD';
        } else {
            calType = "REDUCE"
        }

        var dataObj = {
            balanceAdvanceAmount: balanceAdvanceAmount,
            advanceAmountChange: advanceAmountChange,
            calType: calType
        };

        var URL = "${pageContext.request.contextPath}/MotorEngineerController/setBalanceAdvanceAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj == "ERROR") {
                    notify('Balance Advance Amount Cannot be less than zero', "danger");
                    $('#advanceAmount').val('0.00');
                    $('#balanceAdvanceAmount').val(balanceAdvanceAmount)
                } else {
                    $('#balanceAdvanceAmount').val(obj);
                }
            }
        });
    }


    function calculateBaldTyrePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            boldPercent: $("#boldPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=BaldTyrePenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#boldTyrePenaltyAmount").val(result);
                calculatePayableAmount();
            }
        });
    }

    function calculateUnderInsurancePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            underPenaltyPercent: $("#underPenaltyPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=UnderPenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underInsurancePenaltyAmount").val(result.trim());
                calculatePayableAmount();
            }
        });
    }

    function calculatePayableAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            excess: $("#excess").val(),
            underPenaltyAmount: $("#underInsurancePenaltyAmount").val(),
            boldTirePenaltyAmount: $("#boldTyrePenaltyAmount").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=PayableAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#payableAmount").val(result);
            }
        });
    }

    function calculateUnderInsPenaltyPerc() {
        var dataObj = {
            pav: $("#pav").val(),
            sumInsured: $("#sumInsuredVal").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateUnderInsPenaltyPerc";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underPenaltyPercent").val(parseFloat(result.trim()) < 0 ? '0.00' : result.trim());
                calculateUnderInsurancePenaltyAmount();
            }
        });
    }

    $("#pav").change(function () {
        calculateUnderInsPenaltyPerc();
    });

    $("#acr").change(function () {
        calculateBoldTyerPenaltyPerc();
        calculateUnderInsPenaltyPerc();

    });

    $("#boldTyrePenaltyAmount,#underInsurancePenaltyAmount,#excess").change(function () {
        calculatePayableAmount();
    });

    function calculateBoldTyerPenaltyPerc() {
        var wheelCount = 0;
        var boldWheelCount = 0;
        var boldTyrePercentage = "0.00";
        for (var i = 0; i < 7; i++) {
            var val = $("#cot_0_" + i).val();
            if ("N/A" != val) {
                wheelCount++;
            }
            if ("Bald" == val) {
                boldWheelCount++;
            }
        }
        switch (wheelCount) {
            case 2:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "20.00";
                        break;
                    case 2:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 3:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "10.00";
                        break;
                    case 2:
                        boldTyrePercentage = "30.00";
                        break;
                    case 3:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 4:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "20.00";
                        break;
                    case 3:
                        boldTyrePercentage = "30.00";
                        break;
                    case 4:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 6:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "10.00";
                        break;
                    case 3:
                        boldTyrePercentage = "20.00";
                        break;
                    case 4:
                        boldTyrePercentage = "30.00";
                        break;
                    case 5:
                        boldTyrePercentage = "40.00";
                        break;

                }
                break;
        }
        var previousPercentage = parseFloat(${null == motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.boldTyrePenaltyAmount ||
        motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.boldTyrePenaltyAmount eq '' ? '0.00' :
        motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.boldTyrePenaltyAmount});
        if (parseFloat(boldTyrePercentage) < 0) {
            $("#boldPercent").val(previousPercentage < 0 ? "0.00" : previousPercentage);
        } else {
            $("#boldPercent").val(boldTyrePercentage);
        }

        if(${motorEngineerDto.inspectionDetailsDto.recordStatus eq 8}){
            calculateBaldTyrePenaltyAmount();
        }

    }

    $(".cot_0").change(function () {
        calculateBoldTyerPenaltyPerc();
    });

    function setAddOrReduce(){
        var advanceChange = '${motorEngineerDto.garageInspectionDetailsDto.advanceChange}';
        if(advanceChange == "ADD"){
            document.getElementById('advanceAdd').checked = true;
            setBalanceAdvanceAmount();
        }else{
            document.getElementById('advanceReduce').checked = true;
            setBalanceAdvanceAmount();
        }
    }
    $(document).ready(function(){
        setAddOrReduce();
        let isFwd = ${motorEngineerDto.inspectionDetailsDto.recordStatus eq 80};
        if (isFwd) {
            $('#advanceAmount').val('${motorEngineerDto.garageInspectionDetailsDto.advancedAmount}');
            setBalanceAdvanceAmount();
        }
    });
</script>
