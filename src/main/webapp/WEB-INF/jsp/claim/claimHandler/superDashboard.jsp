<%@ page import="com.misyn.mcms.utility.AppConstant" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<jsp:useBean id="UtilityBean" class="com.misyn.mcms.utility.Utility"
             scope="session"/>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8">
    <title>Super Dashboard - ${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/superdashboard/superdashboard-form-validations.js?v4"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/superdashboard/enhanced-document-management.js?v1"></script>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload-ui.css">
    <!-- jQuery File Upload dependencies in correct order -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload-ui.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-scrollto.js"></script>
    <script type="text/javascript">
        var documentUploadIds1 = [];
        var documentUploadIds2 = [];
        var documentUploadIds = [];
    </script>

    <c:set var="claimsDto" value="${claimSuperDashboardDto.claimHandlerDto.claimsDto}" scope="request" />
    <c:set var="claimHandlerDto" value="${claimSuperDashboardDto.claimHandlerDto}" scope="request" />


</head>
<body class="scroll calcsheetjsp">
<div class="container-fluid">
    <div class="row header-bg bg-dark">
        <div class="col-sm-12 py-2 " >
                <h5 class="hide-sm m-0">Super Dashboard</h5>
                <div class="clearfix"></div>
        </div>

<%--        <div class="col-sm-12 py-2" style="--%>
<%--            display: flex;--%>
<%--            flex-direction: row;--%>
<%--            justify-content: space-around;--%>
<%--            align-items: center;--%>
<%--            background: #a7d1d6;--%>
<%--        ">--%>
<%--            <h6 style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                ${(claimSuperDashboardDto.claimHandlerDto.claimsDto.policyDto.product eq "") || (null eq claimSuperDashboardDto.claimHandlerDto.claimsDto.policyDto.product) ? "N/A" : claimSuperDashboardDto.claimHandlerDto.claimsDto.policyDto.product }--%>
<%--            </h6>--%>
<%--            <h6 id="service-factor-header" style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                Service Factors--%>
<%--            </h6>--%>
<%--        </div>--%>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <c:choose>
                <c:when test="${claimSuperDashboardDto.claimHandlerDto.processingStage eq 1}">
                    <c:set value="width: 10%;" var="cssClass"></c:set>
                </c:when>
                <c:when test="${claimSuperDashboardDto.claimHandlerDto.processingStage eq 2}">
                    <c:set value="width: 30%;" var="cssClass"></c:set>
                </c:when>
                <c:when test="${claimSuperDashboardDto.claimHandlerDto.processingStage eq 3}">
                    <c:set value="width: 50%;" var="cssClass"></c:set>
                </c:when>
                <c:when test="${claimSuperDashboardDto.claimHandlerDto.processingStage eq 4}">
                    <c:set value="width: 70%;" var="cssClass"></c:set>
                </c:when>
                <c:when test="${claimSuperDashboardDto.claimHandlerDto.processingStage eq 5}">
                    <c:set value="width: 100%;" var="cssClass"></c:set>
                </c:when>
            </c:choose>
            <div id="mainClaimStampContainer" class="stamp-container"></div>
            <div id="claimStampContainer" class="stamp-container">
                <c:if test="${claimSuperDashboardDto.claimHandlerDto.claimsDto.policyDto.categoryDescription eq 'VIP'}">
                    <img src="${pageContext.request.contextPath}/resources/stamps/vip.png"
                         class="stamp-container-vip"
                    >
                </c:if>
            </div>
            <div class="f1-steps">
                <div class="f1-progress">
                    <div class="f1-progress-line" data-now-value="10" data-number-of-steps="5"
                         style="${cssClass}"></div>
                </div>
                <div class="f1-step ${claimSuperDashboardDto.claimHandlerDto.processingStage>=1?"active":""}">
                    <div class="f1-step-icon ${claimSuperDashboardDto.claimHandlerDto.processingStage>=1?"active":""}">
                        1
                    </div>
                    <p>Call Center</p>
                </div>
                <div class="f1-step ${claimSuperDashboardDto.claimHandlerDto.processingStage>=2?"active":""}">
                    <div class="f1-step-icon ${claimSuperDashboardDto.claimHandlerDto.processingStage>=2?"active":""}">
                        2
                    </div>
                    <p>Assessor Coordinator</p>
                </div>
                <div class="f1-step ${claimSuperDashboardDto.claimHandlerDto.processingStage>=3?"active":""}">
                    <div class="f1-step-icon ${claimSuperDashboardDto.claimHandlerDto.processingStage>=3?"active":""}">
                        3
                    </div>
                    <p>Assessor</p>
                </div>
                <div class="f1-step ${claimSuperDashboardDto.claimHandlerDto.processingStage>=4?"active":""}">
                    <div class="f1-step-icon ${claimSuperDashboardDto.claimHandlerDto.processingStage>=4?"active":""}">
                        4
                    </div>
                    <p>Motor Engineer</p>
                </div>
                <div class="f1-step ${claimSuperDashboardDto.claimHandlerDto.processingStage>=5?"active":""}">
                    <div class="f1-step-icon ${claimSuperDashboardDto.claimHandlerDto.processingStage>=5?"active":""}">
                        5
                    </div>
                    <p>Claim Handler</p>
                </div>
            </div>
        </div>
    </div>
    <div class="bd-example bd-example-tabs" id="tabs">
        <ul class="nav  nav-tabs mt-3" role="tablist">
            <li class="nav-item item1"><a href="#tabs-1" class="font-weight-bold nav-link active" data-toggle="tab"><h6>Claim Details</h6>
            </a></li>

            <li class="nav-item item2"><a href="#tabs-2" class="font-weight-bold nav-link" id="tabBranch" data-toggle="tab"><h6>
                Branch</h6></a></li>
            <li class="nav-item item3"><a href="#tabs-3" class=" font-weight-bold nav-link" data-toggle="tab"><h6>Document Upload</h6>
            </a></li>

        </ul>
        <div class="tab-content">
            <div class="tab-pane fade active show" role="tabpanel" id="tabs-1">
                <fieldset class="border p-2 ">

                    <input type="hidden" id="deleteDoc" name="deleteDoc"/>
                    <input type="hidden" id="documentTypeId" name="documentTypeId"/>
                    <h6 class="float-left"> Driver & Reporter Details</h6>
                    <c:if test="${pendingAri == 'Y'}">
                        <span class="badge  badge-warning text-uppercase ml-3 px-3">ARI Request Pending&nbsp;&nbsp;
                            <i class="fa fa-check"></i>
                        </span>
                    </c:if>
                    <hr class="my-1">
                    <fieldset class="p-2">
                        <div class="row">
                            <div class="col-md-6 col-lg-3 offset-lg-3">
                                <ul class="list-group">
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Claim File Assign To : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.assignUserId}</span>
                                        <c:if test="${isLeaveUser}">
                                            <div class="clearfix"></div>
                                            <span></span>
                                            <span class="label_Value  float-right" style="color: red">(Leave)</span>
                                        </c:if>
                                        <div class="clearfix"></div>
                                        <span class="float-left">Contact No : </span>
                                        <span class="label_Value  float-right">&nbsp;${claimSuperDashboardDto.claimHandlerDto.claimHandlerMobileNo}</span>
                                    </li>
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Liability Assign To : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.liabilityAprvAssignUser}</span>
                                    </li>
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Vehicle Number : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.claimsDto.policyDto.vehicleNumber}</span>
                                    </li>
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Insured's Name : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.claimsDto.policyDto.custName}</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6  col-lg-3">
                                <ul class="list-group">
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Claim No : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.claimNo}</span>
                                    </li>
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Policy Number : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.claimsDto.policyDto.policyNumber}</span>
                                    </li>
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Accident Date : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.claimsDto.accidDate}</span>
                                    </li>
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Engineer Approved ACR : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.aprvTotAcrAmount}</span>
                                    </li>
                                    <li class="list-group-item p-2">
                                        <span class="float-left">Azentio Claim No : </span>
                                        <span class="label_Value  float-right">${claimSuperDashboardDto.claimHandlerDto.claimsDto.isfClaimNo}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </fieldset>

                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Claim Process Flow Details</h5>
                        </div>

                        <div class="card-body">
                            <%--<c:forEach var="claimProcessFlowDto"--%>
                            <%--items="${claimSuperDashboardDto.claimProcessFlowDtos}">--%>
                            <%--<c:set var="calculationProcessFlowDtoList"--%>
                            <%--value="${claimCalculationSheetDto.calculationProcessFlowDtos}"/>--%>

                            <div class="row">
                                <div class="col-md-12 border-left border-right">
                                    <div class="row">
                                        <div class="col text-left align-self-center">
                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                <thead>
                                                <tr>
                                                    <th>Task Description</th>
                                                    <th>Performed By</th>
                                                    <th>Assigned To</th>
                                                    <th>Date/Time</th>
                                                    <th width="150px">TAT</th>
                                                </tr>
                                                </thead>
                                                <tbody>

                                                <tr class="text-left">
                                                    <td colspan="1"><i
                                                            class="fa fa-circle-o text-success pl-5 ml-1"></i>
                                                    </td>
                                                </tr>
                                                <tr class="text-left">
                                                    <td colspan="1"><i
                                                            class="fa fa-arrow-down text-success pl-5 ml-1"></i>
                                                    </td>
                                                </tr>
                                                <c:forEach var="claimProcessFlowDto"
                                                           items="${claimSuperDashboardDto.claimProcessFlowDtos}">
                                                    <tr>
                                                        <td>${claimProcessFlowDto.task}</td>
                                                        <td>${claimProcessFlowDto.inpUserId}</td>
                                                        <td>${claimProcessFlowDto.assignUserId}</td>
                                                        <td>${claimProcessFlowDto.taskCompletedDateTime eq '1980-01-01 00:00:00' ? '':claimProcessFlowDto.taskCompletedDateTime}</td>
                                                        <td class="align-middle text-center">
                                                            <label class="m-0 badge  badge-info px-3">${claimProcessFlowDto.tat}</label>
                                                        </td>
                                                    </tr>
                                                    <tr class="text-left">
                                                        <td colspan="1"><i
                                                                class="fa fa-arrow-down text-success pl-5 ml-1"></i>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                                <c:choose>
                                                    <c:when test="${claimCalculationSheetDto.status == 67}">
                                                        <tr class="text-left">
                                                            <td colspan="1"><i
                                                                    class="fa fa-dot-circle-o text-success pl-5 ml-1"></i>
                                                            </td>
                                                        </tr>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <tr class="text-left">
                                                            <td colspan="1"><i
                                                                    class="fa fa-dot-circle-o text-warning pl-5 ml-1"></i>
                                                            </td>
                                                        </tr>
                                                    </c:otherwise>
                                                </c:choose>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2 align-self-center">
                                    <div class="row">
                                        <div class="col text-center align-self-center">

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <%--</c:forEach>--%>
                        </div>
                    </div>


                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Intimation </h5>
                            <span class="badge  badge-danger mt-1 ml-2 px-3">ISFS <i
                                    class="fa fa-times"></i></span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 text-center border-right">
                        <span class="badge align-self-center  rounded-circle badge-primary text-uppercase"
                              style="height: 40px;width: 40px;padding: 14px;">I</span>
                                    <%--<span class="text-muted  mt-3"></span>--%>
                                    <div class="row">
                                        <div class="col align-self-center">
                                            <span class="text-muted d-block">Intimation Type</span>
                                            <c:if test="${claimSuperDashboardDto.claimHandlerDto.claimsDto.intimationType==2}">
                                                <span class="badge   badge-danger  text-uppercase px-3">Late Intimation</span>
                                            </c:if>
                                            <c:if test="${claimSuperDashboardDto.claimHandlerDto.claimsDto.intimationType==1}">
                                                <span class="badge   badge-primary  text-uppercase px-3">On-site</span>
                                            </c:if>
                                            <span class="badge  badge-success text-uppercase mt-2 px-3">${claimSuperDashboardDto.claimHandlerDto.claimsDto.claimStatusDesc}<i
                                                    class="fa fa-check"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 border-right">
                                    <div class="row">
                                        <h5 class="col-12  card-title w-100">Basic Details</h5>
                                        <div class="col text-center align-self-center border-right">
                                            <span class="text-muted d-block mb-1">1 <sup>st</sup> Statement Required </span>
                                            <c:if test="${claimSuperDashboardDto.claimHandlerDto.claimsDto.isFirstStatementReq=='Y'}">
                                    <span class="badge  badge-light text-uppercase px-3"><i
                                            class="fa fa-check text-success font-weight-bold"></i></span>
                                            </c:if>
                                            <c:if test="${claimSuperDashboardDto.claimHandlerDto.claimsDto.isFirstStatementReq=='N'}">
                                    <span class="badge  badge-light text-uppercase px-3"><i
                                            class="fa fa-times text-danger font-weight-bold"></i></span>
                                            </c:if>
                                        </div>
                                        <div class="col text-center align-self-center border-right">
                                            <span class="text-muted d-block mb-1">Cause of loss</span>
                                            <span class="badge  badge-warning text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.causeOfLoss}</span>
                                        </div>
                                        <div class="col text-center align-self-center">
                                            <%--<span class="text-muted d-block mb-1">NCB Details</span>--%>
                                            <span class="text-muted d-block mb-1">Follow Up Call & Promotions Done</span>
                                            <c:if test="${claimSuperDashboardDto.claimHandlerDto.claimsDto.isFollowupCallDone =='Y'}">
                                    <span class="badge  badge-light text-uppercase px-3"><i
                                            class="fa fa-check text-success font-weight-bold"></i></span>
                                            </c:if>
                                            <c:if test="${claimSuperDashboardDto.claimHandlerDto.claimsDto.isFollowupCallDone =='N'}">
                                    <span class="badge  badge-light text-uppercase px-3"><i
                                            class="fa fa-times text-danger font-weight-bold"></i></span>
                                            </c:if>
                                            <%--<span class="text-uppercase px-3 text-success w-100">NCB Promoted <i class="fa fa-check"></i></span>--%>
                                            <%--<span class="text-uppercase px-3 text-primary w-100">Customer Agreed  <i class="fa fa-check"></i></span>--%>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="row">
                                        <h5 class="col-12 card-title w-100">ASSIGNMENT USER</h5>
                                        <div class="col text-center align-self-center border-right">
                                            <span class="text-muted d-block mb-1">Call Center Agent</span>
                                            <span class="badge  badge-primary text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.claimsDto.callUser}</span>
                                        </div>
                                        <div class="col text-center align-self-center">
                                            <span class="text-muted d-block mb-1">Create Date & Time</span>
                                            <span class="badge  badge-primary text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.claimsDto.dateOfReport} ${claimSuperDashboardDto.claimHandlerDto.claimsDto.timeOfReport}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header mb-4">
                            <h5 class="float-left m-0 text-uppercase">Assessor Appointment </h5>

                        </div>
                        <c:forEach var="assessorAllocationDto"
                                   items="${claimSuperDashboardDto.assessorAllocationDtoList}">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2 text-center border-right">
                                            <%--<span class="badge align-self-center  rounded-circle badge-primary text-uppercase  mt-3"--%>
                                            <%--style="height: 40px;width: 40px;padding: 14px;">${assessorAllocationDto.assessorDto.name}</span>--%>

                                        <div class="font-bg log-left text-primary"
                                             style="width: 50px; height:50px; overflow: hidden; margin-top: 25px; margin-top: -48px; position: absolute;left: 40%;">
                                            <h2 class="name">${assessorAllocationDto.assessorDto.name}</h2>
                                        </div>

                                        <div class="row">
                                            <div class="col">
                                                <span class="text-muted d-block  mt-2">${assessorAllocationDto.assessorDto.name}</span>
                                                <span class="badge   badge-secondary  text-uppercase px-3 mt-1">${assessorAllocationDto.assessorDto.code}</span>
                                                <span class="badge   badge-secondary  text-uppercase px-3 mt-1">${assessorAllocationDto.jobId}</span>
                                                <span class="badge   badge-info  text-uppercase px-3 mt-1"
                                                      title="Reporting RTE- Regional Technical Excecutive [${assessorAllocationDto.assessorDto.reportingCode}]">${assessorAllocationDto.assessorDto.reportingCode}</span>
                                                <span class="badge   badge-info  text-uppercase px-3 mt-1"
                                                      title="Reporting RTE Contact Number [${assessorAllocationDto.assessorDto.rteMobileNo}]">${assessorAllocationDto.assessorDto.rteMobileNo}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="row">
                                            <h5 class="col-12 card-title w-100">Details</h5>
                                            <div class="col text-center align-self-center border-right">
                                                <span class="text-muted d-block mb-1">Assigned Date & Time </span>
                                                <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-muted">${assessorAllocationDto.assignDatetime  eq '1980-01-01 00:00:00' ? '' :assessorAllocationDto.assignDatetime}</span>
                                            </div>
                                            <div class="col text-center align-self-center border-right">
                                                <span class="text-muted d-block mb-1">Inspection Type </span>
                                                <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-success">${assessorAllocationDto.inspectionDto.inspectionValue}</span>
                                            </div>
                                            <c:if test="${assessorAllocationDto.inspectionDto.inspectionId ne 8 && assessorAllocationDto.inspectionDto.inspectionId ne 11}">
                                                <div class="col text-center align-self-center text-center">
                                                    <span class="text-muted d-block mb-1">SMS Sent </span>
                                                    <span class="text-muted d-block mb-1"><i
                                                            class="fa fa-envelope fa-2x"></i></span>
                                                    <span class="badge  badge-info text-uppercase px-3">${assessorAllocationDto.assessorDto.assessorContactNo}</span>
                                                </div>
                                            </c:if>
                                            <c:if test="${assessorAllocationDto.inspectionDto.inspectionId ne 8 && assessorAllocationDto.inspectionDto.inspectionId ne 11}">
                                                <div class="col text-center align-self-center text-center  border-right">
                                                    <span class="text-muted d-block mb-1">Job Complete Date </span>
                                                    <span class="badge  badge-info text-uppercase px-3">${assessorAllocationDto.jobFinishedDatetime  eq '1980-01-01 00:00:00' ? '' :assessorAllocationDto.jobFinishedDatetime}</span>
                                                </div>
                                            </c:if>
                                            <div class="col text-center align-self-center">
                                                <span class="text-muted d-block  mb-1">Assessor Coordinator</span>
                                                <span class="badge  badge-light text-uppercase px-3 font-weight-bold">${assessorAllocationDto.inputUserId}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                            </div>
                        </c:forEach>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Assessor's
                                Assessment ${motorEngineerDetailsList.inspectionDetailsDto.firstStatementReq}</h5>
                        </div>
                        <div class="card-body">
                            <c:forEach var="motorEngineerDetailsList"
                                       items="${claimSuperDashboardDto.motorEngineerDetailsDtoList}">
                                <div class="row">
                                    <c:if test="${motorEngineerDetailsList.inspectionDto.inspectionId ne 8}">
                                        <div class="col-md-12 text-center border-right">
                                            <div class="row">
                                                <div class="col text-center align-self-center border-right">
                                                    <span class="text-muted d-block mb-1">Report Status </span>
                                                    <span class="badge  badge-success  text-uppercase px-3 ">${motorEngineerDetailsList.recordStatusDesc}</span>
                                                </div>
                                                <div class="col text-center align-self-center border-right">
                                                    <span class="text-muted d-block mb-1">Job No </span>
                                                    <span class="badge  badge-secondary  text-uppercase px-3">${motorEngineerDetailsList.assessorAllocationDto.jobId}</span>
                                                </div>
                                                <div class="col text-center align-self-center border-right">
                                                    <span class="text-muted d-block mb-1">Assigned Date & Time </span>
                                                    <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-muted">${motorEngineerDetailsList.inspectionDetailsDto.assessorAllocationDto.assignDatetime eq '1980-01-01 00:00:00' ? '' :motorEngineerDetailsList.inspectionDetailsDto.assessorAllocationDto.assignDatetime}</span>
                                                    <br><br>
                                                    <span class="text-muted d-block mb-1">Approved Date & Time </span>
                                                    <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-muted">${motorEngineerDetailsList.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime eq '1980-01-01 00:00:00' ? '' :motorEngineerDetailsList.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime}</span>
                                                </div>
                                                <div class="col text-center align-self-center">
                                                    <span class="text-muted d-block mb-1">Inspection Type </span>
                                                    <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-success">${motorEngineerDetailsList.inspectionDto.inspectionValue}
                                                    </span>
                                                    <span class="text-muted d-block mb-1">PR Requested </span>
                                                        <%--<c:if test="${motorEngineerDetailsList.inspectionDetailsDto.firstStatementReq=='Y'}">--%>
                                                    <span class="badge  badge-success text-uppercase mt-2 px-3"
                                                        ${motorEngineerDetailsList.inspectionDetailsDto.firstStatementReq eq 'Yes' ? '' : 'hidden'}><i
                                                            class="fa fa-check"></i></span>
                                                        <%--</c:if>--%>
                                                        <%--<c:if test="${motorEngineerDetailsList.inspectionDetailsDto.firstStatementReq=='N'}">--%>
                                                    <span class="badge  badge-danger text-uppercase mt-2 px-3"
                                                        ${motorEngineerDetailsList.inspectionDetailsDto.firstStatementReq eq 'No' ? '' : 'hidden'} ><i
                                                            class="fa fa-times"></i></span>
                                                        <%--</c:if>--%>
                                                </div>
                                                <div class="col text-center align-self-center">
                                                    <span class="text-muted d-block  mb-1">Assessor</span>
                                                    <span class="badge  badge-light text-uppercase px-3 font-weight-bold">${motorEngineerDetailsList.inspectionDetailsDto.inputUserId}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </c:if>
                                </div>
                                <hr>
                            </c:forEach>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Engineer Approval</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12 text-center ">
                                    <c:forEach var="motorEngineerDetails"
                                               items="${claimSuperDashboardDto.motorEngineerDetailsDtoList}">
                                        <div class="row">
                                            <div class="col border-right">
                                                <span class="text-muted d-block mb-1">Report Status </span>
                                                <span class="badge  badge-success text-uppercase px-3 ">${motorEngineerDetails.recordStatusDesc}</span>
                                            </div>
                                            <div class="col border-right">
                                                <span class="text-muted d-block mb-1">Job No </span>
                                                <span class="badge  badge-secondary text-uppercase px-3">${motorEngineerDetails.jobId}</span>
                                            </div>
                                            <div class="col border-right">
                                                <span class="text-muted d-block mb-1">Assigned Date & Time</span>
                                                <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-muted">${motorEngineerDetails.inspectionDetailsDto.assignRteDatetime eq '1980-01-01 00:00:00' ? '' :motorEngineerDetails.inspectionDetailsDto.assignRteDatetime}</span>
                                                <br><br>
                                                <c:if test="${motorEngineerDetails.isForwarded == 'Y'}">
                                                    <span class="text-muted d-block mb-1">Forwarded Date & Time</span>
                                                    <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-muted">${motorEngineerDetails.inspectionDetailsDto.approverAssignRteDateTime eq '1980-01-01 00:00:00' ? '' :motorEngineerDetails.inspectionDetailsDto.approverAssignRteDateTime}</span>
                                                    <br><br>
                                                </c:if>
                                                <c:if test="${motorEngineerDetails.inspectionDetailsDto.inspectionDetailsAuthStatus =='A'}">
                                                    <span class="text-muted d-block mb-1">Approved Date & Time</span>
                                                    <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-muted">${motorEngineerDetails.inspectionDetailsDto.inspectionDetailsAuthDatetime eq '1980-01-01 00:00:00' ? '' :motorEngineerDetails.inputDatetime}</span>
                                                    <br><br>
                                                    <span class="text-muted d-block mb-1">TAT</span>
                                                    <label class="m-0 badge  badge-info px-3">${motorEngineerDetails.engineerTat}</label>
                                                </c:if>
                                            </div>
                                            <div class="col border-right">
                                                <span class="text-muted d-block mb-1">Inspection Type </span>
                                                <span class="badge  badge-light text-uppercase px-3 w-100 font-weight-bold text-success">${motorEngineerDetails.inspectionDto.inspectionValue}
                                                </span>
                                                <span class="text-muted d-block mb-1">PR Requested </span>
                                                    <%--<c:if test="${motorEngineerDetailsList.inspectionDetailsDto.firstStatementReq=='Y'}">--%>
                                                <span class="badge  badge-success text-uppercase mt-2 px-3"
                                                    ${motorEngineerDetails.firstStatementReq eq 'Yes' ? '' : 'hidden'}><i
                                                        class="fa fa-check"></i></span>
                                                    <%--</c:if>--%>
                                                    <%--<c:if test="${motorEngineerDetailsList.inspectionDetailsDto.firstStatementReq=='N'}">--%>
                                                <span class="badge  badge-danger text-uppercase mt-2 px-3"
                                                    ${motorEngineerDetails.firstStatementReq eq 'No' ? '' : 'hidden'} ><i
                                                        class="fa fa-times"></i></span>
                                                    <%--</c:if>--%>
                                            </div>
                                            <c:choose>
                                                <c:when test="${motorEngineerDetails.isForwarded == 'Y'}">
                                                    <div class="col">
                                                        <span class="text-muted d-block mb-1">Forwarded Engineer</span>
                                                        <span class="badge  badge-light text-uppercase px-3 font-weight-bold">${motorEngineerDetails.inspectionDetailsDto.assignRteUser}</span>
                                                        <br><br>
                                                        <span class="text-muted d-block mb-1">Approve Assigned Engineer</span>
                                                        <span class="badge  badge-light text-uppercase px-3 font-weight-bold">${motorEngineerDetails.inspectionDetailsDto.approveAssignRteUser}</span>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="col">
                                                        <span class="text-muted d-block mb-1">Approve Assigned Engineer</span>
                                                        <span class="badge  badge-light text-uppercase px-3 font-weight-bold">${motorEngineerDetails.inspectionDetailsDto.assignRteUser}</span>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                        <hr>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Claims Status </h5>
                            <%--<span class="badge  badge-success text-uppercase mt-1 ml-2 px-3">Normal</span>--%>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 align-self-center border-right">
                                    <p class="text-uppercase d-block  mb-0">Loss Type</p>
                                    <span class="badge badge-warning text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.lossTypeDecs}</span>
                                    <p class="text-uppercase d-block mt-2 mb-0">Current Claim Status</p>

                                    <c:choose>
                                        <c:when test="${claimSuperDashboardDto.claimHandlerDto.claimStatus==17}">
                                            <span class="badge badge-danger text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.claimStatusPara}</span><br>
                                        </c:when>
                                        <c:when test="${claimSuperDashboardDto.claimHandlerDto.claimStatus==43 || claimSuperDashboardDto.claimHandlerDto.claimStatus==46
                                            || claimSuperDashboardDto.claimHandlerDto.claimStatus==48 || claimSuperDashboardDto.claimHandlerDto.claimStatus==52 || claimSuperDashboardDto.claimHandlerDto.claimStatus==53 || claimSuperDashboardDto.claimHandlerDto.claimStatus==54 || claimSuperDashboardDto.claimHandlerDto.claimStatus==55
                                             || claimSuperDashboardDto.claimHandlerDto.claimStatus==56 || claimSuperDashboardDto.claimHandlerDto.claimStatus==68}">
                                            <span class="badge badge-danger text-uppercase px-3">Pending Decision</span><br>
                                        </c:when>
                                        <c:otherwise>
                                            <span class="badge badge-success text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.claimStatusPara}</span><br>
                                        </c:otherwise>
                                    </c:choose>

                                    <c:choose>
                                        <c:when test="${claimSuperDashboardDto.claimHandlerDto.initLiabilityAprvStatus eq 'A'}">
                                            <p class="d-block mt-2 text-uppercase mb-0">Initial Liability
                                                Status </p>
                                            <span class="badge badge-success text-uppercase px-3">Initial Liability Checked
                                        </span><br>
                                        </c:when>
                                        <c:otherwise>
                                            <p class="d-block mt-2 text-uppercase mb-0">Initial Liability
                                                Status </p>
                                            <span class="badge badge-danger text-uppercase px-3">Initial Liability Pending

                                        </span><br>
                                        </c:otherwise>
                                    </c:choose>

                                    <c:choose>
                                        <c:when test="${claimSuperDashboardDto.claimHandlerDto.liabilityAprvStatus eq 'A'}">
                                            <p class="d-block mt-2 text-uppercase mb-0"> Liability Status </p>
                                            <span class="badge badge-success text-uppercase px-3">Liability Approved
                                        </span><br>
                                        </c:when>
                                        <c:otherwise>
                                            <p class="d-block mt-2 text-uppercase mb-0"> Liability Status </p>
                                            <span class="badge badge-danger text-uppercase px-3"> Liability Pending

                                        </span><br>
                                        </c:otherwise>
                                    </c:choose>

                                    <c:if test="${claimSuperDashboardDto.claimHandlerDto.isAllDocUpload eq 'Y'}">
                                        <p class="d-block mt-2 text-uppercase mb-0">Document Upload Status</p>
                                        <span class="badge badge-success text-uppercase px-3">Document Uploaded</span><br>
                                    </c:if>
                                    <c:if test="${claimSuperDashboardDto.claimHandlerDto.isAllDocUpload ne 'Y'}">
                                        <p class="d-block mt-2 text-uppercase mb-0">Document Upload Status</p>
                                        <span class="badge badge-danger text-uppercase px-3">Document Upload Pending</span><br>
                                    </c:if>
                                    <c:if test="${claimSuperDashboardDto.claimHandlerDto.isCheckAllMndDocs eq 'Y'}">
                                        <p class="d-block mt-2 text-uppercase mb-0">Document Verify Status </p>
                                        <span class="badge badge-success text-uppercase px-3">Checked</span>
                                    </c:if>
                                    <c:if test="${claimSuperDashboardDto.claimHandlerDto.isCheckAllMndDocs ne 'Y'}">
                                        <p class="d-block mt-2 text-uppercase mb-0">Document Verify Status</p>
                                        <span class="badge badge-danger text-uppercase px-3">Pending</span><br>
                                    </c:if>


                                    <c:if test="${claimSuperDashboardDto.claimHandlerDto.closeStatus eq 'CLOSE'}">
                                        <h6 class="d-block mt-2 text-uppercase mb-0">Claim Finalize Status </h6>
                                        <c:if test="${bulkCloseDto.id ne 0}">
                                            <c:if test="${bulkCloseDto.days eq 180}">
                                                <span class="badge badge-success text-uppercase px-3"> BULK CLOSE 180 DAYS</span><br>
                                            </c:if>
                                            <c:if test="${bulkCloseDto.days eq 100}">
                                                <span class="badge badge-success text-uppercase px-3"> BULK CLOSE 100 DAYS</span><br>
                                            </c:if>
                                            <c:if test="${bulkCloseDto.days eq 30}">
                                                <span class="badge badge-success text-uppercase px-3"> BULK CLOSE 30 DAYS</span><br>
                                            </c:if>
                                            <c:if test="${bulkCloseDto.days eq 0}">
                                                <span class="badge badge-success text-uppercase px-3"> BULK CLOSE</span><br>
                                            </c:if>
                                        </c:if>
                                        <c:if test="${bulkCloseDto.id eq 0}">
                                            <span class="badge badge-success text-uppercase px-3"> CLOSE</span><br>
                                        </c:if>
                                    </c:if>
                                    <c:if test="${claimSuperDashboardDto.claimHandlerDto.closeStatus ne 'CLOSE'}">
                                        <p class="d-block mt-2 text-uppercase mb-0"> Finalize Status </p>
                                        <span class="badge badge-danger text-uppercase px-3"> Pending
                                                 </span><br>
                                    </c:if>
                                    </span>
                                </div>

                                <div class="col-md-9 align-self-center text-center border-right">
                                    <div class="row">
                                        <div class="col text-center align-self-center border-right">
                                            <span class="text-muted d-block mb-1">Initial Liability Approved User </span>
                                            <span class="badge  badge-warning text-uppercase px-3"> ${claimSuperDashboardDto.claimHandlerDto.initLiabilityAprvUserId}</span>
                                            <div class="w-100"></div>
                                            <span class="text-muted d-block mb-1">Date </span>
                                            <span class="badge  badge-primary text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.initLiabilityAprvDateTime eq '1980-01-01 00:00:00' ? '' :UtilityBean.getCustomDateFormat(claimSuperDashboardDto.claimHandlerDto.initLiabilityAprvDateTime,'yyyy-MM-dd HH:mm:ss','yyyy-MM-dd HH:mm:ss')}</span>


                                            <span class="text-muted d-block mb-1 mt-3">Liability Approved User </span>
                                            <span class="badge badge-warning text-uppercase px-3"> ${claimSuperDashboardDto.claimHandlerDto.liabilityAprvUser}</span>
                                            <div class="w-100"></div>
                                            <span class="text-muted d-block mb-1">Date </span>
                                            <span class="badge  badge-primary text-uppercase px-3">${claimSuperDashboardDto.claimHandlerDto.liabilityAprvDateTime eq '1980-01-01 00:00:00' ? '' :UtilityBean.getCustomDateFormat(claimSuperDashboardDto.claimHandlerDto.liabilityAprvDateTime,'yyyy-MM-dd HH:mm:ss','yyyy-MM-dd HH:mm:ss')}</span>
                                        </div>
                                        <div class="col-8 text-center align-self-center">
                                            <div id="accordion">
                                                <div class="card">
                                                    <div class="card-header p-0" id="headingOne">
                                                        <h5 class="mb-0">
                                                            <button type="button" onclick="documentUpload()"
                                                                    class="btn btn-link" data-toggle="collapse"
                                                                    data-target="#collapseOne"
                                                                    aria-expanded="true"
                                                                    aria-controls="collapseOne">
                                                                Documents
                                                            </button>
                                                        </h5>
                                                    </div>
                                                    <div id="collapseOne" class="collapse"
                                                         aria-labelledby="headingOne"
                                                         data-parent="#accordion">
                                                        <div class="card-body p-2">
                                                            <div id="docUpload">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card">
                                                    <div class="card-header p-0" id="headingTwo">
                                                        <h5 class="mb-0">
                                                            <button onclick="reminderLetterDiv()" type="button"
                                                                    class="btn btn-link collapsed"
                                                                    data-toggle="collapse"
                                                                    data-target="#collapseTwo"
                                                                    aria-expanded="false"
                                                                    aria-controls="collapseTwo">
                                                                Reminder Letter
                                                            </button>
                                                        </h5>
                                                    </div>
                                                    <div id="collapseTwo" class="collapse"
                                                         aria-labelledby="headingTwo"
                                                         data-parent="#accordion">
                                                        <div class="card-body p-2">
                                                            <div id="reminderLetterDiv"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <%--Acknowledgement letter--%>
                                                <div class="card">
                                                    <div class="card-header p-0" id="headingThree">
                                                        <h5 class="mb-0">
                                                            <button onclick="acknowledgementLetter()" type="button"
                                                                    class="btn btn-link collapsed"
                                                                    data-toggle="collapse"
                                                                    data-target="#collapseThree"
                                                                    aria-expanded="false"
                                                                    aria-controls="collapseThree">
                                                                Acknowledgement Letter
                                                            </button>
                                                        </h5>
                                                    </div>
                                                    <div id="collapseThree" class="collapse"
                                                         aria-labelledby="headingThree"
                                                         data-parent="#accordion">
                                                        <div class="card-body p-2">
                                                            <div id="acknowledgementLetterDiv"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Payment Details</h5>
                        </div>

                        <div class="card-body">
                            <c:forEach var="claimCalculationSheetDto"
                                       items="${claimSuperDashboardDto.claimCalculationSheetMainDtoList}">
                                <c:set var="calculationProcessFlowDtoList"
                                       value="${claimCalculationSheetDto.calculationProcessFlowDtos}"/>

                                <div class="row">
                                    <div class="col-md-3 align-self-center">
                                        <div id="claimStampContainer" class="stamp-container imagealignment1">
                                            <c:choose>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '1' and claimCalculationSheetDto.status eq '67'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_full_final_issued.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '1' and claimCalculationSheetDto.status ne '67'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_full_final_processing.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '2' and claimCalculationSheetDto.status eq '67'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_advance_issued.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '2' and claimCalculationSheetDto.status ne '67'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_advance_processing.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '3' and claimSuperDashboardDto.claimHandlerDto.isGenarateSupplyOrder eq 'Y'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_do_issued.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '3' and (claimSuperDashboardDto.claimHandlerDto.isGenarateSupplyOrder eq 'N' or empty claimCalculationSheetDto.claimHandlerDto.isGenarateSupplyOrder)}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_do_processing.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '4' and claimCalculationSheetDto.status eq '67'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_balance_issued.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '4' and claimCalculationSheetDto.status ne '67'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_balance_processing.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '5' and claimCalculationSheetDto.isReleaseOrderGenerate eq 'Y'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_release_issued.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '5' and ( claimCalculationSheetDto.isReleaseOrderGenerate eq 'N' or empty claimCalculationSheetDto.isReleaseOrderGenerate)}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_release_processing.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.calSheetType eq '6'}">
                                                    <img src="${pageContext.request.contextPath}/resources/stamps/payment_third_party_issued.png"
                                                         class="ml-3" width="200"
                                                         height="46">
                                                </c:when>
                                                <c:otherwise>

                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                        <p class="d-block mt-2 text-uppercase mb-0"> Payment Status </p>
                                        <c:choose>
                                            <c:when test="${claimCalculationSheetDto.status == 65}">
                                                    <span class="badge  badge-primary text-uppercase px-3">
                                                        Approved,Voucher generation pending</span><br>
                                            </c:when>
                                            <c:when test="${claimCalculationSheetDto.status == 66}">
                                                    <span class="badge  badge-danger text-uppercase px-3">
                                                        Rejected</span><br>
                                            </c:when>
                                            <c:when test="${claimCalculationSheetDto.status == 67}">
                                                    <span class="badge  badge-success text-uppercase px-3">
                                                        Voucher Generated</span><br>
                                            </c:when>
                                            <c:otherwise>
                                                    <span class="badge  badge-warning text-uppercase px-3">
                                                        Processing..</span><br>
                                            </c:otherwise>
                                        </c:choose>

                                        <p class="d-block mt-2 text-uppercase mb-0"> Payment Type </p>
                                        <a class="badge  badge-warning text-uppercase px-3"
                                           href="#">${claimCalculationSheetDto.paymentTypeDesc}
                                        </a><br>
                                        <c:if test="${claimCalculationSheetDto.noObjectionStatus !='P'}">
                                            <p class="d-block mt-2 text-uppercase mb-0"> No Objection Status</p>
                                            <c:choose>
                                                <c:when test="${claimCalculationSheetDto.noObjectionStatus == 'C'}">
                                            <span class="badge  badge-warning text-uppercase px-3">
                                                       Called - Approval Pending
                                            </span><br>
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.noObjectionStatus == 'A'}">
                                            <span class="badge  badge-success text-uppercase px-3">
                                                       Approved
                                            </span><br>
                                                </c:when>
                                                <c:otherwise>
                                            <span class="badge  badge-danger text-uppercase px-3">
                                               Calling Pending
                                            </span><br>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>

                                        <c:if test="${claimCalculationSheetDto.premiumOutstandingStatus !='P'}">
                                            <p class="d-block mt-2 text-uppercase mb-0">Premium Outstanding
                                                Status</p>
                                            <c:choose>
                                                <c:when test="${claimCalculationSheetDto.premiumOutstandingStatus == 'C'}">
                                            <span class="badge  badge-success text-uppercase px-3">
                                                       Called
                                            </span><br>
                                                </c:when>
                                                <c:when test="${claimCalculationSheetDto.premiumOutstandingStatus == 'A'}">
                                            <span class="badge  badge-success text-uppercase px-3">
                                                       Called.
                                            </span><br>
                                                </c:when>
                                                <c:otherwise>
                                            <span class="badge  badge-danger text-uppercase px-3">
                                               Calling Pending
                                            </span><br>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>

                                        <a href="${pageContext.request.contextPath}/CalculationSheetController/viewPreviousSuperCalculationSheet?calSheetId=${claimCalculationSheetDto.calSheetId}&history=Y&claimNo=${claimCalculationSheetDto.claimNo}"
                                           class="btn btn-link btn-xs mt-2 pl-0 previousView${claimCalculationSheetDto.calSheetId}">
                                            View Calculation Sheet <i class="fa fa-angle-double-right"></i>
                                        </a><br>
                                        <script type="text/javascript">
                                            $('.previousView${claimCalculationSheetDto.calSheetId}').popupWindow({
                                                height: (screen.height - 150),
                                                width: screen.width,
                                                resizable: 1,
                                                centerScreen: 1,
                                                scrollbars: 1,
                                                top: 1,
                                                windowName: 'previousView${claimCalculationSheetDto.calSheetId}'
                                            });
                                        </script>
                                    </div>
                                    <div class="col-md-9 border-left border-right">
                                        <div class="row">
                                            <div class="col text-left align-self-center">
                                                <table width="100%" cellpadding="0" cellspacing="1"
                                                       class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                    <thead>
                                                    <tr>
                                                        <th>Task Description</th>
                                                        <th>Performed By</th>
                                                        <th>Assigned To</th>
                                                        <th>Date/Time</th>
                                                        <th width="150px">TAT</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>

                                                    <tr class="text-left">
                                                        <td colspan="1"><i
                                                                class="fa fa-circle-o text-success pl-5 ml-1"></i>
                                                        </td>
                                                    </tr>
                                                    <tr class="text-left">
                                                        <td colspan="1"><i
                                                                class="fa fa-arrow-down text-success pl-5 ml-1"></i>
                                                        </td>
                                                    </tr>
                                                    <c:forEach var="calculationProcessFlowDto"
                                                               items="${calculationProcessFlowDtoList}">
                                                        <tr>
                                                            <td>${calculationProcessFlowDto.task}</td>
                                                            <td>${calculationProcessFlowDto.inpUserId}</td>
                                                            <td>${calculationProcessFlowDto.assignUserId}</td>
                                                            <td>${calculationProcessFlowDto.taskCompletedDateTime eq '1980-01-01 00:00:00' ? '':calculationProcessFlowDto.taskCompletedDateTime}</td>
                                                            <td class="align-middle text-center">
                                                                    <%--<div class="progress" style="height: 5px;">
                                                                        <div class="progress-bar" role="progressbar"
                                                                             style="width: 25%;" aria-valuenow="25"
                                                                             aria-valuemin="0" aria-valuemax="100">

                                                                        </div>
                                                                    </div>--%>
                                                                <label class="m-0 badge  badge-info px-3">${calculationProcessFlowDto.tat}</label>
                                                            </td>
                                                        </tr>
                                                        <tr class="text-left">
                                                            <td colspan="1"><i
                                                                    class="fa fa-arrow-down text-success pl-5 ml-1"></i>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                    <c:choose>
                                                        <c:when test="${claimCalculationSheetDto.status == 67}">
                                                            <tr class="text-left">
                                                                <td colspan="1"><i
                                                                        class="fa fa-dot-circle-o text-success pl-5 ml-1"></i>
                                                                </td>
                                                            </tr>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <tr class="text-left">
                                                                <td colspan="1"><i
                                                                        class="fa fa-dot-circle-o text-warning pl-5 ml-1"></i>
                                                                </td>
                                                            </tr>
                                                        </c:otherwise>
                                                    </c:choose>

                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2 align-self-center">
                                        <div class="row">
                                            <div class="col text-center align-self-center">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                            </c:forEach>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Payment Voucher Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <c:forEach var="calsheet"
                                           items="${claimSuperDashboardDto.claimCalculationSheetMainDtoList}">
                                    <c:if test="${calsheet.status==67}">
                                        <div class="col-12">
                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                <thead>
                                                <tr>
                                                    <th>Calculation Sheet Id</th>
                                                    <th>Voucher No</th>
                                                    <th>Payee</th>

                                                </tr>
                                                </thead>
                                                <tbody>
                                                <c:forEach var="voucher"
                                                           items="${calsheet.claimCalculationSheetPayeeDtos}">
                                                    <tr>
                                                        <td class="text-center"><span
                                                                class="badge  badge-info  text-uppercase px-3 mt-2 ">${calsheet.calSheetId}</span>
                                                        </td>
                                                        <td>${voucher.voucherNo}</td>
                                                        <td>${voucher.payeeDesc}</td>
                                                    </tr>

                                                </c:forEach>
                                                </tbody>
                                            </table>
                                        </div>
                                    </c:if>
                                </c:forEach>

                            </div>
                        </div>
                    </div>

                    <%--cheque details--%>
                    <div class="card mt-2">
                        <div class="card-header">
                            <h5 class="float-left m-0 text-uppercase">Cheque Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <table width="100%" cellpadding="0" cellspacing="1"
                                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                                        <thead>
                                        <tr>
                                            <th>Voucher No</th>
                                            <th>Cheque No</th>
                                            <th>Cheque Date</th>
                                            <th>Status</th>
                                            <th>Dispatch Location</th>
                                            <th>Date and Time</th>
                                            <th>Received Location</th>
                                            <th width="10%">Cheque Received By</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="voucher"
                                                   items="${claimSuperDashboardDto.voucherDetailsDtos}">
                                            <tr>
                                                <td class="text-center"><span
                                                        class="badge  badge-info  text-uppercase px-3 mt-2 ">${voucher.voucherNo}</span>
                                                </td>
                                                <td>${voucher.chequeNumber}</td>
                                                <td>${voucher.chequeDate}</td>
                                                <td>${voucher.statusDesc}</td>
                                                <td>${voucher.claimPaymentDispatchDto.branchDetailDto.branchName}</td>
                                                <td>${voucher.claimPaymentDispatchDto.dispatchDateTime}</td>
                                                <td>${voucher.claimPaymentDispatchDto.dispatchedLocation.branchName}</td>
                                                <c:choose>
                                                    <c:when test="${voucher.claimPaymentDispatchDto.chequeDispatchStatus == 'Y'}">
                                                        <td>${voucher.claimPaymentDispatchDto.dispatchUser}</td>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <td>
                                                            <select class="form-control"
                                                                    id="selectCheckReceive${voucher.voucherNo}"
                                                                    name="selectCheckReceive"
                                                                    onchange="markCheckStatus('${voucher.chequeNumber}', '${voucher.voucherNo}', '${voucher.claimPaymentDispatchDto.branchDetailDto.branchCode}', '${voucher.claimPaymentDispatchDto.payeeId}')">
                                                                <option value="0">No</option>
                                                                <option value="1">Yes</option>
                                                            </select>
                                                            <script type="text/javascript">
                                                                if (${G_USER.accessUserType ne 100}) {
                                                                    $("#selectCheckReceive" + '${voucher.voucherNo}').prop('disabled', true);
                                                                } else {
                                                                    $("#selectCheckReceive" + '${voucher.voucherNo}').prop('disabled', false);
                                                                }
                                                            </script>
                                                        </td>
                                                    </c:otherwise>
                                                </c:choose>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form name="frmHandoverRemark" id="frmHandoverRemark">
                        <div class="modal fade bd-example-modal-lg" id="modalHandoverRemark" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <input type="hidden" name="txtChequeNo" id="txtChequeNo"/>
                            <input type="hidden" name="txtVoucherNo" id="txtVoucherNo"/>
                            <input type="hidden" name="txtDispatchLocation" id="txtDispatchLocation"/>
                            <input type="hidden" name="txtPayeeId" id="txtPayeeId"/>
                            <input type="hidden" name="txtHandoverRemarkSave" id="txtHandoverRemarkSave"/>
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h6>Cheque Handover Remark</h6>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="col-md-12">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label"> Remark : </label>
                                                <div class="col-sm-8">
                                            <textarea name="txtHandoverRemark" id="txtHandoverRemark"
                                                      class="form-control form-control-sm"
                                                      placeholder="Hand Over Remark"
                                                      onkeyup="validateHandOverRemark()"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer col-sm-12 text-right">
                                            <button class="btn btn-primary" type="button" id="btnHandoverRemarkSave"
                                                    disabled
                                                    onclick="saveChequeStatus()">
                                                Save
                                            </button>
                                            <button class="btn btn-secondary" type="button" id="closeBtn"
                                                    onclick="closeModal();">
                                                Close
                                            </button>
                                        </div>
                                        <div class="text-danger" id="errorDiv" style="display: none">
                                            Please enter a valid email
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class=" mt-2 text-right">
                        <button class="btn btn-secondary" type="button" name="cmdClose" id="cmdClose"
                                onclick="closePage();">Close
                        </button>
                        <hr>
                    </div>

                </fieldset>
            </div>
            <div class="tab-pane fade" role="tabpanel" id="tabs-2">
                <fieldset class="border p-2 ">
                    <div class="col-md-12 col-lg-6 offset-lg-3">
                        <div id="accordion2">
                            <div class="card mt-2">
                                <div class="card-header p-0" id="specialRemarks">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse"
                                           data-target="#specialRemarksCollapes"
                                           aria-expanded="false" aria-controls="col_logdetails">
                                            Special Remarks
                                        </a>
                                    </h5>
                                </div>
                                <div id="specialRemarksCollapes" class="collapse"
                                     aria-labelledby="specialRemarksCollapes"
                                     data-parent="#accordion2">
                                    <div id="specialRemarksDiv" class="scroll"
                                         style="height: calc(100vh - 220px);"></div>
                                    <form name="frmRemark" id="frmRemark">
                                        <input type="hidden" name="claimNo"
                                               value="${claimSuperDashboardDto.claimHandlerDto.claimNo}">
                                        <fieldset class="border p-2 mx-2">
                                            <div class="form-group row has-feedback">
                                                <label class="col-sm-4 col-form-label">Remark :</label>
                                                <div class="col-sm-8">
                                        <textarea name="remark" id="remark" class="form-control form-control-sm"
                                                  data-fv-field="remark"></textarea><i class="form-control-feedback"
                                                                                       data-fv-icon-for="remark"
                                                                                       style="display: none;"></i>
                                                    <small class="help-block" data-fv-validator="notEmpty"
                                                           data-fv-for="remark"
                                                           data-fv-result="NOT_VALIDATED" style="display: none;">This
                                                        field
                                                        is
                                                        required and cannot be empty.
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="mt-2 text-right mb-3">
                                                <button type="submit" name="" value="BR" class="btn btn-info ml-2">Add
                                                    Branch Remark
                                                </button>
                                            </div>
                                        </fieldset>
                                    </form>
                                </div>
                            </div>
                            <div class="card mt-2">
                                <div class="card-header p-0" id="requestAri">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#requestAriCollapes"
                                           aria-expanded="false" aria-controls="col_logdetails" id="tabCusDtl">
                                            Request ARI
                                        </a>
                                    </h5>
                                </div>
                                <div id="requestAriCollapes" class="collapse" aria-labelledby="requestAriCollapes"
                                     data-parent="#accordion2">
                                    <div class="card-body">
                                        <form name="frmRequested" id="frmRequested" method="post" action="">
                                            <input type="hidden" name="claimNo"
                                                   value="${claimSuperDashboardDto.claimHandlerDto.claimNo}">
                                            <div id="requestAriDiv"></div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="card mt-2">
                                <div class="card-header p-0" id="paymentDetails">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#paymentDtlCollapse"
                                           aria-expanded="false" aria-controls="col_pmtdetails" id="tabPmtDtl">
                                            Payment Details
                                        </a>
                                    </h5>
                                </div>
                                <div id="paymentDtlCollapse" class="collapse" aria-labelledby="paymentDtlCollapse"
                                     data-parent="#accordion2">
                                            <jsp:include page="/WEB-INF/jsp/claim/common/PaymentCard.jsp">
                                                <jsp:param name="isVerifyUser" value="${G_USER.userId eq claimHandlerDto.assignUserId ? 'Y' : 'N'}"/>
                                                <jsp:param name="isSpecialTeamUser" value="${G_USER.userId eq claimCalculationSheetMainDto.specialTeamAssignUserId ? 'Y' : 'N'}"/>
                                                <jsp:param name="isBranchUser" value="${G_USER.accessUserType eq 100 ? 'Y' : 'N'}"/>
                                                <jsp:param name="claimNo"  value="${claimHandlerDto.claimNo}"/>
                                                <jsp:param name="documentTypeId" value="<%= AppConstant.BANK_DETAILS_DOCUMENT_TYPE_ID %>"/>

                                            </jsp:include>
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="tab-pane fade" role="tabpanel" id="tabs-3">
                <fieldset class="border p-2 ">
                    <div class="col-md-12 col-lg-6 offset-lg-3">
                        <div id="documentUpload"></div>
                    </div>
                </fieldset>
            </div>
        </div>
    </div>
</div>

<div>
    <div id="paymentModalContainer">

    </div>
</div>

<script type="text/javascript">



    function validateHandOverRemark() {
        if ($("#txtHandoverRemark").val() != null && $("#txtHandoverRemark").val() != '') {
            $("#btnHandoverRemarkSave").prop('disabled', false);
        } else {
            $("#btnHandoverRemarkSave").prop('disabled', true);
        }
    }

    function closeModal() {
        $("#btnHandoverRemarkSave").prop('disabled', true);
        $("#selectCheckReceive" + $("#txtVoucherNo").val()).val(valInError);
        $("#txtHandoverRemark").val('');
        $("#modalHandoverRemark").modal('hide');
    }

    $(document).ready(function () {
        // Bootstrap 4 handles tab switching automatically with data-toggle="tab"
        // Just load the claim stamp container
        loadClaimStampContainer(${claimSuperDashboardDto.claimHandlerDto.claimNo});
    });

    $(".item2 a").click(function (e) {
        specialRemarksDiv();
        requestAriDiv();
    });
    $(".item3 a").click(function (e) {
        documentAllUpload();
    });

    function specialRemarksDiv() {
        $("#specialRemarksDiv").load("${pageContext.request.contextPath}/DashboardController/viewSpecialRemark?P_N_CLIM_NO=${claimSuperDashboardDto.claimHandlerDto.claimNo}");
    }


    function requestAriDiv() {
        $("#requestAriDiv").load("${pageContext.request.contextPath}/DashboardController/viewRequestedAri?P_N_CLIM_NO=${claimSuperDashboardDto.claimHandlerDto.claimNo}");
    }


    function documentUpload() {
        documentUploadIds1 = [];
        $("#docUpload").load("${pageContext.request.contextPath}/DashboardController/viewDocumentUpload?P_N_CLIM_NO=${claimSuperDashboardDto.claimHandlerDto.claimNo}&requestFormId=${requestFormId}");
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    }

    function documentAllUpload() {

        documentUploadIds2 = [];
        $("#documentUpload").load("${pageContext.request.contextPath}/DashboardController/viewAllDocumentUpload?P_N_CLIM_NO=${claimSuperDashboardDto.claimHandlerDto.claimNo}&requestFormId=${requestFormId}");
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    }

    function searchDocumentAllUpload(searchDoc) {
        documentUploadIds2 = [];
        $("#documentUpload").load("${pageContext.request.contextPath}/DashboardController/viewAllDocumentUpload?P_N_CLIM_NO=${claimSuperDashboardDto.claimHandlerDto.claimNo}&requestFormId=${requestFormId}" + "&SEARCH_DOC=" + searchDoc);
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    }

    function reminderLetterDiv() {
        $("#reminderLetterDiv").load("${pageContext.request.contextPath}/DashboardController/viewReminderPrint?P_N_CLIM_NO=${claimSuperDashboardDto.claimHandlerDto.claimNo}");
    }

    function acknowledgementLetter() {
        $("#acknowledgementLetterDiv").load("${pageContext.request.contextPath}/DashboardController/viewAcknowledgementLetterPrint?P_N_CLIM_NO=${claimSuperDashboardDto.claimHandlerDto.claimNo}");
    }

    function closePage() {
        location.href = "${pageContext.request.contextPath}/DashboardController/claimViewList?TYPE=1"
    }

    function loadPaymentOptionPage(claimNo) {

    }

    function loadClaimStampContainer(claimNo) {
        $("#mainClaimStampContainer").load(contextPath + "/RequestAriController/loadClaimStampsForAri?claimNo=" + claimNo);
    }

    function submitCustomerDetails() {
        const isUpload = $("#isUploaded").val();

        if (isUpload == 'true') {
            var formData = $('#frmRequested').serialize();
            if ($('#customerName').val() != '' && $('#contactNo').val() != '' && $('#address1').val() != '' && $('#address2').val() != '') {
                $.ajax({
                    url: contextPath + "/RequestAriController/updateAri",
                    type: 'POST',
                    data: formData,
                    success: function (result) {
                        var obj = JSON.parse(result);
                        console.log(obj);
                        console.log(obj == "SUCCESS");
                        if (obj == "SUCCESS") {
                            notify("Successfully updated", "success");
                            requestAriDiv();
                            logdetails();
                        } else {
                            notify("Can not be updated", "danger");
                        }

                    }
                });
            } else {
                notify("Please Submit All Required Data", "danger");
            }
        } else {
            notify("Please Upload Required Documents prior to Submitting Customer Details", "danger");
        }
    }

    let valInError = '';

    function saveChequeStatus() {
        $("#txtHandoverRemarkSave").val($("#txtHandoverRemark").val());
        $("#txtHandoverRemark").val('');
        $("#modalHandoverRemark").modal('hide');
        let voucherNo = $("#txtVoucherNo").val();
        let checkNo = $("#txtChequeNo").val();

        var val = $("#selectCheckReceive" + voucherNo).val();

        var claimNo = 0;<%--${claimSuperDashboardDto.claimHandlerDto.claimNo};--%>
        if (val == 0) {
            valInError = 1;
        } else {
            valInError = 0;
        }
        bootbox.confirm({
            message: "Do you want to Change the Received Status of the cheque : " + checkNo + "?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/DashboardController/changeCheckReceiveStatus",
                        type: 'POST',
                        data: {
                            voucherNo: voucherNo,
                            chequeNo: checkNo,
                            dispatchLocation: $("#txtDispatchLocation").val(),
                            status: val,
                            claimNo: claimNo,
                            payeeId: $("#txtPayeeId").val(),
                            remark: $("#txtHandoverRemarkSave").val()
                        },
                        success: function (response) {
                            var resp = JSON.parse(response);
                            if (resp == 'SUCCESS') {
                                notify('Successfully Changed Cheque Status', 'success');
                                reload();
                            } else {
                                notify('Failed to Change Cheque Status', 'danger');
                                $("#txtHandoverRemarkSave").val('');
                                $("#selectCheckReceive" + voucherNo).val(valInError);
                            }
                        }
                    })
                } else {
                    $("#selectCheckReceive" + voucherNo).val(valInError);
                }
            }
        });
    }

    function markCheckStatus(checkNo, voucherNo, dispatchLocation, payeeId) {
        $("#txtChequeNo").val(checkNo);
        $("#txtVoucherNo").val(voucherNo);
        $("#txtDispatchLocation").val(dispatchLocation);
        $("#txtPayeeId").val(payeeId);

        var val = $("#selectCheckReceive" + voucherNo).val();

        if (val == 0) {
            valInError = 1;
        } else {
            valInError = 0;
        }
        $("#modalHandoverRemark").modal({
            show: true,
            backdrop: 'static'
        });
    }

    function reload() {
        location.reload();
    }
</script>
</body>
</html>

