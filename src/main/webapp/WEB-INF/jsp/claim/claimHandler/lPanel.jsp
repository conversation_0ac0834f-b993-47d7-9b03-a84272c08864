<%--
    Document   : lPanel
    Created on : May 11, 2021, 11:24:25 AM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Tharindu Perera
    version 1.0
--%>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/ScrollTabla.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/datatables.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/datatables.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/dataTables.fixedHeader.min.js"></script>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);

        });


        function setLoading(url) {
//            showLoader()
            window.location = url;
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

        function init() {
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            var today = new Date();
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var yyyy = today.getFullYear();
            $('#txtFromDate').val(yyyy+'-'+mm+'-'+dd+' '+'00:00');
            $('#txtToDate').val(yyyy+'-'+mm+'-'+dd+' '+'23:59');
        }


    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">

                <h5> View Letter Panel List</h5>

            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                       value="${searchFromDate}"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRejectionPanelType" class="col-sm-4 col-form-label">Rejection
                                                Panel Type</label>
                                            <div class="col-sm-8">
                                                <select name="txtRejectionPanelType" id="txtRejectionPanelType"
                                                        class="form-control form-control-sm">
                                                    <option selected value="">All</option>
                                                    <option value="1">Sub Panel</option>
                                                    <option value="2">Main Panel</option>
                                                </select>
                                                <script>
                                                    $("#txtRejectionPanelType").val("${searchRejectionPanelType==null?'':searchRejectionPanelType}");
                                                </script>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRejection" class="col-sm-4 col-form-label">Rejection</label>
                                            <div class="col-sm-8">
                                                <select name="txtRejection" id="txtRejection"
                                                        class="form-control form-control-sm">
                                                    <option selected value="">All</option>
                                                    <c:forEach var="reasonDto" items="${reasonList}">
                                                        <option value="${reasonDto.repudiatedLetterType}">${reasonDto.repudiatedLetterTypeDesc}</option>
                                                    </c:forEach>
                                                </select>
                                                <script>
                                                    $("#txtRejection").val("${searchRejection==null?'':searchRejection}");
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">

                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date"
                                                       value="${searchToDate}"
                                                >
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtDmaker" class="col-sm-4 col-form-label">Decision Maker</label>
                                            <div class="col-sm-8">
                                                <select name="txtDmaker" id="txtDmaker"
                                                        class="form-control form-control-sm">
                                                    <option selected value="">All</option>
                                                    <c:forEach var="UserDto" items="${dMakerList}">
                                                        <option value="${UserDto.userId}">${UserDto.userId}</option>
                                                    </c:forEach>
                                                </select>
                                                <script>
                                                    $("#txtDmaker").val("${searchDmaker==null?'':searchDmaker}");
                                                </script>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtLPanelStatus" class="col-sm-4 col-form-label">Letter Panel
                                                Status </label>
                                            <div class="col-sm-8">
                                                <select name="txtLPanelStatus" id="txtLPanelStatus"
                                                        class="form-control form-control-sm">
                                                    <option selected value="N">Letter Not Attached</option>
                                                    <option value="Y">Letter Attached</option>
                                                    <option value="">All</option>
                                                </select>
                                                <script>
                                                    $("#txtLPanelStatus").val("${searchLPanelStatus==null?'N':searchLPanelStatus}");
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()"
                                        >Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <div class="row">
                    <div class="col-md-12">
                        <hr class="my-2">
                        <div class="">
                            <div class="mt-2 ">
                                <h6>Letter Panel Result</h6>
                                <div class="mt-2" style="overflow-x: auto">
                                    <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                           style="cursor:pointer" width="100%">
                                        <thead>
                                        <tr>
                                            <th width="40px">No</th>
                                            <th>Claim no</th>
                                            <th>Vehicle No</th>
                                            <th>Azentio Claim No</th>
                                            <th class="min-mobile">Policy No</th>
                                            <th>Policy Channel Type</th>
                                            <th>Rejection Reason</th>
                                            <th>Accident Date</th>
                                            <th>Assigned D Maker</th>
                                            <th>File Assigned Date/Time</th>
                                            <th>Liability Assigned User</th>
                                            <th>Liability Assigned Date/Time</th>
                                            <th>Int.Liability Assigned User</th>
                                            <th>Int.Liability Assigned Date/Time</th>
                                            <th>ACR</th>
                                            <th>Rejection Letter Attached (Y/N)</th>
                                            <th class="min-mobile"></th>
                                        </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/lpanel-datatables.js?v3"></script>
</body>
</html>
