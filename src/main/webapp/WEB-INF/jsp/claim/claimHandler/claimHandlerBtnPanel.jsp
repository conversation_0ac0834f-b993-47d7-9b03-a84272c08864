<%@taglib prefix="c" uri="jakarta.tags.core" %>
<div role="group">

    <c:choose>
        <c:when test="${claimHandlerDto.closeStatus eq 'CLOSE' || claimHandlerDto.closeStatus eq 'SETTLE'}">
            <button id="btnGroupDrop10" type="button" class="btn btn-secondary dropdown-toggle"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Liability
            </button>
            <c:if test="${47 eq claimHandlerDto.claimStatus and (IS_DECISION_MAKER or IS_LETTER_PANEL_USER) and (G_USER.userId eq claimHandlerDto.decisionMakingAssignUserId or G_USER.userId eq claimHandlerDto.letterPanelUserId)}">
                <a href="${pageContext.request.contextPath}/ClaimHandlerController/viewRejectionDocumentViewer?claimNo=${claimHandlerDto.claimNo}"
                   class="claimViewRepudiatedLetter">
                    <button class="btn btn-secondary" type="button">
                        View Rejection Letter
                    </button>
                </a>
            </c:if>
            <div class="dropdown-menu" aria-labelledby="btnGroupDrop10">
                <a onclick="specialToClaimPanel()" class="dropdown-item" href="#">Forward to obtain
                    special comment/Approval</a>
            </div>
        </c:when>
        <c:when test="${claimHandlerDto.closeStatus eq 'REOPEN' && claimHandlerDto.reOpenType eq 'EX' and 20 eq claimHandlerDto.claimStatus}">
            <button id="btnGroupDrop3" type="button" class="btn btn-secondary dropdown-toggle"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Liability
            </button>
            <div class="dropdown-menu" aria-labelledby="btnGroupDrop3">
                <a onclick="pendingLiability()" class="dropdown-item" href="#">Liability Pending</a>
                <a onclick="forwardToSparePart()" class="dropdown-item" href="#">Forward to spare
                    parts coordinator for DO </a>
                <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                    <a onclick="storeFile()" class="dropdown-item storefile" href="#">Store File</a>
                </c:if>
                <c:if test="${claimHandlerDto.isFileStore eq 'Y'}">
                    <a onclick="restoreFile()" class="dropdown-item" href="#">Re-Store File</a>
                </c:if>
                <a onclick="specialToClaimPanel()" class="dropdown-item" href="#">Forward to obtain
                    special comment/Approval</a>
            </div>

        </c:when>
        <c:when test="${claimHandlerDto.closeStatus eq 'REOPEN' && claimHandlerDto.reOpenType eq 'NORMAL' and 20 eq claimHandlerDto.claimStatus}">
            <button id="btnGroupDrop2" type="button" class="btn btn-secondary dropdown-toggle"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Liability
            </button>

            <div class="dropdown-menu" aria-labelledby="btnGroupDrop2">
                <c:if test="${claimHandlerDto.liabilityAprvStatus eq 'A'}">
                    <a onclick="pendingLiability()" class="dropdown-item" href="#">Liability Pending</a>
                </c:if>
                <c:if test="${claimHandlerDto.liabilityAprvStatus ne 'A'}">
                    <a onclick="checkCoverNoteApproveLiability(2,1)" class="dropdown-item" href="#">Liability
                        Approve</a>
                </c:if>
                <a onclick="referClaimPanel()" class="dropdown-item" href="#">Make a Decision</a>
                <c:if test="${claimHandlerDto.supplyOrderAssignStatus != 'Y' and !IS_TOTAL_LOSS_CLAIM_HANDLER_USER}">
                    <a onclick="forwardToSparePart()" class="dropdown-item" href="#">Forward to spare
                        parts coordinator for DO </a>
                </c:if>
                <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                    <a onclick="storeFile()" class="dropdown-item storefile" href="#">Store File</a>
                </c:if>
                <c:if test="${claimHandlerDto.isFileStore eq 'Y'}">
                    <a onclick="restoreFile()" class="dropdown-item" href="#">Re-Store File</a>
                </c:if>
                <a onclick="specialToClaimPanel()" class="dropdown-item" href="#">Forward to obtain
                    special comment/Approval</a>
            </div>

        </c:when>
        <c:otherwise>

            <%--38	FORWARD TO DECISION MAKER--%>
            <c:if test="${(38 eq claimHandlerDto.claimStatus or 84 eq claimHandlerDto.claimStatus) and (G_USER.userId eq claimHandlerDto.assignUserId || G_USER.userId eq claimHandlerDto.initLiabilityAssignUserId)}">
                <%-- <button onclick="arrangeInvestigationByDecisionMaker()" class="btn btn-secondary"
                type="button">Arrange Investigation
                </button>--%>
                <%--<button onclick="requestForInvestigationByDecisionMaker()" class="btn btn-secondary"
                        type="button">Request Investigation
                </button>--%>
                <button onclick="approveRejectionByPanel(1)" class="btn btn-secondary" type="button">Claim Rejected
                </button>
                <button onclick="rejectRejectionByPanel(1)" class="btn btn-secondary" type="button">Claim Approved
                </button>
            </c:if>
            <%--39	FORWARD TO 2 MEMBER PANEL--%>
            <c:if test="${39 eq claimHandlerDto.claimStatus and G_USER.userId eq claimHandlerDto.assignUserId}">
                <button onclick="approveRejectionByPanel(1)" class="btn btn-secondary" type="button">Claim Rejected
                </button>
                <button onclick="rejectRejectionByPanel(1)" class="btn btn-secondary" type="button">Claim Approved
                </button>
            </c:if>
            <%--40	FORWARD TO 4 MEMBER PANEL--%>
            <c:if test="${40 eq claimHandlerDto.claimStatus and G_USER.userId eq claimHandlerDto.assignUserId and isApprovalPending}">
                <button onclick="approveRejectionByPanel(2)" class="btn btn-secondary" type="button">Claim Rejected
                </button>
                <button onclick="rejectRejectionByPanel(2)" class="btn btn-secondary" type="button">Claim Approved
                </button>
            </c:if>
            <%--52	DECISION MAKER REQUEST INVESTIGATION--%>
            <c:if test="${52 eq claimHandlerDto.claimStatus and G_USER.userId eq claimHandlerDto.assignUserId}">
                <%-- <button onclick="arrangeInvestigation(1)" class="btn btn-secondary" type="button">Arrange
                Investigation
                </button>--%>
            </c:if>
            <%--41	2 MEMBER PANEL APPROVED--%>
            <c:if test="${41 eq claimHandlerDto.claimStatus and G_USER.userId eq claimHandlerDto.assignUserId}">
                <button onclick="generateRejectionLetter(1)" class="btn btn-secondary" type="button">
                    Generate Rejection Letter
                </button>
            </c:if>
            <%--44	4 MEMBER PANEL APPROVED--%>
            <c:if test="${44 eq claimHandlerDto.claimStatus and G_USER.userId eq claimHandlerDto.assignUserId}">
                <button onclick="generateRejectionLetter(2)" class="btn btn-secondary"
                        type="button" ${claimHandlerDto.isPrintRepudiatedLetter eq 'Y' ? '' : ''}>
                    Generate Rejection Letter
                </button>
            </c:if>
            <%--47	CR	CLAIM REPUDIATED	4--%>
            <c:if test="${47 eq claimHandlerDto.claimStatus  and (G_USER.userId eq claimHandlerDto.assignUserId)}">
                <a href="${pageContext.request.contextPath}/ClaimHandlerController/viewRejectionDocumentViewer?claimNo=${claimHandlerDto.claimNo}"
                   class="claimViewRepudiatedLetter">
                    <button class="btn btn-secondary" type="button">
                        View Rejection Letter
                    </button>
                </a>
            </c:if>
            <button id="btnGroupDrop1" type="button" class="btn btn-secondary dropdown-toggle"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Liability
            </button>
            <div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                    <%--35	INITIAL LIABILITY PENDING--%>
                <c:if test="${35 eq claimHandlerDto.claimStatus and  G_USER.userId eq claimHandlerDto.initLiabilityAssignUserId}">
                    <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                        <a onclick="checkCoverNoteAndApproveInitLiabilityAndStoreFile()"
                           class="dropdown-item initliability" href="#">Initial
                            Liability Checked And Store</a>
                        <a onclick="checkCoverNoteAndApproveInitLiability()" class="dropdown-item initliability"
                           href="#">Initial
                            Liability Checked Forward To Payment</a>
                        <a onclick="referClaimPanel()" class="dropdown-item" href="#">Make a Decision</a>
                        <%--<a onclick="storeFile()" class="dropdown-item storefile" href="#">Store File</a>--%>
                    </c:if>
                    <c:if test="${claimHandlerDto.isFileStore eq 'Y'}">
                        <a onclick="restoreFile()" class="dropdown-item" href="#">Re-Store File</a>
                    </c:if>
                </c:if>
                    <%--38	FORWARD TO DECISION MAKING--%>
                <c:if test="${(38 eq claimHandlerDto.claimStatus
        or 41 eq claimHandlerDto.claimStatus
        or 42 eq claimHandlerDto.claimStatus
        or 44 eq claimHandlerDto.claimStatus
        or 45 eq claimHandlerDto.claimStatus
        or 53 eq claimHandlerDto.claimStatus
        or 54 eq claimHandlerDto.claimStatus
        or 55 eq claimHandlerDto.claimStatus
        or 68 eq claimHandlerDto.claimStatus
        or 84 eq claimHandlerDto.claimStatus)
        and G_USER.userId eq claimHandlerDto.decisionMakingAssignUserId}">
                    <c:if test="${claimHandlerDto.initLiabilityAprvStatus ne 'A'}">
                        <a onclick="approveAndStoreFileLiability()" class="dropdown-item" href="#">Initial Liability in
                            order,
                            forward to store</a>
                    </c:if>

<%--                    <a onclick="forwardToPanel(1)" class="dropdown-item" href="#">Make Decision</a>--%>
<%--                    <a onclick="forwardToPanel(2)" class="dropdown-item" href="#">Forward To Main Panel</a>--%>
                    <c:if test="${claimHandlerDto.initLiabilityAprvStatus eq 'A' &&  claimHandlerDto.liabilityAprvStatus != 'A'}">
                        <a onclick="checkCoverNoteApproveLiability(0,2)" class="dropdown-item" href="#">Liability in
                            order,
                            forward to Processing</a>
                        <a onclick="checkCoverNoteApproveLiability(1,1)" class="dropdown-item" href="#">Liability
                            Approve</a>
                    </c:if>
                    <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                        <a onclick="storeFile()" class="dropdown-item storefile" href="#">Store File</a>
                    </c:if>
                    <c:if test="${claimHandlerDto.isFileStore eq 'Y'}">
                        <a onclick="restoreFile()" class="dropdown-item" href="#">Re-Store File</a>
                    </c:if>

                </c:if>
                    <%--39	FORWARD TO 2 MEMBER PANEL--%>
                <c:if test="${39 eq claimHandlerDto.claimStatus and IS_TWO_PANEL_USER}">
                    <a onclick="forwardToPanel(2)" class="dropdown-item" href="#">Forward To Main Panel</a>
                </c:if>
                    <%--42	2 MEMBER PANEL REJECTED--%>
                <c:if test="${42 eq claimHandlerDto.claimStatus and G_USER.userId eq claimHandlerDto.decisionMakingAssignUserId}">
                    <a onclick="checkCoverNoteApproveLiability(1,1)" class="dropdown-item" href="#">Liability
                        Approve</a>
                </c:if>
                    <%--45	4 MEMBER PANEL REJECTED--%>
<%--                <c:if test="${49 eq claimHandlerDto.claimStatus and IS_DECISION_MAKER and G_USER.userId eq claimHandlerDto.decisionMakingAssignUserId}">--%>
<%--                    <a onclick="checkCoverNoteApproveLiability(2,1)" class="dropdown-item" href="#">Liability--%>
<%--                        Approve</a>--%>
<%--                </c:if>--%>
                    <%--36	ILA	INITIAL LIABILITY APPROVED	4--%>
                    <%--49	LP	LIABILITY PENDING	4--%>
                <c:if test="${(49 eq claimHandlerDto.claimStatus or 36 eq claimHandlerDto.claimStatus) and (IS_CLAIM_HANDLER_USER or IS_OFFER_TEAM_CLAIM_HANDLER_USER or IS_TOTAL_LOSS_CLAIM_HANDLER_USER) and G_USER.userId eq claimHandlerDto.assignUserId}">
                    <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                        <a onclick="checkCoverNoteApproveLiability(0,1)" class="dropdown-item" href="#">Liability
                            Approve</a>
                        <a onclick="referClaimPanel()" class="dropdown-item" href="#">Make a Decision</a>
                        <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                            <a onclick="storeFile()" class="dropdown-item storefile" href="#">Store File</a>
                        </c:if>

                        <%--<a onclick="requestForInvestigationByClaimHandler()" class="dropdown-item" href="#">Request
                            Investigation</a>--%>
                    </c:if>

                    <c:if test="${claimHandlerDto.isFileStore eq 'Y'}">
                        <a onclick="restoreFile()" class="dropdown-item" href="#">Re-Store File</a>
                    </c:if>
                </c:if>
                <c:if test="${57 != claimHandlerDto.claimStatus and not (claimHandlerDto.claimStatus eq 83 and G_USER.userId eq claimHandlerDto.forwardedEngineer)}">
                    <a onclick="specialToClaimPanel()" class="dropdown-item" href="#">Forward to obtain
                        special comment/Approval</a>

                    <c:if test="${(50 eq claimHandlerDto.claimStatus and IS_DECISION_MAKER and G_USER.userId eq claimHandlerDto.decisionMakingAssignUserId)}">
                        <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                            <a onclick="storeFile()" class="dropdown-item storefile" href="#">Store File</a>
                        </c:if>
                        <c:if test="${claimHandlerDto.isFileStore eq 'Y'}">
                            <a onclick="restoreFile()" class="dropdown-item" href="#">Re-Store File</a>
                        </c:if>
                    </c:if>

                </c:if>

                <c:if test="${(50 eq claimHandlerDto.claimStatus or 49 eq claimHandlerDto.claimStatus or 54 eq claimHandlerDto.claimStatus or 36 eq claimHandlerDto.claimStatus) and (IS_SPECIAL_TEAM or IS_OFFER_TEAM_SPECIAL_TEAM)}">
                    <a onclick="forwardToClaimHandler()" class="dropdown-item" href="#">Forward to Claim handler</a>
                </c:if>
                    <%--50	LA	LIABILITY APPROVED	4--%>
                <c:if test="${'A' eq claimHandlerDto.liabilityAprvStatus and (IS_CLAIM_HANDLER_USER or IS_OFFER_TEAM_CLAIM_HANDLER_USER or IS_TOTAL_LOSS_CLAIM_HANDLER_USER) and G_USER.userId eq claimHandlerDto.assignUserId }">
                    <a onclick="pendingLiability()" class="dropdown-item" href="#">Liability Pending</a>
                    <c:if test="${claimHandlerDto.supplyOrderAssignStatus != 'Y' and !IS_TOTAL_LOSS_CLAIM_HANDLER_USER}">
                        <a onclick="forwardToSparePart()" class="dropdown-item" href="#">Forward to spare
                            parts coordinator for DO </a>
                    </c:if>
                    <c:if test="${claimHandlerDto.supplyOrderAssignStatus eq 'Y' and claimHandlerDto.isGenarateSupplyOrder eq 'N' and isSupplyOrderPEnding eq 'true' and !IS_TOTAL_LOSS_CLAIM_HANDLER_USER}">
                        <a onclick="recallDO()" class="dropdown-item" href="#">Recall DO </a>
                    </c:if>
                    <c:if test="${'A' eq claimHandlerDto.liabilityAprvStatus and (not (((claimHandlerDto.aprvAdvanceAmount < 0) or (claimHandlerDto.aprvAdvanceAmount > 0))) and 81 ne claimHandlerDto.claimStatus and 82 ne claimHandlerDto.claimStatus) and (IS_CLAIM_HANDLER_USER or IS_OFFER_TEAM_CLAIM_HANDLER_USER or IS_TOTAL_LOSS_CLAIM_HANDLER_USER) and G_USER.userId eq claimHandlerDto.assignUserId }">
                        <a onclick="forwardForAdvance()" class="dropdown-item" href="#">Forward for Advance Request</a>
                    </c:if>
                    <c:if test="${(81 eq claimHandlerDto.claimStatus || 82 eq claimHandlerDto.claimStatus)}">
                        <a onclick="recallAdvanceRequestByClaimHandler()" class="dropdown-item" href="#">Recall Advance
                            Request</a>
                    </c:if>
                    <c:if test="${claimHandlerDto.isFileStore ne 'Y'}">
                        <a onclick="storeFile()" class="dropdown-item storefile" href="#">Store File</a>
                    </c:if>
                    <c:if test="${claimHandlerDto.isFileStore eq 'Y'}">
                        <a onclick="restoreFile()" class="dropdown-item" href="#">Re-Store File</a>
                    </c:if>
                </c:if>

                <c:if test="${(39 eq claimHandlerDto.claimStatus and IS_TWO_PANEL_USER) or (40 eq claimHandlerDto.claimStatus and IS_FOUR_PANEL_USER and isApprovalPending)}">
                    <a onclick="returnToDecisionMaker()" class="dropdown-item" href="#">Return to Decision Maker
                    </a>
                </c:if>

            </div>
        </c:otherwise>
    </c:choose>

</div>

<script type="text/javascript">
    var count = $(".dropdown-menu .dropdown-item").length;
    if (count == 0) {
        $('.dropdown-menu').hide();
        $('#btnGroupDrop1').hide();
    }
    $('.claimViewRepudiatedLetter').popupWindow({
        height: screen.height,
        width: screen.width,
        centerBrowser: 0,
        left: 0,
        resizable: 1,
        centerScreen: 1,
        scrollbars: 1,
        windowName: 'claimViewRepudiatedLetter'
    });

</script>
