<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/26/2018
  Time: 10:21 AM
  To change this template use File | Settings | File Templates.
--%>

<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<head>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css">
    <script src="${pageContext.request.contextPath}/resources/js/imagePre.js"></script>

    <script src="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.js"></script>
    <script>
        $('[data-magnify]').magnify({
            fixedContent: false,
            initMaximized: false,
            fixedModalPos: true
        });
    </script>
</head>
<form name="frmInvestigation" id="frmInvestigation">
    <input type="hidden" name="claimNo" value="${sessionClaimHandlerDto.claimsDto.claimNo}">
    <div class="card-body">
        <c:set var="investigationDetailsDto" value="${investigationDetailsFormDto.investigationDetailsDto}"/>
        <c:set var="investigationReportStatus" value="${investigationDetailsFormDto.investigationReportStatus}"/>
        <div class="form-group row">
            <div class="col-12">
                <c:if test="${investigationDetailsFormDto.investigationDetailsDtoList.size()>0}">
                    <div id="invPaymentDiv">
                        <fieldset class="border p-2 my-2">
                            <div class="row">
                                <div class="col-12">
                                        <%--<p class="m-0"><b>View Last Investigation Details</b></p>--%>
                                    <div class="row">
                                        <div class="col">
                                            <table width="100%" cellpadding="0" cellspacing="1"
                                                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                                                <thead>
                                                <tr>
                                                    <th class="tbl_row_header">Job No</th>
                                                    <th class="tbl_row_header">Investigation User</th>
                                                    <th class="tbl_row_header">Arrange Date/Time</th>
                                                    <th class="tbl_row_header">Completed Date/Time</th>
                                                    <th class="tbl_row_header"> Status</th>
                                                    <th class="tbl_row_header"> Payment</th>
                                                    <th class="tbl_row_header"> Action</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <c:forEach var="investigationDetails1"
                                                           items="${investigationDetailsFormDto.investigationDetailsDtoList}">
                                                    <tr>
                                                        <td>${investigationDetails1.investJobNo}</td>
                                                        <td>${investigationDetails1.assignInvestigatorName}</td>
                                                        <td>${investigationDetails1.investArrangeDateTime}</td>
                                                        <td>${investigationDetails1.investCompletedDateTime}</td>
                                                        <td>
                                                            <c:if test="${investigationDetails1.investigationStatus=='C'}">
                                                                Completed
                                                            </c:if>
                                                            <c:if test="${investigationDetails1.investigationStatus=='CAN'}">
                                                                Cancel
                                                            </c:if>
                                                        </td>
                                                        <td class="text-center">
                                                            <c:choose>
                                                                <c:when test="${investigationDetails1.paymentStatus=='CAN'}">
                                                                    <button class="btn-danger btn btn-sm btn-xs"
                                                                            type="button"
                                                                            onclick="showUpdatePaymentModel('${investigationDetails1.investTxnNo}')"
                                                                            title="View Investigator Payment- Rejected">
                                                                        <i
                                                                                class="fa fa-eye"></i></button>
                                                                </c:when>
                                                                <c:when test="${investigationDetails1.paymentStatus=='P'}">
                                                                    <button class="btn-warning btn btn-sm btn-xs"
                                                                            type="button"
                                                                            onclick="showUpdatePaymentModel('${investigationDetails1.investTxnNo}')"
                                                                            title="View Investigator Payment- Pending Voucher Generate ">
                                                                        <i
                                                                                class="fa fa-eye"></i></button>
                                                                </c:when>
                                                                <c:when test="${investigationDetails1.paymentStatus=='A'}">
                                                                    <button class="btn-success btn btn-sm btn-xs"
                                                                            type="button"
                                                                            onclick="showUpdatePaymentModel('${investigationDetails1.investTxnNo}')"
                                                                            title="View Investigator Payment- Approved">
                                                                        <i
                                                                                class="fa fa-eye"></i></button>
                                                                </c:when>
                                                            </c:choose>

                                                        </td>
                                                        <td class="text-center">
                                                            <button class="btn-primary btn btn-sm btn-xs"
                                                                    type="button"
                                                                    onclick="showIndividualInvestigation('${investigationDetails1.investTxnNo}')"
                                                                    title="View Investigator"><i
                                                                    class="fa fa-eye"></i></button>
                                                        </td>
                                                    </tr>
                                                </c:forEach>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </c:if>
            </div>
            <div class="col-12">
                <div class="row">
                    <label class="col-sm-4 col-form-label">Investigation Status <span
                            class="text-danger font-weight-bold"> *</span>
                        :</label>
                    <div class="col-sm-8">
                        <select name="investigationStatus"
                                id="investigationStatus"
                                class="form-control form-control-sm">
                            <c:choose>
                                <c:when test="${investigationDetailsDto.investigationStatus=='CH_REQ_INVEST'}">
                                    <option value="CH_REQ_INVEST" disabled>Claim Handler Request Investigation</option>
                                    <option value="INVEST_APPROVED">Investigation Request Approved</option>
                                    <option value="CAN">Investigation Cancel</option>
                                    <c:choose>
                                        <c:when test="${sessionClaimUserTypeDto.decisionMaker}">
                                            <option value="DM_REQ_INVEST">Decision Maker Request Investigation</option>
                                        </c:when>
                                    </c:choose>
                                </c:when>
                                <c:when test="${investigationDetailsDto.investigationStatus=='DM_REQ_INVEST'}">
                                    <option value="DM_REQ_INVEST">Decision Maker Request Investigation</option>
                                    <option value="INVEST_APPROVED">Investigation Request Approved</option>
                                    <option value="CAN">Investigation Cancel</option>
                                </c:when>
                                <c:when test="${investigationDetailsDto.investigationStatus=='INVEST_APPROVED'}">
                                    <option value="INVEST_APPROVED" disabled >Investigation Request Approved</option>
                                    <option value="AR">Investigation Arrange</option>
                                </c:when>
                                <c:when test="${investigationDetailsDto.investigationStatus=='AR'}">
                                    <option value="AR" disabled>Investigation Arrange</option>
                                    <option value="C">Investigation Completed</option>
                                </c:when>

                                <c:when test="${investigationDetailsDto.investigationStatus=='C'}">
                                    <option value="C">Investigation Completed</option>
                                </c:when>
                                <c:when test="${investigationDetailsDto.investigationStatus=='CAN'}">
                                    <option value="CAN">Investigation Cancel</option>
                                </c:when>
                                <c:otherwise>
                                    <option value="N">-- Please Select --</option>
                                    <c:choose>
                                        <c:when test="${sessionClaimUserTypeDto.decisionMaker}">
                                            <option value="DM_REQ_INVEST">Decision Maker Request Investigation</option>
                                        </c:when>
                                        <c:otherwise>
                                            <option value="CH_REQ_INVEST">Request Investigation</option>
                                        </c:otherwise>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                        </select>
                        <script type="text/javascript">
                            $("#investigationStatus").val('${investigationDetailsDto.investigationStatus}');
                        </script>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group row">
            <input name="investTxnNo" type="hidden" value="${investigationDetailsDto.investTxnNo}">
            <label class="col-sm-4 col-form-label">Vehicle No :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.vehicleNo}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Insured Name :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.custName}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Insured Address :</label>
            <div class="col-md-8">
                <span class="label_Value col-md-12 pl-0">${sessionClaimHandlerDto.claimsDto.policyDto.custAddressLine1}</span>
                <span class="label_Value col-md-12 pl-0">${sessionClaimHandlerDto.claimsDto.policyDto.custAddressLine2}</span>
                <span class="label_Value col-md-12 pl-0">${sessionClaimHandlerDto.claimsDto.policyDto.custAddressLine3}</span>
            </div>
        </div>
        <%--        <div class="form-group row">--%>
        <%--            <label class="col-sm-4 col-form-label"></label>--%>
        <%--            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.custAddressLine2}</span>--%>
        <%--        </div>--%>
        <%--        <div class="form-group row">--%>
        <%--            <label class="col-sm-4 col-form-label"></label>--%>
        <%--            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.custAddressLine3}</span>--%>
        <%--        </div>--%>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Insured Contact No :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.custMobileNo}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Policy Period :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.inspecDate}
                                             <c:if test="${sessionClaimHandlerDto.claimsDto.policyDto.coverNoteNo ne sessionClaimHandlerDto.claimsDto.policyDto.policyNumber}">
                                                 to ${sessionClaimHandlerDto.claimsDto.policyDto.expireDate}
                                             </c:if></span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Claim No :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.claimNo}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Lease Company and Branch :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.financeCompany}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Driver Name :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.driverName}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Date/Time of Accident :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.accidDate} &nbsp; ${sessionClaimHandlerDto.claimsDto.accidTime}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Place of Accident :</label>
            <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.placeOfAccid}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Inspection assessors name and contact No :</label>
            <span class="label_Value col-md-8 ml-0">
            <c:forEach var="list" items="${investigationDetailsFormDto.assessorDtoList}">
                <span class="label_Value col-md-12 pl-0">${list.firstName}&nbsp;${list.lastName} - ${list.assessorContactNo}</span>
                <%--                <span>${list.firstName} ${list.firstName} - ${list.assessorContactNo}</span>--%>
            </c:forEach>

            </span>
        </div>

        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Type of Loss :</label>
            <div class="col-sm-8">
                <div class="row">
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input type="checkbox" name="isAccident" id="isAccident" title="Accident" class="align-middle"
                               value="Y" ${investigationDetailsDto.isAccident=='Y'?"checked":""}>
                        <span class="checkmark"></span>
                        <span class="custom-control-description">Accident </span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input name="isTheft" title="Theft" type="checkbox" id="isTheft"
                               class="align-middle" value="Y" ${investigationDetailsDto.isTheft=='Y'?"checked":""}>
                        <span class="checkmark"></span>
                        <span class="custom-control-description">Theft</span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input name="isFire" id="isFire" title="Fire" type="checkbox"
                               class="align-middle" value="Y" ${investigationDetailsDto.isFire=='Y'?"checked":""}>
                        <span class="checkmark"></span>
                        <span class="custom-control-description">Fire</span>
                    </label>
                </div>
            </div>
        </div>

        <%--    <div class="form-group row">--%>
        <%--        <label class="col-sm-4 col-form-label">Policy Inception Date :</label>--%>
        <%--        <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.inspecDate}</span>--%>
        <%--    </div>--%>
        <div class="form-group row">
            <label class="col-form-label col-sm-4 text-warning">Reasons for Investigation :</label>
            <span class="label_Value col-md-8 ml-0"></span>
        </div>

        <c:set var="index" value="0"/>
        <c:forEach var="investigationReasonDetailsDto"
                   items="${investigationDetailsDto.investigationReasonDetailsDtoList}">
            <div class="form-group row">
                <label class="col-form-label col-sm-4">${investigationReasonDetailsDto.invesReason} : </label>
                <div class="col-sm-8">

                    <input type="hidden"
                           name="investigationDetailsDto.investigationReasonDetailsDtoList[${index}].invesReasonRefNo"
                           value="${investigationReasonDetailsDto.invesReasonRefNo}">
                    <label class="custom-control custom-checkbox check-container ml-0">

                        <input name="investigationDetailsDto.investigationReasonDetailsDtoList[${index}].isCheck"
                               title="" type="checkbox" class="align-middle"
                               value="Y" ${investigationReasonDetailsDto.isCheck=='Y'?"checked":""} />

                        <input name="investigationDetailsDto.investigationReasonDetailsDtoList[${index}].isCheck"
                               title="" type="checkbox" class="align-middle"
                               value="Y" ${investigationReasonDetailsDto.isCheck=='Y'?"checked":""} />
                        <span class="checkmark"></span>
                        <span class="custom-control-description"></span>
                    </label>
                </div>
            </div>
            <c:set var="index" value="${index+1}"/>
        </c:forEach>
        <input type="hidden" name="investigationReasonCount" value="${index}">
        <div class="form-group row">
            <div class="col-md-8">
                <div id="accordion">
                    <div class="card ">
                        <div class="card-header p-0" id="efiledetails1"
                             style="background-color: rgba(255, 235, 59, 0.5) !important;">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_efiledetails1"
                                   aria-expanded="false" aria-controls="col_efiledetails1">
                                    Documents
                                </a>
                            </h5>
                        </div>
                        <div id="col_efiledetails1" class="collapse" aria-labelledby="efiledetails1"
                             data-parent="#accordion">
                            <div class="card-body p-1">
                                <div class="row">
                                    <div class="col-12 pdf-thumbnails">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <span>Police report</span>
                                            </div>
                                            <div>
                                                <c:forEach var="document"
                                                           items="${investigationDetailsFormDto.policeReportList}">
                                                    <c:set var="iconColorCls" value=" text-dark "/>
                                                    <c:if test="${document.documentStatus=='A'}">
                                                        <c:set var="iconColorCls" value=" text-success"/>
                                                    </c:if>
                                                    <c:if test="${document.documentStatus=='H'}">
                                                        <c:set var="iconColorCls" value=" text-warning"/>
                                                    </c:if>
                                                    <c:if test="${document.documentStatus=='R'}">
                                                        <c:set var="iconColorCls" value=" text-danger"/>
                                                    </c:if>
                                                    <div class="uploadfile-delet " style="width: 40px; height: 40px;">
                                                        <a href="${pageContext.request.contextPath}/ClaimHandlerController/documentViewer?refNo=${document.refNo}&jobRefNo=${document.jobRefNo}&PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}"
                                                           class="investigationView${document.refNo} ${iconColorCls}">
                                                            <i class="fa fa-file-pdf-o fa-2x m-3"></i>
                                                        </a>
                                                    </div>
                                                    <script type="text/javascript">
                                                        $('.investigationView${document.refNo}').popupWindow({
                                                            height: screen.height,
                                                            width: screen.width,
                                                            centerBrowser: 0,
                                                            left: 0,
                                                            resizable: 1,
                                                            centerScreen: 1,
                                                            scrollbars: 1,
                                                            windowName: 'claimHandler${document.refNo}'
                                                        });
                                                    </script>
                                                </c:forEach>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <span>Driving Licence</span>
                                            </div>
                                            <div>
                                                <c:forEach var="document"
                                                           items="${investigationDetailsFormDto.drivenLicenseDocumentList}">
                                                    <c:set var="iconColorCls" value=" text-dark "/>
                                                    <c:if test="${document.documentStatus=='A'}">
                                                        <c:set var="iconColorCls" value=" text-success"/>
                                                    </c:if>
                                                    <c:if test="${document.documentStatus=='H'}">
                                                        <c:set var="iconColorCls" value=" text-warning"/>
                                                    </c:if>
                                                    <c:if test="${document.documentStatus=='R'}">
                                                        <c:set var="iconColorCls" value=" text-danger"/>
                                                    </c:if>
                                                    <div class="uploadfile-delet " style="width: 40px; height: 40px;">
                                                        <a href="${pageContext.request.contextPath}/ClaimHandlerController/documentViewer?refNo=${document.refNo}&jobRefNo=${document.jobRefNo}&PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}"
                                                           class="investigationView${document.refNo} ${iconColorCls}">
                                                            <i class="fa fa-file-pdf-o fa-2x m-3"></i>
                                                        </a>
                                                    </div>
                                                    <script type="text/javascript">
                                                        $('.investigationView${document.refNo}').popupWindow({
                                                            height: screen.height,
                                                            width: screen.width,
                                                            centerBrowser: 0,
                                                            left: 0,
                                                            resizable: 1,
                                                            centerScreen: 1,
                                                            scrollbars: 1,
                                                            windowName: 'claimHandler${document.refNo}'
                                                        });
                                                    </script>
                                                </c:forEach>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <span>Claim Form</span>
                                            </div>
                                            <div>
                                                <c:forEach var="document"
                                                           items="${investigationDetailsFormDto.estimateDocumentList}">
                                                    <c:set var="iconColorCls" value=" text-dark "/>
                                                    <c:if test="${document.documentStatus=='A'}">
                                                        <c:set var="iconColorCls" value=" text-success"/>
                                                    </c:if>
                                                    <c:if test="${document.documentStatus=='H'}">
                                                        <c:set var="iconColorCls" value=" text-warning"/>
                                                    </c:if>
                                                    <c:if test="${document.documentStatus=='R'}">
                                                        <c:set var="iconColorCls" value=" text-danger"/>
                                                    </c:if>
                                                    <div class="uploadfile-delet " style="width: 40px; height: 40px;">
                                                        <a href="${pageContext.request.contextPath}/ClaimHandlerController/documentViewer?refNo=${document.refNo}&jobRefNo=${document.jobRefNo}&PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}"
                                                           class="investigationView${document.refNo} ${iconColorCls}">
                                                            <i class="fa fa-file-pdf-o fa-2x m-3"></i>
                                                        </a>
                                                    </div>
                                                    <script type="text/javascript">
                                                        $('.investigationView${document.refNo}').popupWindow({
                                                            height: screen.height,
                                                            width: screen.width,
                                                            centerBrowser: 0,
                                                            left: 0,
                                                            resizable: 1,
                                                            centerScreen: 1,
                                                            scrollbars: 1,
                                                            windowName: 'claimHandler${document.refNo}'
                                                        });
                                                    </script>
                                                </c:forEach>

                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>

        <div class="form-group row">
            <div class="col-md-8">
                <div id="accordion2">
                    <div class="card ">
                        <div class="card-header p-0" id="efiledetails2"
                             style="background-color: rgba(255, 235, 59, 0.5) !important;">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_efiledetails2"
                                   aria-expanded="false" aria-controls="col_efiledetails2">
                                    Images
                                </a>
                            </h5>
                        </div>
                        <div id="col_efiledetails2" class="collapse" aria-labelledby="efiledetails2"
                             data-parent="#accordion">
                            <div class="card-body p-1" id="investigationImagesContainer">

                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>


        <div class="form-group row">
            <label class="col-form-label col-sm-4">Reason <span class="text-danger font-weight-bold"> *</span>
                : </label>
            <div class="col-sm-8">
    <textarea class="form-control" name="reason" id="reason"
              rows="5">${investigationDetailsDto.reason}</textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Investigation Assigned to <span
                    class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8">
                <select name="assignInvestigatorUserRefId" id="assignInvestigatorUserRefId"
                        class="form-control form-control-sm" onchange="updateInvestigatorDetails()">
                    <option value="">-- Please Select --</option>
                    <c:forEach var="listDto" items="${investigatorDetailsList}">
                        <option value="${listDto.refNo}">${listDto.name}</option>
                    </c:forEach>
                </select>
                <script type="text/javascript">
                    $("#assignInvestigatorUserRefId").val('${investigationDetailsDto.assignInvestigatorUserRefId}');
                </script>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Investigator Email :</label>
            <span class="label_Value col-md-8 ml-0" id="investigatorEmail"></span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Investigator Mobile No :</label>
            <span class="label_Value col-md-8 ml-0" id="investigatorMobile"></span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Assigned User & Date/Time :</label>
            <span class="label_Value col-md-4 ml-0">${investigationDetailsDto.investArrangeUser}</span>
            <span class="label_Value col-md-4 ml-0">${investigationDetailsDto.investArrangeDateTime}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Completed User & Date/Time :</label>
            <span class="label_Value col-md-4 ml-0">${investigationDetailsDto.investCompletedUser}</span>
            <span class="label_Value col-md-4 ml-0">${investigationDetailsDto.investCompletedDateTime}</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Investigator Accepting the Assignment :</label>
            <span class="label_Value col-md-4 ml-0 text-dark">..............................................</span>
            <span class="label_Value col-md-4 ml-0 text-dark">..............................................</span>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Important :</label>
            <div class="col-sm-8">
                <p class="text-danger">If Audio / Video evidence is part of the
                    investigation report, an affidavit confirming the authenticity of the
                    recording has to be
                    provided by the investigator along with the recording.</p>
            </div>
        </div>
        <div class="form-group row d-none">
            <label class="col-sm-4 col-form-label">Documents (Copies) Attached :</label>
            <div class="col-sm-8">
                <div class="row">
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-4 col-form-label check-container">
                        <input type="checkbox" class="align-middle">
                        <input name="isNoDamage" value="N" disabled="">
                        <span class="checkmark"></span>
                        <span class="custom-control-description">First Statement </span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-4 col-form-label check-container">
                        <input name="chkIsHugeDamage" title="Huge Damage" type="checkbox"
                               class="align-middle">
                        <input name="isHugeDamage">
                        <span class="checkmark"></span>
                        <span class="custom-control-description">Driving Licence</span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-4 col-form-label check-container">
                        <input name="chkIsHugeDamage" title="Huge Damage" type="checkbox"
                               class="align-middle">
                        <input name="isHugeDamage">
                        <span class="checkmark"></span>
                        <span class="custom-control-description">Claim Form</span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-4 col-form-label check-container">
                        <input name="chkIsHugeDamage" title="Huge Damage" type="checkbox"
                               class="align-middle">
                        <input name="isHugeDamage">
                        <span class="checkmark"></span>
                        <span class="custom-control-description">Photos</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <div class="col-sm-6">
                <label class="col-form-label label_Value float-left">Date/Time :
                    <span> ${investigationDetailsDto.investArrangeDateTime}</span></label>
            </div>
            <div class="col-sm-6">
                <label class="col-form-label label_Value float-left">Job No
                    :<span> ${investigationDetailsDto.investJobNo}</span></label>
            </div>
        </div>
        <div class="mt-3 clearfix ">
            <c:if test="${ investigationDetailsDto.investigationStatus=='CH_REQ_INVEST'
                      || investigationDetailsDto.investigationStatus=='INVEST_APPROVED'
                      ||investigationDetailsDto.investigationStatus=='DM_REQ_INVEST'}">
                <button type="button" onclick="printInvestigation()" class="btn btn-success"
                        style="margin-left: 10px;">
                    <i class="fa fa-print" style="margin-right: 10px"></i>
                    Print Investigation
                </button>
            </c:if>

            <c:choose>
                <c:when test="${(sessionClaimUserTypeDto.claimHandlerUser==true || sessionClaimUserTypeDto.offerTeamClaimHandlerUser==true)  }">

                    <c:if test="${(investigationDetailsDto.investigationStatus=='N'|| investigationDetailsDto.investigationStatus=='CH_REQ_INVEST' ||  investigationDetailsDto.investigationStatus=='INVEST_APPROVED'  || investigationDetailsDto.investigationStatus=='AR')}">
                        <button id="cmdSave" class="btn btn-primary float-right ml-3" style="display: none;"
                                type="submit" value="AR">
                            Submit
                        </button>

                    </c:if>
                    <c:if test="${(investigationDetailsDto.investigationStatus=='CH_REQ_INVEST')}">
                        <button id="cmdCancel" class="btn btn-danger float-right ml-3" style="display: none;"
                                type="submit"
                                value="CAN"> Cancel
                        </button>
                    </c:if>
                </c:when>
                <c:when test="${sessionClaimUserTypeDto.decisionMaker==true}">

                    <c:if test="${(investigationDetailsDto.investigationStatus=='N'
                      || investigationDetailsDto.investigationStatus=='CH_REQ_INVEST'
                      || investigationDetailsDto.investigationStatus=='INVEST_APPROVED'
                      ||investigationDetailsDto.investigationStatus=='DM_REQ_INVEST')
                      && TYPE!=10}">
                        <c:if test="${investigationDetailsDto.investigationStatus!='DM_REQ_INVEST'}">
                            <button id="cmdSave" class="btn btn-primary float-right ml-3" style="display: none;"
                                    type="submit" value="AR">
                                Submit
                            </button>
                        </c:if>

                        <c:if test="${(investigationDetailsDto.investigationStatus=='CH_REQ_INVEST' || investigationDetailsDto.investigationStatus=='DM_REQ_INVEST')}">
                            <button id="cmdCancel" class="btn btn-danger float-right ml-3" style="display: none;"
                                    type="submit"
                                    value="CAN"> Cancel
                            </button>
                        </c:if>
                    </c:if>
                </c:when>

                <c:when test="${sessionClaimUserTypeDto.twoPanelUser==true || sessionClaimUserTypeDto.fourPanelUser==true }">
                    <c:if test="${(investigationDetailsDto.investigationStatus=='DM_REQ_INVEST')}">
                        <button id="cmdSave" class="btn btn-primary float-right ml-3" style="display: none;"
                                type="submit" value="AR">
                            Submit
                        </button>
                        <button id="cmdCancel" class="btn btn-danger float-right ml-3" style="display: none;"
                                type="submit"
                                value="CAN"> Cancel
                        </button>
                    </c:if>
                </c:when>
                <c:otherwise>

                </c:otherwise>
            </c:choose>

        </div>
    </div>
    <div class="modal fade slideInDown animated" id="exampleModal" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Investigator's Payment Sheet</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                            onclick="closePaymentModal();">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <hr class="my-2">
                            <div class="form-group row">
                                <div class="col-sm-4">Investigator's Name :</div>
                                <span class="label_Value col-md-8 ml-0">${investigationDetailsDto.assignInvestigatorName}</span>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Payment Status :</label>
                                <div class="col-sm-8">
                                    <c:choose>
                                        <c:when test="${investigationDetailsDto.paymentStatus=='A'}">
                                            <span class="label_Value col-md-8 ml-0 text-success">Completed</span>
                                        </c:when>
                                        <c:otherwise>
                                            <span class="label_Value col-md-8 ml-0 text-warning">Pending</span>
                                        </c:otherwise>
                                    </c:choose>

                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Professional Fee :</label>
                                <div class="col-sm-8">
                                    <input name="profFee" id="profFee" class="form-control form-control-sm text-right"
                                           value="${investigationDetailsDto.profFee}"
                                           type="text" onkeyup="calculateInvestigation();">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Traveling Fee :</label>
                                <div class="col-sm-8">
                                    <input name="travelFee" id="travelFee"
                                           class="form-control form-control-sm text-right"
                                           value="${investigationDetailsDto.travelFee}"
                                           type="text" onkeyup="calculateInvestigation();">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Other Fee :</label>
                                <div class="col-sm-8">
                                    <input name="otherFee" id="otherFee" class="form-control form-control-sm text-right"
                                           value="${investigationDetailsDto.otherFee}"
                                           type="text" onkeyup="calculateInvestigation();">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Total Amount :</label>
                                <div class="col-sm-8">
                                    <input name="totalFee" id="totalFee" class="form-control form-control-sm text-right"
                                           value="${investigationDetailsDto.totalFee}"
                                           type="text" readonly>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closePaymentModal();">
                        Close
                    </button>
                    <c:if test="${sessionClaimHandlerDto.investigationArrangeUserId==G_USER.userId}">
                        <c:if test="${(investigationDetailsDto.investigationStatus=='AR')}">
                            <button id="cmdComplete" type="submit" value="C" class="btn btn-primary"
                                    style="display: none;">
                                Complete Investigation
                            </button>
                        </c:if>
                    </c:if>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade slideInDown animated" id="investigationModal" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document" style="max-width: 1000px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="investigationModalLabel">Investigation Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="claimInvestigationIndividualContainer"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</form>

<form name="frmInvestigationFee" id="frmInvestigationFee">
    <input name="investTxnNo" id="investTxnNo1" type="hidden" value="">
    <input name="assignInvestigatorUserRefId" id="assignInvestigatorUserRefId1" type="hidden" value="">
    <input name="claimNo" type="hidden" value="${sessionClaimHandlerDto.claimsDto.claimNo}">
    <div class="modal fade slideInDown animated" id="investigationFeeModal" tabindex="-1" role="dialog"
         aria-labelledby="investigationFeeModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="investigationFeeModalLabel">Investigator's Payment Sheet</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                            onclick="">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <hr class="my-2">
                            <div class="form-group row">
                                <div class="col-sm-4">Investigator's Name :</div>
                                <span class="label_Value col-md-8 ml-0"
                                      id="spanAssignInvestigatorName">${investigationDetailsDto.assignInvestigatorName}</span>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Payment Status :</label>
                                <div class="col-sm-8">
                                    <span class="label_Value col-md-8 ml-0 text-success" id="spanPaymentStatus1"></span>
                                    <span class="label_Value col-md-8 ml-0 text-warning" id="spanPaymentStatus2"></span>

                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Professional Fee :</label>
                                <div class="col-sm-8">
                                    <input name="profFee" id="profFee1" class="form-control form-control-sm text-right"
                                           value=""
                                           type="text" onkeyup="calculateInvestigationPayment();">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Traveling Fee :</label>
                                <div class="col-sm-8">
                                    <input name="travelFee" id="travelFee1"
                                           class="form-control form-control-sm text-right"
                                           value="${investigationDetailsDto.travelFee}"
                                           type="text" onkeyup="calculateInvestigationPayment();">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Other Fee :</label>
                                <div class="col-sm-8">
                                    <input name="otherFee" id="otherFee1"
                                           class="form-control form-control-sm text-right"
                                           value="${investigationDetailsDto.otherFee}"
                                           type="text" onkeyup="calculateInvestigationPayment();">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Total Amount :</label>
                                <div class="col-sm-8">
                                    <input name="totalFee" id="totalFee1"
                                           class="form-control form-control-sm text-right"
                                           value="${investigationDetailsDto.totalFee}"
                                           type="text" readonly>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"
                            onclick="closePaymentUpdateModal()">
                        Close
                    </button>

                    <c:if test="${sessionClaimHandlerDto.investigationArrangeUserId==G_USER.userId}">
                        <button id="cmdPaymentUpdate" type="button" value="C" class="btn btn-primary"
                                style="display: none;" onclick="updatePayment()">
                            Update Payment
                        </button>

                    </c:if>
                </div>
            </div>
        </div>
    </div>
</form>


<script>
    var type = "${TYPE}";
    var isTwoPanelUser =${sessionClaimUserTypeDto.twoPanelUser};
    var isFourPanelUser =${sessionClaimUserTypeDto.fourPanelUser};
    var isDecisionMaker =${sessionClaimUserTypeDto.decisionMaker};
    var isClaimHandlerUser =${sessionClaimUserTypeDto.claimHandlerUser};
    var isOfferTeamClaimHandlerUser =${sessionClaimUserTypeDto.offerTeamClaimHandlerUser};
    var isTotalLossClaimHandlerUser =${sessionClaimUserTypeDto.totalLossClaimHandlerUser};

    buttonShowHide('${investigationDetailsDto.investigationStatus}');

    $('#investigationStatus').change(function () {
        var investigationReportStatus = "${investigationReportStatus.documentStatusEnum}";
        var selected = $("#investigationStatus option:selected").val();
        buttonShowHide(selected);
        if (selected === "C") {

            if (investigationReportStatus == "NO_VALID_DOCUMENT") {
                bootbox.alert({
                    message: "Please upload investigation report",
                    callback: function () {
                        closePaymentModal();
                    }
                });
                return;
            } else if (investigationReportStatus == "PENDING") {
                bootbox.alert({
                    message: "Please check all upload investigation reports",
                    callback: function () {
                        closePaymentModal();
                    }
                });
                return;
            } else {
                showPaymentModel();
            }
        }

    });

    function closePaymentModal() {
        $("#investigationStatus").val("AR");
    }

    function closePaymentUpdateModal() {
        loadInvestigationView();
    }

    function buttonShowHide(selected) {
        $('#cmdSave').hide();
        $('#cmdComplete').hide();
        $('#cmdCancel').hide();
        if (selected === "C") {
            $('#cmdComplete').show();
        } else if (selected === "CAN") {
            $('#cmdCancel').show();
        } else if (((selected === "CH_REQ_INVEST" || selected === "INVEST_APPROVED" || selected === "AR") && (isClaimHandlerUser || isTotalLossClaimHandlerUser || isOfferTeamClaimHandlerUser))
            || (selected === "DM_REQ_INVEST" && isDecisionMaker)
            || (selected === "INVEST_APPROVED" && (isTwoPanelUser || isFourPanelUser))
            || (selected === "AR" && isDecisionMaker)
        ) {
            if (type == 10) {
                $('#cmdSave').hide();
            } else {
                $('#cmdSave').show();
            }

        }
    }

    function showPaymentModel() {
        $('#exampleModal').modal('show');
    }

    function showUpdatePaymentModel(investTxnId) {
        var url = contextPath + "/ClaimHandlerController/viewInvestigationPayment?P_INVEST_TXN_ID=" + investTxnId;
        var claimNo = '${sessionClaimHandlerDto.claimsDto.claimNo}';
        showLoader();
        var formData = $('#frmInvestigationFee').serialize();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != "") {
                    $("#spanAssignInvestigatorName").html(obj.investigationDetailsDto.assignInvestigatorName);
                    if (obj.investigationDetailsDto.paymentStatus == "A") {
                        $("#spanPaymentStatus1").html("Completed");
                        $('#cmdPaymentUpdate').hide();
                    } else {
                        $("#spanPaymentStatus2").html("Pending");
                        $('#cmdPaymentUpdate').show();

                    }
                    $("#investTxnNo1").val(obj.investigationDetailsDto.investTxnNo);
                    $("#profFee1").val(obj.investigationDetailsDto.profFee);
                    $("#travelFee1").val(obj.investigationDetailsDto.travelFee);
                    $("#otherFee1").val(obj.investigationDetailsDto.otherFee);
                    $("#totalFee1").val(obj.investigationDetailsDto.totalFee);
                    $("#assignInvestigatorUserRefId1").val(obj.investigationDetailsDto.assignInvestigatorUserRefId);

                    $('#investigationFeeModal').modal('show');
                } else {

                }
                hideLoader();
            }
        });


    }

    function updatePayment() {
        var url = contextPath + "/ClaimHandlerController/updateInvestigationPayment";
        var claimNo = '${sessionClaimHandlerDto.claimsDto.claimNo}';
        showLoader();
        var formData = $('#frmInvestigationFee').serialize();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != "") {
                    notify(obj, "success");
                    loadInvestigationView();
                    loadClaimStampContainer(claimNo);
                } else {
                    notify("Can not be updated", "danger");
                }
                hideLoader();
            }
        });
    }

    function showIndividualInvestigation(investTxnId) {
        $("#claimInvestigationIndividualContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewInvestigationIndividual?P_INVEST_TXN_ID=" + investTxnId);
        $('#investigationModal').modal('show');

    }


    function updateInvestigation(type) {

        var url;
        if (type == 'AR') {
            url = contextPath + "/ClaimHandlerController/updateArrangeInvestigation";
        } else if (type == 'C') {
            url = contextPath + "/ClaimHandlerController/updateCompleteInvestigation";
        } else if (type == 'CAN') {
            url = contextPath + "/ClaimHandlerController/updateCancelInvestigation";
        }
        var claimNo = '${sessionClaimHandlerDto.claimsDto.claimNo}';
        showLoader();
        var formData = $('#frmInvestigation').serialize();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != "" && obj != "USER_NOT_FOUND") {
                    notify(obj, "success");
                    //loadGenerateReminderView();
                    // loadDefineDocumentView();
                    // loadDocumentUploadView();
                    loadInvestigationView();
                    loadClaimStampContainer(claimNo);
                } else if (obj == "USER_NOT_FOUND") {
                    notify("User not found to assign claim", "danger");
                } else {
                    notify("Can not be updated", "danger");
                }
                hideLoader();
            }
        });
    }

    function calculateInvestigation() {
        var url = contextPath + "/ClaimHandlerController/calculateInvestigationPayment";

        var formData = $('#frmInvestigation').serialize();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != "") {
                    $("#profFee").val(obj.profFee);
                    $("#travelFee").val(obj.travelFee);
                    $("#otherFee").val(obj.otherFee);
                    $("#totalFee").val(obj.totalFee);
                } else {

                }
            }
        });
    }

    function calculateInvestigationPayment() {
        var url = contextPath + "/ClaimHandlerController/calculateInvestigationPayment";

        var formData = $('#frmInvestigationFee').serialize();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != "") {
                    $("#profFee1").val(obj.profFee);
                    $("#travelFee1").val(obj.travelFee);
                    $("#otherFee1").val(obj.otherFee);
                    $("#totalFee1").val(obj.totalFee);
                } else {

                }
            }
        });
    }


</script>

<script>
    //Investigation validation
    $(document).ready(function () {
        loadInvestigationImagesView();
        var fv = $('#frmInvestigation')
            .formValidation({
                framework: 'bootstrap',
                excluded: ':disabled',
                icon: {
                    valid: 'glyphicon glyphicon-ok',
                    invalid: 'glyphicon glyphicon-remove',
                    validating: 'glyphicon glyphicon-refresh'
                },
                fields: {
                    /*'languages[]': {
                    validators: {
                    choice: {
                    min: 2,
                    max: 4,
                    message: 'Please choose 2 - 4 programming languages you are good at'
                    }
                    }
                    },*/
                    reason: {
                        validators: {
                            notEmpty: {
                                message: 'This field is required and cannot be empty.'
                            }
                        }
                    }, investigationStatus: {
                        validators: {
                            callback: dropDownInvestigationValidation
                        }
                    }, assignInvestigatorUserRefId: {
                        validators: {
                            callback: dropDownValidationForText
                        }
                    }

                }
            })

            .on('success.form.fv', function (e) {

                // Prevent form submission
                e.preventDefault();
                var $form = $(e.target); // Form instance
                // Get the clicked button
                var $button = $form.data('formValidation').getSubmitButton();
                var type = $button.attr('value');
                if ("AR" == type) {
                    bootbox.confirm({
                        message: "Do you want to submit this investigation details?",
                        buttons: {
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            },
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            }
                        },
                        callback: function (result) {
                            if (result) {
                                updateInvestigation('AR');
                            } else {
                                // fv.revalidateField('reason');
                                $('#frmInvestigation').formValidation('revalidateField', 'investigationStatus');
                            }
                        }
                    });

                } else if ("C" == type) {
                    bootbox.confirm({
                        message: "Do you want to complete this investigation details?",
                        buttons: {
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            },
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            }
                        },
                        callback: function (result) {
                            if (result) {
                                updateInvestigation('C');
                            } else {
                                // fv.revalidateField('reason');
                                $('#frmInvestigation').formValidation('revalidateField', 'investigationStatus');
                            }
                        }
                    });


                } else if ("CAN" == type) {
                    bootbox.confirm({
                        message: "Do you want to Cancel this investigation details?",
                        buttons: {
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            },
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            }
                        },
                        callback: function (result) {
                            if (result) {
                                updateInvestigation('CAN');
                            } else {
                                // fv.revalidateField('reason');
                                $('#frmInvestigation').formValidation('revalidateField', 'investigationStatus');
                            }
                        }
                    });

                }
            });
    });

    var dropDownInvestigationValidation = {
        message: 'This field is required.&nbsp;&nbsp;',
        callback: function (value, validator, $field) {
            return value != 'N';
        }
    };

    function viewImageModal() {
        $('#imagesModal').modal('show');
    }

    $("#closeModal").click(function () {
        $('#imagesModal').modal('hide');
    });


    function printInvestigation() {
        var claimNo = '${sessionClaimHandlerDto.claimsDto.claimNo}';
        var myWindow = window.open(contextPath + "/ClaimReportController/investigationPrintView?claimNo=" + claimNo);
        myWindow.focus();

    }

    function loadInvestigationImagesView() {
        $("#investigationImagesContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewInvestigationImageList?P_N_CLIM_NO=${sessionClaimHandlerDto.claimsDto.claimNo}");
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    }

    // Investigator details data
    var investigatorDetailsMap = {};
    <c:forEach var="investigator" items="${investigatorDetailsList}">
        investigatorDetailsMap['${investigator.refNo}'] = {
            email: '${investigator.email}',
            mobile: '${investigator.mobile}'
        };
    </c:forEach>

    function updateInvestigatorDetails() {
        var selectedRefNo = $("#assignInvestigatorUserRefId").val();
        var emailElement = $("#investigatorEmail");
        var mobileElement = $("#investigatorMobile");

        if (selectedRefNo && investigatorDetailsMap[selectedRefNo]) {
            var investigator = investigatorDetailsMap[selectedRefNo];
            emailElement.text(investigator.email || 'N/A');
            mobileElement.text(investigator.mobile || 'N/A');
        } else {
            emailElement.text('');
            mobileElement.text('');
        }
    }

    $(document).ready(function() {
        updateInvestigatorDetails();
    });
</script>
