<%@ taglib prefix="c" uri="jakarta.tags.core" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
    int TYPE = 0;
    String error = "";

    try {
        session.removeAttribute("TYPE");
        String typeParam = request.getParameter("TYPE");
        if (typeParam != null) {
            TYPE = Integer.parseInt(typeParam);
        }
        session.setAttribute("TYPE", TYPE);
    } catch (Exception e) {
        error = "Invalid TYPE parameter.";
    }
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>

    <link rel="shortcut icon" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/resources/css/jquery-ui.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/resources/css/ScrollTabla.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/resources/css/datatables.min.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet"/>

    <script src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/datatables.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/dataTables.fixedHeader.min.js"></script>

    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp" />

    <script type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        console.log("claimList...loading " + contextPath)
        var currentDate = moment('${Current_Date}', 'YYYY-MM-DD HH:mm:ss');

        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm:ss',
                maxDate: currentDate,
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm:ss',
                maxDate: currentDate,
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            // Optional: when selecting from date, set min on toDate
            $('#txtFromDate').on('dp.change', function (e) {
                $('#txtToDate').data('DateTimePicker').minDate(e.date);
            });

            // Clear values on page load (make fields empty by default)
            $('#txtFromDate').val('');
            $('#txtToDate').val('');
        });

    </script>


    </script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form id="frmForm" method="post">
        <input type="hidden" name="P_POL_N_REF_NO" id="P_POL_N_REF_NO"/>
        <input type="hidden" name="P_N_CLIM_NO" id="P_N_CLIM_NO"/>
        <input type="hidden" name="type" id="type" value="<%= TYPE %>"/>

        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Onsite Review List</h5>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote"><%= error %>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="row">
            <div class="col-md-12">
                <div class="accordion" id="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <!-- Left -->
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label">From Input Date/Time</label>
                                            <div class="col-sm-8">
                                                <input type="text" id="txtFromDate" name="txtFromDate"
                                                       class="form-control form-control-sm"
                                                       placeholder="From Date" value=""/>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label">Claim Number</label>
                                            <div class="col-sm-8">
                                                <input type="text" id="txtClaimNumber" name="txtClaimNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Claim Number" value=""/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right -->
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label">To Input Date/Time</label>
                                            <div class="col-sm-8">
                                                <input type="text" id="txtToDate" name="txtToDate"
                                                       class="form-control form-control-sm"
                                                       placeholder="To Date" value=""/>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtStatus" class="col-sm-4 col-form-label">Status</label>
                                            <div class="col-sm-8">
                                                <select id="txtStatus" name="txtStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">ALL</option>
                                                    <option value="P">PENDING</option>
                                                    <option value="A">ASSIGN</option>
                                                    <option value="C">COMPLETE</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Buttons -->
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button type="button" class="btn btn-primary" id="cmdSearch" onclick="search()">Search</button>
                                        <a href="${pageContext.request.contextPath}/welcome.do"
                                           class="btn btn-secondary" id="cmdClose">Close</a>
                                        <hr/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div id="but_cont" class="float-right"></div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <div class="row">
                    <div class="col-md-12">
                        <hr class="my-2"/>
                        <h6>Claim Result</h6>
                        <div class="mt-2" style="overflow-x: auto">
                            <table id="onsite_review_tbl" class="table table-sm table-hover" cellspacing="0" style="cursor:pointer">
                                <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Claim No</th>
                                    <th>RTE User ID</th>
                                    <th>Assigned User</th>
                                    <th>Assessor ID</th>
                                    <th>Onsite Review</th>
                                    <th>Status</th>
                                    <th>Submitted Datetime</th>
                                    <th>Submitted User</th>
                                    <th>Last Updated Datetime</th>
                                    <th>Last Updated User</th>
                                    <th>Rejected Reason</th>
                                    <th>Reject Count</th>
                                    <th class="min-mobile"></th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script src="${pageContext.request.contextPath}/resources/js/custom/callcenter/onsite-review-datatables.js"></script>
<script>
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });
</script>
</body>
</html>
