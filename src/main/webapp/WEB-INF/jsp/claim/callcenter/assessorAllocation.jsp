<%--
  Created by IntelliJ IDEA.
  User: Asiri
  Date: 3/13/2018
  Time: 4:34 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%
    String strInsepctionTypes = DbRecordCommonFunction.getInstance().
            getPopupList("claim_inspection_type ", "inspection_type_id", "inspection_type_desc", "inspection_type_id <> 0", "");

    String strInsepctionReasonTypes = DbRecordCommonFunction.getInstance().
            getPopupList("claim_inspection_type_reason ", "N_ID", "V_REASON", "V_REC_STATUS='A' AND N_ID <>0 ", "");

    String strRejectedReason = DbRecordCommonFunction.getInstance().
            getPopupList("claim_assessor_reject_reason ", "N_ID", "V_REASON", "V_REC_STATUS='A' AND N_ID <>0   ", "");

    String directRejectReason = DbRecordCommonFunction.getInstance().
            getPopupList("claim_assessor_reject_reason", "N_ID", "V_REASON", "V_REC_STATUS='C' AND N_ID <> 0", "");
%>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/assessorallocation/assessor-allocation.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/assessorallocation/assessor-form-validations.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>

</head>
<body class="scroll">
<form id="frmMain" name="frmMain" method="post">
    <input type="hidden" name="oldJobId" id="oldJobId"/>
    <input type="hidden" name="previousJobId" id="previousJobId" value="${assessorAllocationDto.previousJobId}"/>
    <input type="hidden" name="previousRefId" id="previousRefId" value="${assessorAllocationDto.previousRefId}"/>
    <input type="hidden" name="inspectionId" id="inspectionId"/>
    <input type="hidden" name="assignedUser" id="assignedUser"/>
    <input type="hidden" name="claimId" id="claimId" value="${claimId}"/>
    <div class="col-md-12 assessor-allocation-tab-content">
        <div class="form-group row">
            <%--<label class="col-sm-3 col-form-label">Intimation No : </label>--%>
            <%--<div class="col-sm-3">--%>
            <%--<label class="col-form-label"></label>--%>
            <%--</div>--%>
            <label class="col-sm-3 col-form-label">CMS Claim No :</label>
            <div class="col-sm-3">
                <label class="col-form-label">${assessorAllocationDto.claimsDto.claimNo}</label>
            </div>
            <label class="col-sm-3 col-form-label">ACR Amount :</label>
            <div class="col-sm-3">
                <label class="col-form-label">
                    <c:choose>
                        <c:when test="${assessorAllocationDto.acrAmount != null}">
                            <fmt:formatNumber value="${assessorAllocationDto.acrAmount}" type="currency" currencySymbol="Rs. " maxFractionDigits="2"/>
                        </c:when>
                        <c:otherwise>
                            Rs. 0.00
                        </c:otherwise>
                    </c:choose>
                </label>
            </div>
            <%--<label class="col-sm-3 col-form-label">Azentio Claim No :</label>--%>
            <%--<div class="col-sm-3">--%>
            <%--<label class="col-form-label"></label>--%>
            <%--</div>--%>
        </div>
        <c:if test="${onsiteReview == true}">
            <!-- Disabled checkbox for display, checked, value should be "Y" or something meaningful -->
            <input type="checkbox" id="isOnsiteReview" name="onsiteReview" value="true" checked onclick="return false;"> Onsite Review

            <input type="hidden" id="requestedInspectionId" name="requestedInspectionId" value="${requestedInspectionId}" />

            <!-- Make sure to wrap requestedInspectionId in quotes to pass as a string -->
                <button
                        type="button"
                        id="assignUsersBtn"
                        style="background-color:#015AAA; color:white; border:none; padding:6px 12px; border-radius:5px; font-size:14px; cursor:pointer; margin-left: 30%; margin-bottom: 12px;"
                        onclick="assignOnsiteReviewJob('${requestedInspectionId}')"
                >
                    Assign to Me
                </button>
        </c:if>

        <!-- Hidden fields: Make sure these have 'name' attributes so they get submitted -->
        <input type="hidden" id="assignUser" name="assignUser" value="${G_USER.userId}" />
        <input type="hidden" id="claimNo" name="claimNo" value="${claimId}" />

        <!-- Hide Submit button initially if onsiteReview is true -->
        <c:if test="${onsiteReview == true && !isOnsiteReviewAssignedUser}">
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    var submitBtn = document.getElementById('addbtn');
                    if (submitBtn) {
                        submitBtn.style.display = 'none';
                    }
                });
            </script>
        </c:if>







        <fieldset class="border p-2">
            <h6> Inspection Details</h6>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Inspection<span
                        class="text-danger font-weight-bold"> *</span> :</label>
                <div class="col-sm-8">
                    <select id="InspectionType" class="form-control form-control-sm" name="inspectionDto.inspectionId" onchange="checkInspectionType()">
                        <option value="0">Please Select One</option>
                        <c:choose>
                            <c:when test="${onsiteReview}">
                                <c:forEach var="listDto" items="${inspectionList}">
                                    <c:if test="${listDto.inspectionId == 1}">
                                        <option value="${listDto.inspectionId}">On Site Review Inspection</option>
                                    </c:if>
                                    <c:if test="${listDto.inspectionId == 2}">
                                        <option value="${listDto.inspectionId}">Off Site Review Inspection</option>
                                    </c:if>
                                </c:forEach>
                            </c:when>
                            <c:otherwise>
                                <c:forEach var="listDto" items="${inspectionList}">
                                    <option value="${listDto.inspectionId}">${listDto.inspectionValue}</option>
                                </c:forEach>
                            </c:otherwise>
                        </c:choose>
                    </select>


                    <script>
                        $("#InspectionType").val("${assessorAllocationDto.inspectionDto.inspectionId}");
                    </script>

                </div>
            </div>

            <div class="form-group row" id="priorityDiv">
                <label class="col-sm-4 col-form-label">Priority :</label>
                <div class="col-sm-8">
                    <input type="hidden" name="priority" id="priority" value="NORMAL"/>
                    <div class="row">
                        <label class="custom-control custom-checkbox m-0 mb-sm-0 ml-2 pl-3 pr-0 col col-form-label check-container first-container">
                            <input name="priorityHigh" id="priorityHigh" type="radio" class="align-middle" value="HIGH">
                            <span class="radiomark"></span>
                            <span class="custom-control-description">HIGH</span>
                        </label>
                        <label class="custom-control custom-checkbox m-0 mb-sm-0 pl-3 col-4 pr-0 col-form-label check-container ml-0">
                            <input checked name="priorityNormal" id="priorityNormal" type="radio" class="align-middle"
                                   value="NORMAL">
                            <span class="radiomark"></span>
                            <span class="custom-control-description">NORMAL</span>
                        </label>
                    </div>
                    <script>
                        $("#priorityHigh").click(function () {
                            if ($(this).is(":checked")) {
                                $("#priorityNormal").prop("checked", false);
                                $("#priority").val("HIGH");
                            } else {
                                $("#priorityNormal").prop("checked", true);
                            }
                        });

                        $("#priorityNormal").click(function () {
                            if ($(this).is(":checked")) {
                                $("#priorityHigh").prop("checked", false);
                                $("#priority").val("NORMAL");
                            } else {
                                $("#priorityHigh").prop("checked", true);
                            }
                        });
                    </script>
                </div>
            </div>

            <div class="form-group row" id="rteDiv" style="display: none">
                <label class="col-sm-4 col-form-label">RTE <span
                        class="text-danger font-weight-bold"> *</span>:</label>
                <div class="col-sm-8">
                    <select class=" form-control form-control-sm disable text-mute" id="rteCode"
                            name="rteCode">
                        <option value="">Please Select One</option>
                        <c:forEach var="listDto" items="${rteList}">
                            <option value="${listDto.userId}">${listDto.userId}</option>
                        </c:forEach>
                    </select>
                    <script>
                        $("#rteCode").val("${assessorAllocationDto.rteCode}");
                    </script>
                </div>
            </div>
            <div class="form-group row" id="reasonDiv" style="display:none">
                <label class="col-sm-4 col-form-label">Reason <span
                        class="text-danger font-weight-bold"> *</span>:</label>
                <div class="col-sm-8">
                    <select class=" form-control form-control-sm disable text-mute" id="inspectionReasonId"
                            name="inspectionReasonDto.reasonId">
                        <option value="0">Please Select One</option>
                        <%out.print(strInsepctionReasonTypes);%>
                    </select>
                    <script>
                        $("#inspectionReasonId").val("${assessorAllocationDto.inspectionReasonDto.reasonId}");
                    </script>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label"></label>
                <div class="col-sm-8" style="display: none">
                    <button type="button" name="vwclaim" id="vwclaim" value="add" class="btn btn-primary ">View Claim
                        History
                    </button>
                </div>
            </div>
            <div class="form-group row" id="partnerGarageDiv" style="display: none">
                <label class="col-sm-4 col-form-label"></label>
                <div class="col-sm-8">
                    <div class="row">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label">
                            <input name="isPartnerGarage" id="isPartnerGarage" type="checkbox" class="align-middle checkbox_check" value="Y"
                            ${assessorAllocationDto.isPartnerGarage eq 'Y' ? 'checked' : ''}/>
                            <span class="custom-control-indicator"></span>
                            <span class="custom-control-description">Partner Garage</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Place of Inspection <span
                        class="text-danger font-weight-bold"> *</span>:</label>
                <div class="col-sm-8">
                    <input name="placeOfinspection" id="inspplace" class=" form-control form-control-sm"
                           title="Place of Inspection" type="text"
                           value="${assessorAllocationDto.placeOfinspection}"/>

                </div>
            </div>
            <div class="form-group row" id="garageDiv" style="display: none">
                <label class="col-sm-4 col-form-label">Garage Contact No<span
                        class="text-danger font-weight-bold"> *</span>:</label>
                <div class="col-sm-8">
                    <input name="garageContactNo" id="garageContactNo" class=" form-control form-control-sm"
                           title="Garage Contact No" type="text"
                           value="${assessorAllocationDto.garageContactNo}"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">District :</label>
                <div class="col-sm-8">
                    <select class=" form-control form-control-sm disable text-mute" id="districtId"
                            name="districtDto.districtCode">
                        <option value="">Please Select One</option>
                        <c:forEach var="listDto" items="${districtList}">
                            <option value="${listDto.districtCode}">${listDto.districtName}</option>
                        </c:forEach>
                        <script>
                            $("#districtId").val("${assessorAllocationDto.districtDto.districtCode}");
                        </script>
                    </select>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Nearest Town :</label>
                <div class="col-sm-8 input-group">
                    <select class=" form-control form-control-sm disable text-mute" id="cityId"
                            name="cityDto.gramaCode">
                        <option value="0">Please Select One</option>
                        <c:forEach var="listDto" items="${cityList}">
                            <option value="${listDto.gramaCode}">${listDto.gramaName}</option>
                        </c:forEach>
                        <script>
                            $("#cityId").val("${assessorAllocationDto.cityDto.gramaCode}");
                        </script>
                    </select>
                </div>
            </div>
            <c:if test="${assessorAllocationDto.isReAssign == 'Y'}">
                <div class="form-group row" id="reassingReasonDiv">
                    <label class="col-sm-4 col-form-label">Reassigning Reason<span
                            class="text-danger font-weight-bold">  *</span> :</label>
                    <div class="col-sm-8 input-group">
                        <select class=" form-control form-control-sm disable text-mute" id="reassigningId"
                                name="reassigningReasonDto.id">
                        </select>
                    </div>
                </div>
            </c:if>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label"></label>
                <div class="col-sm-8">
                    <button type="button" name="vwclaim" id="viewDocUpload" value="add"
                            class="btn btn-primary "
                            onclick="uploadDocumets()">Upload
                        Document

                    </button>
                </div>
            </div>
        </fieldset>
        <fieldset class="border p-2 mt-3">
            <h6> Assessor Details</h6>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Assessor<span
                        class="text-danger font-weight-bold">  *</span> :</label>
                <div class="col-sm-8 input-group">
                    <select class=" form-control form-control-sm disable text-mute" id="assessorId"
                            name="assessorDto.code">
                        <option value="">Please Select One</option>
                        <c:forEach var="listDto" items="${assessorList}">
                            <option value="${listDto.code}">${listDto.firstName} ( ${listDto.name} )</option>
                        </c:forEach>
                    </select>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Contact No:</label>
                <div class="col-sm-8">
                    <input name="assessorDto.assessorContactNo" id="contactNo" maxlength="100"
                           class=" form-control form-control-sm" type="text"
                           value=""/>
                </div>
            </div>
            <div class="form-group row" hidden>
                <label class="col-sm-4 col-form-label"></label>
                <div class="col-sm-8">
                    <div class="row">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label">
                            <input name="" id="" class="align-middle" type="checkbox" value="Y"/>
                            <span class="custom-control-indicator"></span>
                            <span class="custom-control-description">Edit Contact No</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Assessment Type :</label>
                <div class="col-sm-8">
                    <div class="row">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label">
                            <input name="assessmentType" type="checkbox" class="align-middle checkbox_check" value="Y"/>
                            <span class="custom-control-indicator"></span>
                            <span class="custom-control-description">In-House</span>
                        </label>
                    </div>
                </div>
            </div>
<%--            <div class="form-group row">--%>
<%--                <label class="col-sm-4 col-form-label">Inspection Type :</label>--%>
<%--                <div class="col-sm-8">--%>
<%--                    <div class="row">--%>
<%--                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label">--%>
<%--                            <input name="typeOnlineInspection" type="checkbox" class="align-middle checkbox_check" value="Y"/>--%>
<%--                            <span class="custom-control-indicator"></span>--%>
<%--                            <span class="custom-control-description">Online Inspection</span>--%>
<%--                        </label>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--            </div>--%>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Response<span
                        class="text-danger font-weight-bold">  *</span>:</label>
                <div class="col-sm-8">
                    <div class="row">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label">
                            <input name="responseValue" value=24 type="radio" class="align-middle" checked
                                   onclick="hideReason()"/>
                            <span class="custom-control-indicator"></span>
                            <span class="custom-control-description">Accepted</span>
                        </label>
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label">
                            <input name="responseValue" value=23 type="radio" class="align-middle"
                                   onclick="showReason()"/>
                            <span class="custom-control-indicator"></span>
                            <span class="custom-control-description">Rejected</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group row" id="rejectReasonDiv" style="display: none">
                <label class="col-sm-4 col-form-label">Reason <span
                        class="text-danger font-weight-bold">  *</span>:</label>
                <div class="col-sm-8">
                    <select class=" form-control form-control-sm disable text-mute" id="rejectReasonId"
                            name="rejectReasonDto.reasonId">
                        <option value="0">Please Select One</option>
                        <%out.print(strRejectedReason);%>
                    </select>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Assessor's Current Location
                    <%--Of The Vehical--%>
                    :</label>
                <div class="col-sm-8">
                    <input name="currentLocation" class=" form-control form-control-sm"
                           id="txtV_place_of_Vehical" title="Assessor's Current Location"
                           maxlength="255"
                           type="text" value=""/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Assign Date & Time <span
                        class="text-danger font-weight-bold">  *</span>:</label>
                <div class="col-sm-8 input-group">
                    <input name="assignDatetime" class=" form-control form-control-sm"
                           id="assignDatetime" title="Accident Date" type="text"
                           value="${assessorAllocationDto.assignDatetime}"
                           style="width: 100px;"
                    />

                </div>
            </div>
            <div class="form-group row" hidden>
                <label class="col-sm-4 col-form-label">Customer Expected Date & Time :</label>
                <div class="col-sm-8 input-group">
                    <input name="txtD_expect_date" class=" form-control form-control-sm"
                           id="txtD_expect_date" title="Accident Date" type="text"
                           style="width: 100px;"
                    />
                    <strong class="p-1">&nbsp;&nbsp;</strong>
                    <select class=" form-control form-control-sm" name="ReportddlHour"
                            id="ReportddlHour">
                        <option selected="selected" value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                        <option value="12">12</option>
                    </select>
                    <strong class="p-1">:</strong>
                    <select class=" form-control form-control-sm" name="ReportddlMinutes"
                            id="ReportddlMinutes" style="width:40px;">
                        <%
                            for (int m = 0; m < 60; m++) {
                                out.print("<option value=\"" + UtilityBean.formatMinuts(m) + "\">" + UtilityBean.formatMinuts(m) + "</option>");
                            }
                        %>
                    </select>
                    <select class=" form-control form-control-sm" name="ReportddlAMPM"
                            id="ReportddlAMPM" style="width:40px;">
                        <option value="AM">AM</option>
                        <option selected="selected" value="PM">PM</option>
                    </select>
                    <%--<script type="text/javascript">--%>
                    <%--$("#ReportddlHour").value = "<%=UtilityBean.getHHmmAmPm(UtilityBean.sysDate("HH:mm"), "HH")%>";--%>
                    <%--$("#ReportddlMinutes").value = "<%=UtilityBean.getHHmmAmPm(UtilityBean.sysDate("HH:mm"), "MM")%>";--%>
                    <%--$("#ReportddlAMPM").value = "<%=UtilityBean.getHHmmAmPm(UtilityBean.sysDate("HH:mm"), "AMPM")%>";--%>
                    <%--</script>--%>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Duration <span
                        class="text-danger font-weight-bold">  *</span>:</label>
                <div class="col-sm-8 input-group">
                    <select class=" form-control form-control-sm" name="durationHours"
                            id="ddlHour">
                        <option value="00">0</option>
                        <option selected="selected" value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                        <option value="12">12</option>
                        <option value="13">13</option>
                        <option value="14">14</option>
                        <option value="15">15</option>
                        <option value="16">16</option>
                        <option value="17">17</option>
                        <option value="18">18</option>
                        <option value="19">19</option>
                        <option value="20">20</option>
                        <option value="21">21</option>
                        <option value="22">22</option>
                        <option value="23">23</option>
                        <option value="24">24</option>
                    </select>
                    <strong class="p-1">&nbsp;&nbsp;&nbsp;</strong>
                    <select class=" form-control form-control-sm" name="durationMinutes"
                            id="ddlMinutes" style="width:40px;">
                        <option value="00">00</option>
                        <option value="15">15</option>
                        <option value="30">30</option>
                        <option value="45">45</option>
                    </select>
                </div>
            </div>
            <div class="form-group row" hidden>
                <label class="col-sm-4 col-form-label">Actual Date & Time :</label>
                <div class="col-sm-8 input-group">
                    <input name="txtD_actul_date" class=" form-control form-control-sm"
                           id="txtD_actul_date" title="Accident Date" type="text"
                           style="width: 100px;"
                    <%--value="<%=currentSysDate %>"--%>
                    <%--onClick="partSelection('txtD_assin_date');"--%>
                    />
                    <strong class="p-1">&nbsp;&nbsp;</strong>
                    <select class=" form-control form-control-sm" name="ddlHour"
                            id="ddlHour">
                        <option selected="selected" value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                        <option value="12">12</option>
                    </select>
                    <strong class="p-1">:</strong>
                    <select class=" form-control form-control-sm" name="ddlMinutes"
                            id="ddlMinutes" style="width:40px;">
                        <%--<%--%>
                        <%--for (int m = 0; m < 60; m++) {--%>
                        <%--out.print("<option value=\"" + UtilityBean.formatMinuts(m) + "\">" + UtilityBean.formatMinuts(m) + "</option>");--%>
                        <%--}--%>
                        <%--%>--%>
                    </select>
                    <select class=" form-control form-control-sm" name="ddlAMPM"
                            id="ddlAMPM" style="width:40px;">
                        <option value="AM">AM</option>
                        <option selected="selected" value="PM">PM</option>
                    </select>
                    <%--<script type="text/javascript">--%>
                    <%--$("#ddlHour").value = "<%=UtilityBean.getHHmmAmPm(UtilityBean.sysDate("HH:mm"), "HH")%>";--%>
                    <%--$("#ddlMinutes").value = "<%=UtilityBean.getHHmmAmPm(UtilityBean.sysDate("HH:mm"), "MM")%>";--%>
                    <%--$("#ddlAMPM").value = "<%=UtilityBean.getHHmmAmPm(UtilityBean.sysDate("HH:mm"), "AMPM")%>";--%>
                    <%--</script>--%>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Call Center Remarks :</label>
                <div class="col-sm-8">
            <textarea name="callCenterRemark" id="callCenterRemark" class=" form-control form-control-sm"
                      title="Call Center Remark" cols=""
                      rows="3"></textarea>
                </div>
            </div>


            <fieldset class="border p-2 mt-3 ">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label"><b>Special Remarks :</b></label>
                    <div class="col-sm-8">
            <textarea name="remark" id="remark" class=" form-control form-control-sm" title="Remarks" cols=""
                      rows="3"></textarea>
                    </div>
                </div>
                <div class="form-group row mt-3">
                    <label class="col-sm-4 col-form-label"></label>
                    <div class="col text-right">
                        <button type="button" name="cancel" id="remarkButton" name="remarkButton"
                                class="btn btn-primary ml-2" onclick="addSpecialRemark()">Add Remarks
                        </button>
                    </div>
                </div>
            </fieldset>
            <div class="form-group row mt-3">
                <label class="col-sm-4 col-form-label"></label>
                <c:if test="${TYPE != 4}">
                    <div class="col text-right">
                        <c:if test="${userRight.v_modify=='checked' && assessorAllocationDto.claimsDto.claimStatus!=32}">
                            <button type="button" name="cancel" id="draft" class="btn btn-primary ml-2"
                                    style="display: none">Draft
                            </button>
                            <button type="submit" name="cancel" id="addbtn" value="Insert" class="btn btn-primary "/>
                            Submit
                            </button>
                        </c:if>
                    </div>
                </c:if>
            </div>
        </fieldset>
        <fieldset class="border p-2 mt-3 ">
            <h6 class="float-left"> Assessment Details</h6>
            <div class="text-right legend">
                <%--In-House--%>
                <span class="btn btn-xs  ml-2 btn-multipl disabled" style="background-color: antiquewhite ;opacity: 1;"><i
                        class="fa fa-home"></i> <b class="ml-1"> In-House</b></span>
                <%--Re-Assign--%>
                <span class="btn bg-info btn-xs  ml-2 btn-multipl text-white disabled" style="opacity: 1;"><i
                        class="fa fa-refresh"></i> <b class="ml-1"> Re-Assign</b></span>
                <%--Re-Send--%>
                <span class="btn bg-warning btn-xs ml-2 btn-multipl disabled" style="opacity: 1;"><i
                        class="fa fa-send"></i><b
                        class="ml-1"> Re-Send</b></span>
                <%--Complete Job--%>
                <span class="btn bg-success btn-xs ml-2 btn-multipl text-white disabled" style="opacity: 1;"><i
                        class="fa fa-check"></i> <b class="ml-1"> Complete Job</b></span>
                <%--Reject Job--%>
                <span class="btn bg-danger btn-xs ml-2 btn-multipl text-white disabled" style="opacity: 1;"><i
                        class="fa fa-window-close"></i> <b class="ml-1"> Reject Job</b></span>
            </div>
            <hr class="my-1">
            <div class="scroll ">
                <table width="1024px" cellpadding="0" cellspacing="1"
                       class="table table-hover table-sm table-responsive-md dataTable no-footer dtr-inline "
                       id="TPdata">
                    <thead>
                    <tr>
                        <th scope="col" class="tbl_row_header">Job No</th>
                        <th scope="col" class="tbl_row_header">Inspection</th>
                        <th scope="col" class="tbl_row_header">Assessor/RTE</th>
                        <th scope="col" class="tbl_row_header">Place Of Inspection</th>
                        <th scope="col" class="tbl_row_header">Duration (Min)</th>
                        <th scope="col" class="tbl_row_header">SMS Status</th>
                        <th scope="col" class="tbl_row_header" width="150px">Job Status</th>
                        <th scope="col" class="tbl_row_header">Offer Type</th>
                        <th scope="col" class="tbl_row_header">Assigned Date & Time</th>
                        <th scope="col" class="tbl_row_header">Assigned User</th>
                        <th scope="col" class="tbl_row_header">Commence Assessment Date & Time</th>
                        <th scope="col" class="tbl_row_header">Job Completed Date & Time</th>
                        <th scope="col" class="tbl_row_header">Rejected / Reassigned Reason</th>
                        <th scope="col" class="tbl_row_header">Inspection Status</th>
                        <c:if test="${TYPE != 4}">
                            <th scope="col" class="tbl_row_header" width="100">Action</th>
                        </c:if>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach var="listDto" items="${assessorAllocationList}">
                        <tr style="${listDto.assessorType == 'INHOUSE' ? 'background-color: antiquewhite' : ''}">
                            <c:choose>
                                <c:when test="${listDto.assessorType == 'INHOUSE'}">
                                    <td><span class="fa fa-home"></span>${listDto.jobId}</td>
                                </c:when>
                                <c:otherwise>
                                    <td>${listDto.jobId}</td>
                                </c:otherwise>
                            </c:choose>
                            <c:if test="${listDto.isOnsiteReview == 'N'}">
                                <td>${listDto.inspectionDto.inspectionValue}</td>
                            </c:if>
                            <c:if test="${listDto.isOnsiteReview == 'Y' && listDto.inspectionDto.inspectionId == 1}">
                                <td>On Site Review Inspection</td>
                            </c:if>
                            <c:if test="${listDto.isOnsiteReview == 'Y' && listDto.inspectionDto.inspectionId == 2}">
                                <td>Off Site Review Inspection</td>
                            </c:if>
                            <c:if test="${listDto.inspectionDto.inspectionId == 11 || listDto.inspectionDto.inspectionId == 8}">
                                <c:if test="${listDto.inspectionDto.inspectionId == 11}">
                                    <td>${listDto.inputUserId}</td>
                                </c:if>
                                <c:if test="${listDto.inspectionDto.inspectionId == 8}">
                                    <td>${listDto.rteCode}</td>
                                </c:if>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>${listDto.inspectionDto.offerType}</td>
                                <td>${listDto.assignDatetime}</td>
                                <td>${listDto.inputUserId}</td>
                                <td>
                                    <c:if test="${'Y' == listDto.isCommenceAssessment}">
                                        ${listDto.commenceAssessmentDatetime}
                                    </c:if>
                                </td>
                                <c:if test="${listDto.inspectionDto.inspectionId == 8}">
                                    <td>${listDto.jobFinishedDatetime}</td>
                                </c:if>
                                <c:if test="${listDto.inspectionDto.inspectionId == 11}">
                                    <td></td>
                                </c:if>
                                <c:choose>
                                    <c:when test="${listDto.jobStatusId == 23}">
                                        <td>${listDto.rejectReasonDto.reasonoDescription}</td>
                                    </c:when>
                                    <c:when test="${listDto.jobStatusId == 4}">
                                        <td>${listDto.reassigningReasonDto.reasonValue}</td>
                                    </c:when>
                                    <c:otherwise>
                                        <td></td>
                                    </c:otherwise>
                                </c:choose>
                                <td>
                                    <c:if test="${listDto.recordStatus == 29}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='badge alert-heading m-0' id="parStat${listDto.refNo}">ASSIGNED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 7}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">ATTENDED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 10}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">CLAIM CHANGE
                                                REQUESTED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 8}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='badge alert-heading m-0' id="parStat${listDto.refNo}">
                                                SUBMITTED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 9}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='badge alert-heading m-0' id="parStat${listDto.refNo}">APPROVED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 14}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">INSPECTION CHANGE
                                                REQUESTED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 2}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">FORWARDED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 33}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">FORWARD FOR INFORM
                                                GARAGE &
                                                CUSTOMER</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 34}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">RETURNED
                                                DESKTOP</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 4}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">REASSIGNED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 23}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0' id="parStat${listDto.refNo}">REJECTED</p>
                                        </div>
                                    </c:if>
                                    <script>
                                        var status = $('#parStat${listDto.refNo}').text();
                                        if (${listDto.inspectionDto.isInformedIfDesktop}) {
                                            $('#parStat${listDto.refNo}').text(status + ' & INFORMED');
                                        }
                                    </script>
                                </td>
                                <c:if test="${listDto.inspectionDto.inspectionId == 8}">
                                    <td>
                                        <c:if test="${listDto.jobStatusId != 28 && listDto.jobStatusId != 4 && listDto.jobStatusId != 23 && listDto.recordStatus != 9 && listDto.jobStatusId != 33}">
                                            <div class="btn-group">
                                                <button type="button"
                                                        class="btn btn-info btn-xs btn-multipl reassign align-css"
                                                        title="Re-Assign"
                                                        id="btnReassign1"
                                                        onclick="reAssignDesktop(${listDto.refNo})">
                                                    <i class="fa fa-refresh"></i>
                                                </button>
                                                <button type="button"
                                                        class="btn btn-danger btn-xs btn-multipl reject align-css"
                                                        title="Reject Job"
                                                        id="btnReject"
                                                        onclick="submitRejectReason('${listDto.refNo}', '${listDto.jobId}', '${listDto.assessorDto.userName}', '${listDto.rteCode}', '${listDto.inspectionDto.inspectionId}')">
                                                    <i class="fa fa-window-close"></i>
                                                </button>
                                            </div>
                                        </c:if>
                                    </td>
                                </c:if>
                            </c:if>
                            <c:if test="${listDto.inspectionDto.inspectionId  != 11 && listDto.inspectionDto.inspectionId  != 8}">
                                <td>${listDto.assessorDto.name}</td>
                                <td>${listDto.placeOfinspection}</td>
                                <td>${listDto.duration}</td>
                                <td>${listDto.smsStatus}</td>
                                <c:choose>
                                    <c:when test="${listDto.jobStatusId == 4}">
                                        <td title="Re-Assigned">
                                            <div class="alert alert-danger text-center p-1" role="alert"
                                                 style="border-radius: 4px">
                                                <p class="alert-heading m-0" style="font-weight: 500;">
                                                        ${listDto.reassigningReasonDto.reasonValue}
                                                </p>
                                            </div>
                                        </td>
                                    </c:when>
                                    <c:otherwise>
                                        <td>${listDto.assessorJobStatus}</td>
                                    </c:otherwise>
                                </c:choose>
                                <td>${listDto.inspectionDto.offerType}</td>
                                <td>${listDto.assignDatetime}</td>
                                <td>${listDto.inputUserId}</td>
                                <td>
                                    <c:if test="${'Y' == listDto.isCommenceAssessment}">
                                        ${listDto.commenceAssessmentDatetime}
                                    </c:if>
                                </td>
                                <c:choose>
                                    <c:when test="${listDto.jobStatusId == 28}">
                                        <td>${listDto.jobFinishedDatetime}</td>
                                    </c:when>
                                    <c:otherwise>
                                        <td></td>
                                    </c:otherwise>
                                </c:choose>
                                <c:choose>
                                    <c:when test="${listDto.jobStatusId == 23}">
                                        <td>${listDto.rejectReasonDto.reasonoDescription}</td>
                                    </c:when>
                                    <c:when test="${listDto.jobStatusId == 4}">
                                        <td>${listDto.reassigningReasonDto.reasonValue}</td>
                                    </c:when>
                                    <c:otherwise>
                                        <td></td>
                                    </c:otherwise>
                                </c:choose>
                                <td>
                                    <c:if test="${listDto.recordStatus == 29}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='badge alert-heading m-0'>ASSIGNED</p>
                                        </div>
                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 7}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0'>ATTENDED</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 10}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0'>CLAIM CHANGE REQUESTED</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 8}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='badge alert-heading m-0'>SUBMITTED</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 9}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='badge alert-heading m-0'>APPROVED</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 14}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <span class='alert-heading m-0'>INSPECTION CHANGE REQUESTED</span>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 2}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0'>FORWARDED</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 33}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0'>FORWARD FOR INFORM GARAGE &
                                                CUSTOMER</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 34}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0'>RETURNED DESKTOP</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 4}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0'>REASSIGNED</p>
                                        </div>

                                    </c:if>
                                    <c:if test="${listDto.recordStatus == 23}">
                                        <div class="alert alert-success text-center p-1" role="alert"
                                             style="border-radius: 4px;">
                                            <p class='alert-heading m-0'>REJECTED</p>
                                        </div>

                                    </c:if>
                                </td>
                            </c:if>
                            <c:if test="${TYPE != 4 && listDto.inspectionDto.inspectionId  != 8}">
                                <td class="text-right">
                                    <div class="btn-group">
                                            <%--Re-Assign--%>
                                        <c:if test="${listDto.jobStatusId != 23 && listDto.jobStatusId != 4 && listDto.inspectionDto.inspectionId  != 8}">
                                            <c:if test="${listDto.jobStatusId != 4 && listDto.jobStatusId != 28 && listDto.inspectionDto.inspectionId  != 11}">
                                                <button type="button"
                                                        class="btn btn-info btn-xs btn-multipl reassign align-css"
                                                        title="Re-Assign"
                                                        id="btnReassign"
                                                        onclick="reAssingJob(${listDto.refNo})"><i
                                                        class="fa fa-refresh"></i>
                                                </button>
                                            </c:if>
                                            <%--Re-Send--%>
                                            <c:if test="${listDto.jobStatusId != 28 && listDto.inspectionDto.inspectionId  != 11}">
                                                <button type="button"
                                                        class="btn btn-warning btn-xs btn-multipl resend align-css"
                                                        title="Re-Send"
                                                        id="btnReSend" onclick="reSendSms(${listDto.refNo})"><i
                                                        class="fa fa-send"></i>
                                                </button>
                                            </c:if>
                                            <%--Complete Job--%>
                                            <c:if test="${listDto.jobStatusId != 28 && listDto.inspectionDto.inspectionId  != 11}">
                                                <button type="button"
                                                        class="btn btn-success btn-xs btn-multipl completed align-css"
                                                        title="Complete Job" id="btnCompleted"
                                                        onclick="completed(${listDto.refNo})"><i
                                                        class="fa fa-check"></i>
                                                </button>
                                            </c:if>
                                            <c:if test="${listDto.jobStatusId == 28 && listDto.inspectionDto.inspectionId  != 11}">
                                                <div class="alert alert-success text-center p-1" role="alert"
                                                     style="border-radius: 4px;">
                                                    <p class="alert-heading m-0" style="font-weight: 500;">JOB DONE</p>
                                                </div>
                                            </c:if>
                                            <c:if test="${listDto.jobStatusId != 23 && listDto.jobStatusId != 28}">
                                                <button type="button"
                                                        class="btn btn-danger btn-xs btn-multipl reject align-css"
                                                        title="Reject Job"
                                                        id="btnReject"
                                                        onclick="submitRejectReason('${listDto.refNo}', '${listDto.jobId}', '${listDto.assessorDto.userName}', '${listDto.rteCode}', '${listDto.inspectionDto.inspectionId}')">
                                                    <i class="fa fa-window-close"></i>
                                                </button>
                                            </c:if>
                                        </c:if>
                                    </div>
                                </td>
                            </c:if>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </div>
        </fieldset>
    </div>

    <c:if test="${successMessage!=null && successMessage!=''}">
        <script type="text/javascript">
            window.parent.notify('${successMessage}', "success");
        </script>
    </c:if>
    <c:if test="${errorMessage!=null && errorMessage!=''}">
        <script type="text/javascript">
            window.parent.notify('${errorMessage}', "danger");
        </script>
    </c:if>

    <div class="modal fade bd-example-modal-md" role="dialog"
         id="rejectReasonModal" aria-hidden="true" style="    background: #333333c2; z-index: 9999999">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content p-2" style="overflow: hidden">
                <div class="modal-header  p-2">
                    <h6 class="modal-title" id="txtRejectTitle"></h6>
                </div>
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Reject reason :</label>
                            <div class="col-sm-8">
                                <select class="form-control" id="rejectReasonSelect"
                                        onchange="enableRejectReasonSaveBtn()" name="rejectReasonNewDto.reasonId">
                                    <option value="0">Please Select One</option>
                                    <%out.print(directRejectReason);%>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer p-1">
                    <button type="button" class="btn btn-danger" onclick="closeModal()">
                        Close
                    </button>
                    <button type="button" id="confirmModalBtn" class="btn btn-secondary" onclick="rejectJob()">
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
</body>
<script type="text/javascript">
    var today = new Date();
    $("#assignDatetime").datetimepicker({
        sideBySide: true,
        format: 'YYYY-MM-DD HH:mm',
        maxDate: today,
        icons: {
            time: "fa fa-clock-o",
            date: "fa fa-calendar",
            up: "fa fa-arrow-up",
            down: "fa fa-arrow-down"
        }
    });

    $(document).ready(function () {
        $('#viewDocUpload').hide();
        $("#priorityDiv").hide();
        loadReassignReasons();
        var inp = $('#InspectionType').val();
        if (${isDisabled == 'Y'}) {
            disableFormInputs("#frmMain");
            $('#reasonDiv').show();
            $('#inspectionReasonId').removeAttr('disabled').trigger("chosen:updated");
            $('#InspectionType').removeAttr('disabled').trigger("chosen:updated");
        } else {
            hideReasonOnload();
            $('#rteDiv').hide();
            $('#frmMain').data('formValidation').enableFieldValidators('rteCode', false);
            $("#rteCode").attr('disabled', 'disabled').trigger("chosen:updated");
        }

        var formType = parent.document.getElementById("formType").value || "MASTER";
        if ("HISTORY" == formType) {
            disableFormInputs("#frmMain");
            $("#assignDatetime").attr('disabled', 'disabled');
            $('#addbtn').hide();
            $('#remarkButton').hide();
            $('.reassign').hide();
            $('.resend').hide();
            $('.completed').hide();
        }

        if (inp == 4 || inp == 5 || inp == 6) {
            $('#garageDiv').show();
        }

        if (inp == 4) {
            $('#partnerGarageDiv').show();
        } else {
            $('#partnerGarageDiv').hide();
            $('#isPartnerGarage').prop('checked', false);
        }

        if (inp == 8 || inp == 12) {
            console.log("test2")
            console.log(inp)

            $.ajax({
                url: contextPath + "/InspectionDetailsController/rteReassigningReasons",
                success: function (result) {
                    var response = JSON.parse(result);
                    var select = document.getElementById('reassigningId');
                    select.options.length = 0;
                    for (var i = 0; i < response.length; i++) {
                        select.options[select.options.length] = new Option(response[i].value, response[i].lable);
                    }
                    $('#reassigningId').removeAttr('disabled').trigger("chosen:updated");
                }
            });
            $('#viewDocUpload').show();
            $('#rteDiv').show();
            $("#priorityDiv").show();
            $("#frmMain").data('formValidation').resetForm();
            removeAllValidations();
            disableFormInputs("#frmMain");
            $('#InspectionType').removeAttr('disabled').trigger("chosen:updated");
            $('#rteCode').removeAttr('disabled').trigger("chosen:updated");
            $('#priorityHigh').removeAttr('disabled').trigger("chosen:updated");
            $('#priorityNormal').removeAttr('disabled').trigger("chosen:updated");
            $("#remark").prop("readonly", false);
            $('#inspplace').val("");
            $("#inspplace").attr('disabled', 'disabled');
            $('#frmMain').data('formValidation').enableFieldValidators('rteCode', true);

            hideReason();
            hideInsoectionReason();
            $('#garageDiv').hide();
            $('#garageContactNo').val('');
            $('#frmMain').data('formValidation').enableFieldValidators('garageContactNo', false);
            $('#partnerGarageDiv').hide();
            $('#isPartnerGarage').prop('checked', false);
        }

    });

    function reAssingJob(id) {
        $('#oldJobId').val(id);
        document.frmMain.action = contextPath + "/AssessorAllocationController/reassign";
        document.frmMain.submit();
    }

    function reAssignDesktop(id) {
        $('#oldJobId').val(id);
        document.frmMain.action = contextPath + "/AssessorAllocationController/reassign";
        document.frmMain.submit();
    }

    function completed(id) {
        $('#oldJobId').val(id);
        document.frmMain.action = contextPath + "/AssessorAllocationController/completeJob";
        document.frmMain.submit();

    }

    function reSendSms(id) {
        $('#oldJobId').val(id);
        document.frmMain.action = contextPath + "/AssessorAllocationController/resendSms";
        document.frmMain.submit();

    }

    function submitRejectReason(id, jobId, assessor, rte, inspection) {
        var assignedUser = '';
        if (null != inspection && inspection != 0 && inspection != 11) {
            if (inspection == 8) {
                assignedUser = rte;
            } else {
                assignedUser = assessor;
            }
        }
        $("#claimId").val(${assessorAllocationDto.claimsDto.claimNo});
        $("#previousRefId").val(id);
        $("#previousJobId").val(jobId);
        $("#assignedUser").val(assignedUser);
        $("#inspectionId").val(inspection)
        $("#txtRejectTitle").html("Reject Reason - [" + jobId + "]");
        $("#rejectReasonModal").modal({
            backdrop: 'static',
            refresh: true,
            show: true
        });
    }

    let rteForDesktopAssessment = '';

    function reassignDesktop(refId, jobId, rte) {
        $("#reassignReasonSelect").empty();
        $("#claimId").val(${assessorAllocationDto.claimsDto.claimNo});
        $("#previousRefId").val(refId);
        $("#previousJobId").val(jobId);
        $.ajax({
            url: contextPath + "/InspectionDetailsController/rteReassigningReasons",
            success: function (response) {
                var resp = JSON.parse(response);
                var select = document.getElementById('reassignReasonSelect');
                for (var i = 0; i < resp.length; i++) {
                    select.options[select.options.length] = new Option(resp[i].value, resp[i].lable);
                }
            }
        });
        $("#rteAssignSelect").val(rte);
        rteForDesktopAssessment = rte;
        $("#txtReassignTitle").html("Reassign RTE - [" + jobId + "]");
        $("#desktopReassignModal").modal({
            backdrop: 'static',
            refresh: true,
            show: true
        })
    }

    function confirmReassignDesktop() {
        document.frmMain.action = contextPath + "/InspectionDetailsController/reassignDesktopInspection";
        document.frmMain.submit();
    }

    function enableRejectReasonSaveBtn() {
        if ($("#rejectReasonSelect").val() != '' && $("#rejectReasonSelect").val() != 0) {
            $("#confirmModalBtn").prop("disabled", false);
        } else {
            $("#confirmModalBtn").prop("disabled", true);
        }
    }

    function rejectJob() {
        document.frmMain.action = contextPath + "/AssessorAllocationController/rejectAssessorJob";
        document.frmMain.submit();
    }

    function closeModal() {
        $("#confirmModalBtn").prop('disabled', true);
        $("#rejectReasonSelect").val(0);
        $("#rejectReasonModal").modal('hide');
    }

    async function checkInspectionType() {
        var inp = $('#InspectionType').val();
        var rejValue = $("input[name='responseValue']:checked").val();
        $('#addbtn').show();
        $('#viewDocUpload').hide();
        loadReassignReasons();

        if (inp == 10 && ${assessorAllocationDto.claimsDto.causeOfLoss eq 4}) {
            notify("Please Mark Vehicle Availability before Arranging Investigation", 'danger');
            return;
        }

        if (${assessorAllocationDto.isReAssign == 'Y'}) {
            $("#reassingReasonDiv").show();
            $('#frmMain').data('formValidation').enableFieldValidators('reassigningReasonDto.id', true);
        } else {
            $('#frmMain').data('formValidation').enableFieldValidators('reassigningReasonDto.id', false);
            $("#reassingReasonDiv").show();
        }

        if (inp == 11) {
            $("#priorityDiv").hide();
            $('#reasonDiv').show();
            $("#frmMain").data('formValidation').resetForm();
            removeAllValidations();
            disableFormInputs("#frmMain");
            $('#inspectionReasonId').removeAttr('disabled').trigger("chosen:updated");
            $('#InspectionType').removeAttr('disabled').trigger("chosen:updated");
            $("#remark").prop("readonly", false);
            $('#inspplace').val("");
            $("#inspplace").attr('disabled', 'disabled');
            $('#frmMain').data('formValidation').enableFieldValidators('inspectionReasonDto.reasonId', true);
            $('#rteDiv').hide();
            $('#frmMain').data('formValidation').enableFieldValidators('rteCode', false);
            $("#rteCode").attr('disabled', 'disabled').trigger("chosen:updated");
            $("#reassigningId").removeAttr('disabled').trigger("chosen:updated");
            hideReason();
            $('#garageDiv').hide();
            $('#garageContactNo').val('');
            $('#partnerGarageDiv').hide();
            $('#isPartnerGarage').prop('checked', false);

        } else if (inp == 1) {
            $("#priorityDiv").hide();
            addAllValidations();
            removeDisableFormInputs("#frmMain");
            //   $("#frmMain").data('formValidation').resetForm();
            $('#inspplace').removeAttr('disabled');
            $('#inspectionReasonId').val(0).trigger("chosen:updated");
            $('#reasonDiv').hide();
            $('#frmMain').data('formValidation').enableFieldValidators('inspectionReasonDto.reasonId', false);
            $("#inspectionReasonId").attr('disabled', 'disabled').trigger("chosen:updated");
            $('#rteDiv').hide();
            $('#frmMain').data('formValidation').enableFieldValidators('rteCode', false);
            $("#rteCode").attr('disabled', 'disabled').trigger("chosen:updated");
            if (rejValue != "23") {
                hideReason();
            }
            $('#garageDiv').hide();
            $('#garageContactNo').val('');
            $('#frmMain').data('formValidation').enableFieldValidators('garageContactNo', false);
            $('#partnerGarageDiv').hide();
            $('#isPartnerGarage').prop('checked', false);
            var claim = $('#claimId').val();
            $.ajax({
                url: contextPath + "/AssessorAllocationController/onsite?claimId=" + claim,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    $("#districtId").html("");
                    $("#districtId").append($('<option>').val(obj.districtCode).html(obj.districtName));
                    $("#districtId").val(obj.districtCode).trigger("chosen:updated");
                    $("#cityId").html("");
                    $("#cityId").append($('<option>').val(obj.cityCode).html(obj.cityName));
                    $("#cityId").val(obj.cityCode).trigger("chosen:updated");
                    $('#inspplace').val(obj.placeOfInspection);
                    //addAssessors

                    // var obj1 = obj.assessorDtos;
//                    $("#assessorId").html("");//.append($('<option>').val(0).html("Please Select"));
//                    $("#assessorId").append($('<option>').val("").html("Please Select One")).trigger("chosen:updated");
//                    for (var i = 0; i < obj1.length; i++) {
//                        $("#assessorId").append($('<option>').val(obj1[i].code).html(obj1[i].name)).trigger("chosen:updated");
//                    }

                    //$("#frmMain").formValidation('revalidateField', 'districtDto.districtCode');
                    // $("#frmMain").formValidation('revalidateField', 'cityDto.gramaCode');
                    $("#frmMain").formValidation('revalidateField', 'placeOfinspection');
                    $("#frmMain").formValidation('revalidateField', 'assessorDto.code');
                    $("#frmMain").formValidation('revalidateField', 'rejectReasonDto.reasonId');


                }
            });
        } else if (inp == 8 || inp == 12) {
            $.ajax({
                url: contextPath + "/InspectionDetailsController/rteReassigningReasons",
                success: function (result) {
                    var response = JSON.parse(result);
                    var select = document.getElementById('reassigningId');
                    select.options.length = 0;
                    for (var i = 0; i < response.length; i++) {
                        select.options[select.options.length] = new Option(response[i].value, response[i].lable);
                    }
                    $('#reassigningId').removeAttr('disabled').trigger("chosen:updated");
                }
            });
            $('#viewDocUpload').show();
            $('#rteDiv').show();
            $("#priorityDiv").show();
            $("#frmMain").data('formValidation').resetForm();
            removeAllValidations();
            disableFormInputs("#frmMain");
            $('#InspectionType').removeAttr('disabled').trigger("chosen:updated");
            $('#rteCode').removeAttr('disabled').trigger("chosen:updated");
            $('#priorityHigh').removeAttr('disabled').trigger("chosen:updated");
            $('#priorityNormal').removeAttr('disabled').trigger("chosen:updated");
            $("#remark").prop("readonly", false);
            $('#inspplace').val("");
            $("#inspplace").attr('disabled', 'disabled');
            $('#frmMain').data('formValidation').enableFieldValidators('rteCode', true);
            $("#reassigningId").removeAttr('disabled').trigger("chosen:updated");
            hideReason();
            hideInsoectionReason();
            $('#garageDiv').hide();
            $('#garageContactNo').val('');
            $('#frmMain').data('formValidation').enableFieldValidators('garageContactNo', false);
            $('#partnerGarageDiv').hide();
            $('#isPartnerGarage').prop('checked', false);


        } else {
            addAllValidations();
            removeDisableFormInputs("#frmMain");
            //  $("#frmMain").data('formValidation').resetForm();
            $('#inspectionReasonId').val(0).trigger("chosen:updated");
            $('#reasonDiv').hide();
            $('#inspplace').removeAttr('disabled');
            $('#frmMain').data('formValidation').enableFieldValidators('inspectionReasonDto.reasonId', false);
            $("#inspectionReasonId").attr('disabled', 'disabled').trigger("chosen:updated");

            $('#rteDiv').hide();
            $("#priorityDiv").hide();
            $('#frmMain').data('formValidation').enableFieldValidators('rteCode', false);
            $("#rteCode").attr('disabled', 'disabled').trigger("chosen:updated");
            if (rejValue != "23") {
                hideReason();
            }


            $.ajax({
                url: contextPath + "/AssessorAllocationController/districtList",
                type: 'GET',
                success: function (result) {

                    var obj = JSON.parse(result);

                    $("#districtId").html("");//.append($('<option>').val(0).html("Please Select"));
                    $("#districtId").append($('<option>').val("").html("Please Select One")).trigger("chosen:updated");
                    $("#cityId").html("");//.append($('<option>').val(0).html("Please Select"));
                    $("#cityId").append($('<option>').val("").html("Please Select One")).trigger("chosen:updated");
//                    $("#assessorId").html("");//.append($('<option>').val(0).html("Please Select"));
//                    $("#assessorId").append($('<option>').val("").html("Please Select One")).trigger("chosen:updated");
                    // $('#inspplace').val('');
                    for (var i = 0; i < obj.length; i++) {
                        $("#districtId").append($('<option>').val(obj[i].districtCode).html(obj[i].districtName)).trigger("chosen:updated");
                    }

                }
            });
            $('#inspplace').val("");

            if (inp == 4 || inp == 5 || inp == 6) {
                $('#garageDiv').show();
                $('#frmMain').data('formValidation').enableFieldValidators('garageContactNo', true);
            } else if (inp == 2) {
                $('#inspplace').val("${assessorAllocationDto.claimsDto.currentLocation}");
            }

            if (inp == 4) {
                $('#partnerGarageDiv').show();
            } else {
                $('#partnerGarageDiv').hide();
                $('#isPartnerGarage').prop('checked', false);
            }

        }
    }

    function loadReassignReasons() {
        $.ajax({
            url: contextPath + "/InspectionDetailsController/reassignReasons",
            success: function (result) {
                var response = JSON.parse(result);
                var select = document.getElementById('reassigningId');
                select.options.length = 0;
                for (var i = 0; i < response.length; i++) {
                    select.options[select.options.length] = new Option(response[i].value, response[i].lable);
                }
                $('#reassigningId').removeAttr('disabled').trigger("chosen:updated");
            }
        });
    }

    function addSpecialRemark() {
        var claim = $('#claimId').val();
        var specialRemark = $('#remark').val();
        if (specialRemark != "") {
            $.ajax({
                url: contextPath + "/AssessorAllocationController/addRemark?remark=" + specialRemark + "&claimId=" + claim,
                type: 'POST',
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj.errorCode == 200) {
//                    alerthideAllocation();
                        window.parent.assesoraddRemark(obj.message, 'success');
                    } else {
                        $("#remark").focus().addClass('error');
                        window.parent.assesoraddRemark(obj.message, 'danger');

                    }

                }
            });
        } else {
            window.parent.assesoraddRemark("Special Remark can not be empty", 'danger');
        }


    }

    function hideReason() {
        $('#rejectReasonDiv').hide();
        $('#frmMain').data('formValidation').enableFieldValidators('rejectReasonDto.reasonId', false);
        $("#rejectReasonId").attr('disabled', 'disabled').trigger("chosen:updated");

    }

    function hideInsoectionReason() {
        $('#frmMain').data('formValidation').enableFieldValidators('inspectionReasonDto.reasonId', false);
        $("#inspectionReasonId").attr('disabled', 'disabled').trigger("chosen:updated");
        $('#reasonDiv').hide();

    }


    function hideReasonOnload() {
        $('#rejectReasonDiv').hide();
        $("#rejectReasonId").attr('disabled', 'disabled').trigger("chosen:updated");
        $('#reasonDiv').hide();
        $('#inspectionReasonId').attr('disabled', 'disabled').trigger("chosen:updated");

    }

    function showReason() {
        $('#rejectReasonDiv').show();
        $('#rejectReasonId').removeAttr('disabled').trigger("chosen:updated");
        $('#frmMain').data('formValidation').enableFieldValidators('rejectReasonDto.reasonId', true);

    }

    function removeAllValidations() {
        var inp = $('#InspectionType').val();

        $('#frmMain').data('formValidation').enableFieldValidators('placeOfinspection', false);
        // $('#frmMain').data('formValidation').enableFieldValidators('districtDto.districtCode', false);
        // $('#frmMain').data('formValidation').enableFieldValidators('cityDto.gramaCode', false);
        $('#frmMain').data('formValidation').enableFieldValidators('assessorDto.code', false);
        $('#frmMain').data('formValidation').enableFieldValidators('rejectReasonDto.reasonId', false);
    }

    function addAllValidations() {
        $('#frmMain').data('formValidation').enableFieldValidators('placeOfinspection', true);
        // $('#frmMain').data('formValidation').enableFieldValidators('districtDto.districtCode', true);
        // $('#frmMain').data('formValidation').enableFieldValidators('cityDto.gramaCode', true);
        $('#frmMain').data('formValidation').enableFieldValidators('assessorDto.code', true);
        $('#frmMain').data('formValidation').enableFieldValidators('rejectReasonDto.reasonId', true);
    }

    function uploadDocumets() {
        var claim = $('#claimId').val();
        var inspectionType = $('#InspectionType').val();
        $("#viewDocUpload").colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/AssessorAllocationController/documentUpload?P_N_CLIM_NO=" + claim + "&inspectionType=" + inspectionType,
            onCleanup: function () {
            }
        });
    }

    function validateTheftClaims() {
        let claimNo = $('#claimId').val();
        return new Promise(resolve => {
            $.ajax({
                url: contextPath + "/CallCenter/isTheft",
                data: {
                    N_CLAIM_NO: claimNo
                },
                type: 'POST',
                success: function (result) {
                    let response = JSON.parse(result);
                    if (response == 'Y') {
                        return resolve(1);
                    } else if (response == 'N') {
                        return resolve(0);
                    } else {
                        return resolve(-1);
                    }
                }
            })
        });
    }

    $(document).ready(function() {
        $('#InspectionType').on('change', function() {
            var inspectionType = $(this).val();

            if (inspectionType == 4) {
                $('#partnerGarageDiv').show();
            } else {
                $('#partnerGarageDiv').hide();
                $('#isPartnerGarage').prop('checked', false);
            }
        });

        var currentInspectionType = $('#InspectionType').val();
        if (currentInspectionType == 4) {
            $('#partnerGarageDiv').show();
        } else {
            $('#partnerGarageDiv').hide();
        }
    });


</script>
<script>
    function assignOnsiteReviewJob(requestedInspectionId) {

        var txnId = $('#refNos').val();
        var assignUser = $('#assignUser').val();
        var claimN = $('#claimNo').val();

        bootbox.confirm({
            message: "Are you sure you want to assign this job?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result === true) {
                    $.ajax({
                        url: contextPath + "/ClaimAssignUserReassignController/updateSelfAssignCallCenterUser"
                            + "?refNo=" + txnId
                            + "&assignUser=" + assignUser
                            + "&claimNo=" + claimN
                            + "&requestedInspectionId=" + requestedInspectionId,
                        type: 'POST',
                        success: function (result) {
                            var obj = JSON.parse(result);
                            if (obj === "SUCCESS") {
                                notify("Claim assigned successfully.", "success");
                                $('#assignUsersBtn').prop('disabled', true);
                                $('#addbtn').show();
                            } else if (obj === "ALREADY_ASSIGNED") {
                                // notify("This claim is already assigned.", "warning");


                                bootbox.alert({
                                    message: "This claim is already assigned to someone.",
                                    callback: function () {
                                        const formType = $('#FORM_TYPE').val() || 'ASSIGN';
                                        window.location.href = contextPath
                                            + "/CallCenter/viewAssessorAllocation"
                                            + "?claimNo=" + encodeURIComponent(claimN)
                                            + "&FORM_TYPE=" + encodeURIComponent(formType)
                                            + "&onsiteReview=false"
                                            + "&requestedInspectionId=" + encodeURIComponent(requestedInspectionId);
                                    }
                                });
                            }else if(obj == "ASSIGNED_USER") {
                                bootbox.alert("You are already assigned to this job.");

                            } else if (obj === "FAIL") {
                                notify("Failed to assign user.", "danger");
                                $('#assignUsersBtn').prop('disabled', false);
                            } else {
                                notify("System error occurred.", "danger");
                                $('#assignUsersBtn').prop('disabled', false);
                            }
                        },
                        error: function() {
                            notify("Network or server error occurred.", "danger");
                            $('#assignUsersBtn').prop('disabled', false);
                        }
                    });
                }
            }
        });
    }



</script>
</html>
