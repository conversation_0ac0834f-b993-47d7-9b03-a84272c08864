# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ì´ì  íì´ì§
previous_label=ì´ì 
next.title=ë¤ì íì´ì§
next_label=ë¤ì

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=íì´ì§
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=/ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} / {{pagesCount}})

zoom_out.title=ì¶ì
zoom_out_label=ì¶ì
zoom_in.title=íë
zoom_in_label=íë
zoom.title=íë/ì¶ì
presentation_mode.title=íë ì  íì´ì ëª¨ëë¡ ì í
presentation_mode_label=íë ì  íì´ì ëª¨ë
open_file.title=íì¼ ì´ê¸°
open_file_label=ì´ê¸°
print.title=ì¸ì
print_label=ì¸ì
download.title=ë¤ì´ë¡ë
download_label=ë¤ì´ë¡ë
bookmark.title=íì¬ ë³´ê¸° (ë³µì¬ ëë ì ì°½ì ì´ê¸°)
bookmark_label=íì¬ ë³´ê¸°

# Secondary toolbar and context menu
tools.title=ëêµ¬
tools_label=ëêµ¬
first_page.title=ì²« íì´ì§ë¡ ì´ë
first_page.label=ì²« íì´ì§ë¡ ì´ë
first_page_label=ì²« íì´ì§ë¡ ì´ë
last_page.title=ë§ì§ë§ íì´ì§ë¡ ì´ë
last_page.label=ë§ì§ë§ íì´ì§ë¡ ì´ë
last_page_label=ë§ì§ë§ íì´ì§ë¡ ì´ë
page_rotate_cw.title=ìê³ë°©í¥ì¼ë¡ íì 
page_rotate_cw.label=ìê³ë°©í¥ì¼ë¡ íì 
page_rotate_cw_label=ìê³ë°©í¥ì¼ë¡ íì 
page_rotate_ccw.title=ìê³ ë°ëë°©í¥ì¼ë¡ íì 
page_rotate_ccw.label=ìê³ ë°ëë°©í¥ì¼ë¡ íì 
page_rotate_ccw_label=ìê³ ë°ëë°©í¥ì¼ë¡ íì 

cursor_text_select_tool.title=íì¤í¸ ì í ëêµ¬ íì±í
cursor_text_select_tool_label=íì¤í¸ ì í ëêµ¬
cursor_hand_tool.title=ì ëêµ¬ íì±í
cursor_hand_tool_label=ì ëêµ¬

scroll_vertical.title=ì¸ë¡ ì¤í¬ë¡¤ ì¬ì©
scroll_vertical_label=ì¸ë¡ ì¤í¬ë¡¤
scroll_horizontal.title=ê°ë¡ ì¤í¬ë¡¤ ì¬ì©
scroll_horizontal_label=ê°ë¡ ì¤í¬ë¡¤
scroll_wrapped.title=ëí(ìë ì¤ ë°ê¿) ì¤í¬ë¡¤ ì¬ì©
scroll_wrapped_label=ëí ì¤í¬ë¡¤

spread_none.title=í íì´ì§ ë³´ê¸°
spread_none_label=í¼ì³ì§ ìì
spread_odd.title=íì íì´ì§ë¡ ììíë ë íì´ì§ ë³´ê¸°
spread_odd_label=íì í¼ì³ì§
spread_even.title=ì§ì íì´ì§ë¡ ììíë ë íì´ì§ ë³´ê¸°
spread_even_label=ì§ì í¼ì³ì§

# Document properties dialog box
document_properties.title=ë¬¸ì ìì±â¦
document_properties_label=ë¬¸ì ìì±â¦
document_properties_file_name=íì¼ ì´ë¦:
document_properties_file_size=íì¼ í¬ê¸°:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}}ë°ì´í¸)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}}ë°ì´í¸)
document_properties_title=ì ëª©:
document_properties_author=ìì±ì:
document_properties_subject=ì£¼ì :
document_properties_keywords=í¤ìë:
document_properties_creation_date=ìì± ë ì§:
document_properties_modification_date=ìì  ë ì§:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ìì± íë¡ê·¸ë¨:
document_properties_producer=PDF ë³í ìíí¸ì¨ì´:
document_properties_version=PDF ë²ì :
document_properties_page_count=íì´ì§ ì:
document_properties_page_size=íì´ì§ í¬ê¸°:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=ì¸ë¡ ë°©í¥
document_properties_page_size_orientation_landscape=ê°ë¡ ë°©í¥
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ë í°
document_properties_page_size_name_legal=ë¦¬ê±¸
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ë¹ ë¥¸ ì¹ ë³´ê¸°:
document_properties_linearized_yes=ì
document_properties_linearized_no=ìëì¤
document_properties_close=ë«ê¸°

print_progress_message=ì¸ì ë¬¸ì ì¤ë¹ ì¤â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ì·¨ì

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=íìì°½ íì/ì¨ê¸°ê¸°
toggle_sidebar_notification.title=íìì°½ íì/ì¨ê¸°ê¸° (ë¬¸ìì ììë¼ì¸/ì²¨ë¶íì¼ í¬í¨ë¨)
toggle_sidebar_notification2.title=íìì°½ íì/ì¨ê¸°ê¸° (ë¬¸ìì ììë¼ì¸/ì²¨ë¶íì¼/ë ì´ì´ í¬í¨ë¨)
toggle_sidebar_label=íìì°½ íì/ì¨ê¸°ê¸°
document_outline.title=ë¬¸ì ììë¼ì¸ ë³´ê¸° (ëë¸ í´ë¦­í´ì ëª¨ë  í­ëª© í¼ì¹ê¸°/ì ê¸°)
document_outline_label=ë¬¸ì ììë¼ì¸
attachments.title=ì²¨ë¶íì¼ ë³´ê¸°
attachments_label=ì²¨ë¶íì¼
layers.title=ë ì´ì´ ë³´ê¸° (ëë¸ í´ë¦­í´ì ëª¨ë  ë ì´ì´ë¥¼ ê¸°ë³¸ ìíë¡ ì¬ì¤ì )
layers_label=ë ì´ì´
thumbs.title=ë¯¸ë¦¬ë³´ê¸°
thumbs_label=ë¯¸ë¦¬ë³´ê¸°
current_outline_item.title=íì¬ ììë¼ì¸ í­ëª© ì°¾ê¸°
current_outline_item_label=íì¬ ììë¼ì¸ í­ëª©
findbar.title=ê²ì
findbar_label=ê²ì

additional_layers=ì¶ê° ë ì´ì´
# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas={{page}} íì´ì§
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark={{page}} íì´ì§
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} íì´ì§
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} íì´ì§ ë¯¸ë¦¬ë³´ê¸°

# Find panel button title and messages
find_input.title=ì°¾ê¸°
find_input.placeholder=ë¬¸ììì ì°¾ê¸°â¦
find_previous.title=ì§ì  ë¬¸ìì´ì ì¼ì¹íë 1ê° ë¶ë¶ì ê²ì
find_previous_label=ì´ì 
find_next.title=ì§ì  ë¬¸ìì´ì ì¼ì¹íë ë¤ì ë¶ë¶ì ê²ì
find_next_label=ë¤ì
find_highlight=ëª¨ë ê°ì¡° íì
find_match_case_label=ë/ìë¬¸ì êµ¬ë¶
find_entire_word_label=ë¨ì´ ë¨ìë¡
find_reached_top=ë¬¸ì ì²ìê¹ì§ ê²ìíê³  ëì¼ë¡ ëìì ê²ìíìµëë¤.
find_reached_bottom=ë¬¸ì ëê¹ì§ ê²ìíê³  ìì¼ë¡ ëìì ê²ìíìµëë¤.
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} ì¤ {{current}} ì¼ì¹
find_match_count[two]={{total}} ì¤ {{current}} ì¼ì¹
find_match_count[few]={{total}} ì¤ {{current}} ì¼ì¹
find_match_count[many]={{total}} ì¤ {{current}} ì¼ì¹
find_match_count[other]={{total}} ì¤ {{current}} ì¼ì¹
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} ì´ì ì¼ì¹
find_match_count_limit[one]={{limit}} ì´ì ì¼ì¹
find_match_count_limit[two]={{limit}} ì´ì ì¼ì¹
find_match_count_limit[few]={{limit}} ì´ì ì¼ì¹
find_match_count_limit[many]={{limit}} ì´ì ì¼ì¹
find_match_count_limit[other]={{limit}} ì´ì ì¼ì¹
find_not_found=ê²ì ê²°ê³¼ ìì

# Error panel labels
error_more_info=ì ë³´ ë ë³´ê¸°
error_less_info=ì ë³´ ê°ë¨í ë³´ê¸°
error_close=ë«ê¸°
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ë¹ë: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ë©ìì§: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ì¤í: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=íì¼: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ì¤ ë²í¸: {{line}}
rendering_error=íì´ì§ë¥¼ ë ëë§íë ëì ì¤ë¥ê° ë°ìíìµëë¤.

# Predefined zoom values
page_scale_width=íì´ì§ ëë¹ì ë§ì¶ê¸°
page_scale_fit=íì´ì§ì ë§ì¶ê¸°
page_scale_auto=ìë
page_scale_actual=ì¤ì  í¬ê¸°
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ì¤ë¥

# Loading indicator messages
loading=ë¡ë ì¤â¦
loading_error=PDFë¥¼ ë¡ëíë ëì ì¤ë¥ê° ë°ìíìµëë¤.
invalid_file_error=ìëª»ëìê±°ë ììë PDF íì¼.
missing_file_error=PDF íì¼ ìì.
unexpected_response_error=ììì¹ ëª»í ìë² ìëµìëë¤.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}} {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ì£¼ì]
password_label=ì´ PDF íì¼ì ì´ ì ìë ë¹ë°ë²í¸ë¥¼ ìë ¥íì¸ì.
password_invalid=ìëª»ë ë¹ë°ë²í¸ìëë¤. ë¤ì ìëíì¸ì.
password_ok=íì¸
password_cancel=ì·¨ì

printing_not_supported=ê²½ê³ : ì´ ë¸ë¼ì°ì ë ì¸ìë¥¼ ìì í ì§ìíì§ ììµëë¤.
printing_not_ready=ê²½ê³ : ì´ PDFë¥¼ ì¸ìë¥¼ í  ì ìì ì ëë¡ ì½ì´ë¤ì´ì§ ëª»íìµëë¤.
web_fonts_disabled=ì¹ í°í¸ê° ë¹íì±íë¨: ë´ì¥ë PDF ê¸ê¼´ì ì¬ì©í  ì ììµëë¤.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
unsupported_feature_signatures=ì´ PDF ë¬¸ììë ëì§í¸ ìëªì´ í¬í¨ëì´ ììµëë¤. ìëª ì í¨ì± ê²ì¬ë ì§ìëì§ ììµëë¤.
