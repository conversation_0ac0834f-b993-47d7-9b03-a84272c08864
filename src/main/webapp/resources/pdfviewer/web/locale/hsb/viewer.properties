# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=PÅedchadna strona
previous_label=WrÃ³Äo
next.title=PÅichodna strona
next_label=Dale

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Strona
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=z {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} z {{pagesCount}})

zoom_out.title=PomjeÅÅ¡iÄ
zoom_out_label=PomjeÅÅ¡iÄ
zoom_in.title=PowjetÅ¡iÄ
zoom_in_label=PowjetÅ¡iÄ
zoom.title=Skalowanje
presentation_mode.title=Do prezentaciskeho modusa pÅeÅÄ
presentation_mode_label=Prezentaciski modus
open_file.title=Dataju woÄiniÄ
open_file_label=WoÄiniÄ
print.title=ÄiÅ¡ÄeÄ
print_label=ÄiÅ¡ÄeÄ
download.title=SÄahnyÄ
download_label=SÄahnyÄ
bookmark.title=Aktualny napohlad (kopÄrowaÄ abo w nowym woknje woÄiniÄ)
bookmark_label=Aktualny napohlad

# Secondary toolbar and context menu
tools.title=Nastroje
tools_label=Nastroje
first_page.title=K prÄnjej stronje
first_page.label=K prÄnjej stronje
first_page_label=K prÄnjej stronje
last_page.title=K poslednjej stronje
last_page.label=K poslednjej stronje
last_page_label=K poslednjej stronje
page_rotate_cw.title=K smÄrej Äasnika wjerÄeÄ
page_rotate_cw.label=K smÄrej Äasnika wjerÄeÄ
page_rotate_cw_label=K smÄrej Äasnika wjerÄeÄ
page_rotate_ccw.title=PÅeÄiwo smÄrej Äasnika wjerÄeÄ
page_rotate_ccw.label=PÅeÄiwo smÄrej Äasnika wjerÄeÄ
page_rotate_ccw_label=PÅeÄiwo smÄrej Äasnika wjerÄeÄ

cursor_text_select_tool.title=Nastroj za wubÄranje teksta zmÃ³Å¾niÄ
cursor_text_select_tool_label=Nastroj za wubÄranje teksta
cursor_hand_tool.title=RuÄny nastroj zmÃ³Å¾niÄ
cursor_hand_tool_label=RuÄny nastroj

scroll_vertical.title=Wertikalne suwanje wuÅ¾iwaÄ
scroll_vertical_label=Wertikalnje suwanje
scroll_horizontal.title=Horicontalne suwanje wuÅ¾iwaÄ
scroll_horizontal_label=Horicontalne suwanje
scroll_wrapped.title=Postupne suwanje wuÅ¾iwaÄ
scroll_wrapped_label=Postupne suwanje

spread_none.title=Strony njezwjazaÄ
spread_none_label=Å½ana dwÃ³jna strona
spread_odd.title=Strony zapoÄinajo z njerunymi stronami zwjazaÄ
spread_odd_label=Njerune strony
spread_even.title=Strony zapoÄinajo z runymi stronami zwjazaÄ
spread_even_label=Rune strony

# Document properties dialog box
document_properties.title=Dokumentowe kajkosÄeâ¦
document_properties_label=Dokumentowe kajkosÄeâ¦
document_properties_file_name=Mjeno dataje:
document_properties_file_size=WulkosÄ dataje:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bajtow)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bajtow)
document_properties_title=Titul:
document_properties_author=Awtor:
document_properties_subject=PÅedmjet:
document_properties_keywords=KluÄowe sÅowa:
document_properties_creation_date=Datum wutworjenja:
document_properties_modification_date=Datum zmÄny:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Awtor:
document_properties_producer=PDF-zhotowjer:
document_properties_version=PDF-wersija:
document_properties_page_count=LiÄba stronow:
document_properties_page_size=WulkosÄ strony:
document_properties_page_size_unit_inches=cÃ³l
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=wysoki format
document_properties_page_size_orientation_landscape=prÄÄny format
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Fast Web View:
document_properties_linearized_yes=Haj
document_properties_linearized_no=NÄ
document_properties_close=ZaÄiniÄ

print_progress_message=Dokument so za ÄiÅ¡Äenje pÅihotujeâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=PÅetorhnyÄ

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=BÃ³Änicu pokazaÄ/schowaÄ
toggle_sidebar_notification.title=BÃ³Änicu pÅepinaÄ (dokument wobsahuje wobrys/pÅiwÄÅ¡ki)
toggle_sidebar_notification2.title=BÃ³Änicu pÅepinaÄ (dokument rozrjad/pÅiwÄÅ¡ki/worÅ¡ty wobsahuje)
toggle_sidebar_label=BÃ³Änicu pokazaÄ/schowaÄ
document_outline.title=Dokumentowy naÄisk pokazaÄ (dwÃ³jne kliknjenje, zo bychu so wÅ¡Ä zapiski pokazali/schowali)
document_outline_label=Dokumentowa struktura
attachments.title=PÅiwÄÅ¡ki pokazaÄ
attachments_label=PÅiwÄÅ¡ki
layers.title=WorÅ¡ty pokazaÄ (klikÅÄe dwÃ³jce, zo byÅ¡Äe wÅ¡Ä worÅ¡ty na standardny staw wrÃ³Äo stajiÅ)
layers_label=WorÅ¡ty
thumbs.title=Miniatury pokazaÄ
thumbs_label=Miniatury
current_outline_item.title=Aktualny rozrjadowy zapisk pytaÄ
current_outline_item_label=Aktualny rozrjadowy zapisk
findbar.title=W dokumenÄe pytaÄ
findbar_label=PytaÄ

additional_layers=DalÅ¡e worÅ¡ty
# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Strona {{page}}
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Strona {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Strona {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniatura strony {{page}}

# Find panel button title and messages
find_input.title=PytaÄ
find_input.placeholder=W dokumenÄe pytaÄâ¦
find_previous.title=PÅedchadne wustupowanje pytanskeho wuraza pytaÄ
find_previous_label=WrÃ³Äo
find_next.title=PÅichodne wustupowanje pytanskeho wuraza pytaÄ
find_next_label=Dale
find_highlight=WÅ¡Ä wuzbÄhnyÄ
find_match_case_label=Wulkopisanje wobkedÅºbowaÄ
find_entire_word_label=CyÅe sÅowa
find_reached_top=SpoÄatk dokumenta docpÄty, pokroÄuje so z kÃ³ncom
find_reached_bottom=KÃ³nc dokument docpÄty, pokroÄuje so ze spoÄatkom
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} z {{total}} wotpowÄdnika
find_match_count[two]={{current}} z {{total}} wotpowÄdnikow
find_match_count[few]={{current}} z {{total}} wotpowÄdnikow
find_match_count[many]={{current}} z {{total}} wotpowÄdnikow
find_match_count[other]={{current}} z {{total}} wotpowÄdnikow
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Wjace haÄ {{limit}} wotpowÄdnikow
find_match_count_limit[one]=Wjace haÄ {{limit}} wotpowÄdnik
find_match_count_limit[two]=Wjace haÄ {{limit}} wotpowÄdnikaj
find_match_count_limit[few]=Wjace haÄ {{limit}} wotpowÄdniki
find_match_count_limit[many]=Wjace haÄ {{limit}} wotpowÄdnikow
find_match_count_limit[other]=Wjace haÄ {{limit}} wotpowÄdnikow
find_not_found=Pytanski wuraz njeje so namakaÅ

# Error panel labels
error_more_info=Wjace informacijow
error_less_info=Mjenje informacijow
error_close=ZaÄiniÄ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ZdÅºÄlenka: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=LisÄina zawoÅanjow: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Dataja: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Linka: {{line}}
rendering_error=PÅi zwobraznjenju strony je zmylk wustupiÅ.

# Predefined zoom values
page_scale_width=Å ÄrokosÄ strony
page_scale_fit=WulkosÄ strony
page_scale_auto=Awtomatiske skalowanje
page_scale_actual=Aktualna wulkosÄ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Zmylk

# Loading indicator messages
loading=ZaÄituje soâ¦
loading_error=PÅi zaÄitowanju PDF je zmylk wustupiÅ.
invalid_file_error=NjepÅaÄiwa abo wobÅ¡kodÅºena PDF-dataja.
missing_file_error=Falowaca PDF-dataja.
unexpected_response_error=NjewoÄakowana serwerowa wotmoÅwa.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Typ pÅispomnjenki: {{type}}]
password_label=ZapodajÄe hesÅo, zo byÅ¡Äe PDF-dataju woÄiniÅ.
password_invalid=NjepÅaÄiwe hesÅo. ProÅ¡u spytajÄe hiÅ¡Äe raz.
password_ok=W porjadku
password_cancel=PÅetorhnyÄ

printing_not_supported=Warnowanje: ÄiÅ¡Äenje so pÅez tutÃ³n wobhladowak poÅnje njepodpÄruje.
printing_not_ready=Warnowanje: PDF njeje so za ÄiÅ¡Äenje dospoÅnje zaÄitaÅ.
web_fonts_disabled=Webpisma su znjemÃ³Å¾njene: njeje mÃ³Å¾no, zasadÅºene PDF-pisma wuÅ¾iwaÄ.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
unsupported_feature_signatures=TutÃ³n PDF-dokument digitalne signatury wobsahuje. PÅepruwowanje signaturow so njepodpÄruje.
