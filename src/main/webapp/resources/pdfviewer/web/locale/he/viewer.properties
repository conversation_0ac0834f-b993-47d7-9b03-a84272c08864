# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=××£ ×§×××
previous_label=×§×××
next.title=××£ ×××
next_label=×××

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=××£
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=××ª×× {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} ××ª×× {{pagesCount}})

zoom_out.title=××ª×¨××§××ª
zoom_out_label=××ª×¨××§××ª
zoom_in.title=××ª×§×¨×××ª
zoom_in_label=××ª×§×¨×××ª
zoom.title=××¨××§ ××ª×¦×××
presentation_mode.title=××¢××¨ ×××¦× ××¦××ª
presentation_mode_label=××¦× ××¦××ª
open_file.title=×¤×ª×××ª ×§×××¥
open_file_label=×¤×ª×××
print.title=×××¤×¡×
print_label=×××¤×¡×
download.title=×××¨××
download_label=×××¨××
bookmark.title=×ª×¦××× × ×××××ª (××¢×ª×§× ×× ×¤×ª××× ××××× ×××©)
bookmark_label=×ª×¦××× × ×××××ª

# Secondary toolbar and context menu
tools.title=××××
tools_label=××××
first_page.title=××¢××¨ ××¢××× ××¨××©××
first_page.label=××¢××¨ ××¢××× ××¨××©××
first_page_label=××¢××¨ ××¢××× ××¨××©××
last_page.title=××¢××¨ ××¢××× ××××¨××
last_page.label=××¢××¨ ××¢××× ××××¨××
last_page_label=××¢××¨ ××¢××× ××××¨××
page_rotate_cw.title=×××× ×¢× ××××× ××©×¢××
page_rotate_cw.label=×××× ×¢× ××××× ××©×¢××
page_rotate_cw_label=×××× ×¢× ××××× ××©×¢××
page_rotate_ccw.title=×××× ×× ×× ××××× ××©×¢××
page_rotate_ccw.label=×××× ×× ×× ××××× ××©×¢××
page_rotate_ccw_label=×××× ×× ×× ××××× ××©×¢××

cursor_text_select_tool.title=××¤×¢××ª ××× ××××¨×ª ××§×¡×
cursor_text_select_tool_label=××× ××××¨×ª ××§×¡×
cursor_hand_tool.title=××¤×¢××ª ××× ×××
cursor_hand_tool_label=××× ××

scroll_vertical.title=×©××××© ×××××× ×× ×××ª
scroll_vertical_label=××××× ×× ×××ª
scroll_horizontal.title=×©××××© ×××××× ×××¤×§××ª
scroll_horizontal_label=××××× ×××¤×§××ª
scroll_wrapped.title=×©××××© ×××××× ×¨×¦××¤×
scroll_wrapped_label=××××× ×¨×¦××¤×

spread_none.title=×× ××¦×¨×£ ××¤×ª×× ×¢×××××
spread_none_label=××× ××¤×ª×××
spread_odd.title=×¦××¨××£ ××¤×ª×× ×¢××××× ×©××ª××××× ×××¤×× ×¢× ××¡×¤×¨×× ××Ö¾××××××
spread_odd_label=××¤×ª××× ××Ö¾××××××
spread_even.title=×¦××¨××£ ××¤×ª×× ×¢××××× ×©××ª××××× ×××¤×× ×¢× ××¡×¤×¨×× ××××××
spread_even_label=××¤×ª××× ××××××

# Document properties dialog box
document_properties.title=×××¤××× × ××¡××â¦
document_properties_label=×××¤××× × ××¡××â¦
document_properties_file_name=×©× ×§×××¥:
document_properties_file_size=×××× ××§×××¥:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} ×§×´× ({{size_b}} ××ª××)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ××´× ({{size_b}} ××ª××)
document_properties_title=×××ª×¨×ª:
document_properties_author=××××¨:
document_properties_subject=× ××©×:
document_properties_keywords=×××××ª ××¤×ª×:
document_properties_creation_date=×ª××¨×× ××¦××¨×:
document_properties_modification_date=×ª××¨×× ×©×× ××:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=×××¦×¨:
document_properties_producer=××¦×¨× PDF:
document_properties_version=××¨×¡×ª PDF:
document_properties_page_count=××¡×¤×¨ ××¤××:
document_properties_page_size=×××× ××¢×××:
document_properties_page_size_unit_inches=××× ×³
document_properties_page_size_unit_millimeters=××´×
document_properties_page_size_orientation_portrait=××××¨×
document_properties_page_size_orientation_landscape=××¨×××
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=×××ª×
document_properties_page_size_name_legal=××£ ××©×¤××
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=×ª×¦×××ª ××£ ××××¨×:
document_properties_linearized_yes=××
document_properties_linearized_no=××
document_properties_close=×¡×××¨×

print_progress_message=××¡×× ×××× × ××××¤×¡×â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=×××××

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=××¦××/××¡×ª×¨× ×©× ×¡×¨×× ××¦×
toggle_sidebar_notification.title=××××¤×ª ×ª×¦×××ª ×¡×¨×× ×¦× (××¡×× ×©×××× ×ª××× ×¢× ××× ××/×§××¦×× ××¦××¨×¤××)
toggle_sidebar_notification2.title=××××¤×ª ×ª×¦×××ª ×¡×¨×× ×¦× (××¡×× ×©×××× ×ª××× ×¢× ××× ××/×§××¦×× ××¦××¨×¤××/×©××××ª)
toggle_sidebar_label=××¦××/××¡×ª×¨× ×©× ×¡×¨×× ××¦×
document_outline.title=××¦××ª ×ª××× ××¢× ××× ×× ×©× ×××¡×× (××××¦× ××¤××× ××× ×××¨××× ×× ××¦××¦× ××ª ×× ××¤×¨××××)
document_outline_label=×ª××× ××¢× ××× ×× ×©× ×××¡××
attachments.title=××¦××ª ×¦×¨××¤××ª
attachments_label=×¦×¨××¤××ª
layers.title=××¦××ª ×©××××ª (××© ×××××¥ ××××¦× ××¤××× ××× ×××¤×¡ ××ª ×× ××©××××ª ×××¦× ××¨××¨×ª ×××××)
layers_label=×©××××ª
thumbs.title=××¦××ª ×ª×¦××× ××§××××
thumbs_label=×ª×¦××× ××§××××
current_outline_item.title=××¦×××ª ×¤×¨×× ×ª××× ××¢× ××× ×× ×× ××××
current_outline_item_label=×¤×¨×× ×ª××× ××¢× ××× ×× ×× ××××
findbar.title=×××¤××© ×××¡××
findbar_label=×××¤××©

additional_layers=×©××××ª × ××¡×¤××ª
# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=×¢××× {{page}}
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=×¢××× {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=×¢××× {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=×ª×¦××× ××§×××× ×©× ×¢××× {{page}}

# Find panel button title and messages
find_input.title=×××¤××©
find_input.placeholder=×××¤××© ×××¡××â¦
find_previous.title=××¦×××ª ××××¤×¢ ××§××× ×©× ××××××
find_previous_label=×§×××
find_next.title=××¦×××ª ××××¤×¢ ××× ×©× ××××××
find_next_label=×××
find_highlight=××××©×ª ××××
find_match_case_label=××ª×××ª ×××ª×××ª
find_entire_word_label=××××× ×©××××ª
find_reached_top=××××¢ ××¨××© ×××£, ×××©×× ×××××
find_reached_bottom=××××¢ ××¡××£ ×××£, ×××©×× ××××¢××
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]=×ª××¦×× {{current}} ××ª×× {{total}}
find_match_count[two]={{current}} ××ª×× {{total}} ×ª××¦×××ª
find_match_count[few]={{current}} ××ª×× {{total}} ×ª××¦×××ª
find_match_count[many]={{current}} ××ª×× {{total}} ×ª××¦×××ª
find_match_count[other]={{current}} ××ª×× {{total}} ×ª××¦×××ª
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=×××ª×¨ ×Ö¾{{limit}} ×ª××¦×××ª
find_match_count_limit[one]=×××ª×¨ ××ª××¦×× ×××ª
find_match_count_limit[two]=×××ª×¨ ×Ö¾{{limit}} ×ª××¦×××ª
find_match_count_limit[few]=×××ª×¨ ×Ö¾{{limit}} ×ª××¦×××ª
find_match_count_limit[many]=×××ª×¨ ×Ö¾{{limit}} ×ª××¦×××ª
find_match_count_limit[other]=×××ª×¨ ×Ö¾{{limit}} ×ª××¦×××ª
find_not_found=×××××× ×× × ××¦×

# Error panel labels
error_more_info=××××¢ × ××¡×£
error_less_info=×¤×××ª ××××¢
error_close=×¡×××¨×
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js ××¨×¡× {{version}} (×× ×××: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=××××¢×: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=×ª××× ×××¡× ××ª: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=×§×××¥: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=×©××¨×: {{line}}
rendering_error=×××¨×¢× ×©×××× ××¢×ª ×¢×××× ×××£.

# Predefined zoom values
page_scale_width=×¨××× ××¢×××
page_scale_fit=××ª××× ××¢×××
page_scale_auto=××¨××§ ××ª×¦××× ×××××××
page_scale_actual=×××× ××××ª×
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=×©××××

# Loading indicator messages
loading=×××¢×× ×â¦
loading_error=×××¨×¢× ×©×××× ××¢×ª ××¢×× ×ª ×Ö¾PDF.
invalid_file_error=×§×××¥ PDF ×¤××× ×× ×× ×ª×§××.
missing_file_error=×§×××¥ PDF ××¡×¨.
unexpected_response_error=×ª××××ª ×©×¨×ª ×× ×¦×¤×××.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[××¢×¨×ª {{type}}]
password_label=× × ×××× ××¡ ××ª ××¡×¡×× ××¤×ª×××ª ×§×××¥ PDF ××.
password_invalid=×¡×¡×× ×©××××. × × ×× ×¡××ª ×©× ××ª.
password_ok=×××©××¨
password_cancel=×××××

printing_not_supported=××××¨×: ×××¤×¡× ××× × × ×ª×××ª ×××××× ×××¤××¤× ××.
printing_not_ready=××××¨×: ××¡×× ×Ö¾PDF ×× × ××¢× ××××××× ×¢× ××¦× ×©×××¤×©×¨ ×××¤×¡×.
web_fonts_disabled=×××¤× × ×¨×©×ª ×× ×××¨×××: ×× × ××ª× ×××©×ª××© ××××¤× × PDF ×××××¢××.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
unsupported_feature_signatures=××¡×× PDF ×× ×××× ××ª××××ª ×××××××××ª. ×××××ª ××ª××××ª ××× × × ×ª××.
