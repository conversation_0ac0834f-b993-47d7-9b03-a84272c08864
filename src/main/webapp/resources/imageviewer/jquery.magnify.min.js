!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t("object"==typeof exports?require("jquery"):jQuery)}(function(t){"use strict";function i(i){return t(i).attr("data-src")?t(i).attr("data-src"):t(i).attr("href")}function e(t){var i=t.match(/\d+/g);return parseFloat(i[0])}function o(t,i,e,o){var a=o?t.h:t.w,n=o?t.w:t.h;(n>i.h||a>i.w)&&e.addClass("is-grab"),n<=i.h&&a<=i.w&&e.removeClass("is-grab")}function a(){return!!("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)}var n=a()?"touchstart":"mousedown",s=a()?"touchmove":"mousemove",h=a()?"touchend":"mouseup",l=".magnify",r=t(window),g=t(document),f={draggable:!0,resizable:!0,movable:!0,keyboard:!0,title:!0,modalWidth:320,modalHeight:320,fixedContent:!0,fixedModalSize:!1,initMaximized:!1,gapThreshold:.02,ratioThreshold:.1,minRatio:.1,maxRatio:16,headToolbar:["maximize","close"],footToolbar:["zoomIn","zoomOut","prev","fullscreen","next","actualSize","rotateRight"],icons:{minimize:"fa fa-window-minimize",maximize:"fa fa-window-maximize",close:"fa fa-close",zoomIn:"fa fa-search-plus",zoomOut:"fa fa-search-minus",prev:"fa fa-arrow-left",next:"fa fa-arrow-right",fullscreen:"fa fa-photo",actualSize:"fa fa-arrows-alt",rotateLeft:"fa fa-rotate-left",rotateRight:"fa fa-rotate-right",loader:"fa fa-spinner fa-pulse"},i18n:{minimize:"minimize",maximize:"maximize",close:"close",zoomIn:"zoom-in(+)",zoomOut:"zoom-out(-)",prev:"prev(←)",next:"next(→)",fullscreen:"fullscreen",actualSize:"actual-size(Ctrl+Alt+0)",rotateLeft:"rotate-left(Ctrl+,)",rotateRight:"rotate-right(Ctrl+.)"},multiInstances:!0,initEvent:"click",initAnimation:!0,fixedModalPos:!1,zIndex:1090,dragHandle:"",callbacks:{beforeOpen:t.noop,opened:t.noop,beforeClose:t.noop,closed:t.noop}},m=null,d=!1,c=!1,p=0,u=function(i,e){this.options=t.extend(!0,{},f,e),e&&t.isArray(e.footToolbar)&&(this.options.footToolbar=e.footToolbar),e&&t.isArray(e.headToolbar)&&(this.options.headToolbar=e.headToolbar),this.$el=t(i),this.isOpened=!1,this.isMaximized=!1,this.isRotated=!1,this.rotateAngle=0,this.imageData={},this.modalData={width:null,height:null,left:null,top:null},this.init(i,this.options)};u.prototype={init:function(e,o){var a=i(e);this.groupName=null;var n=t(e).attr("data-group"),s=g.find('[data-group="'+n+'"]');void 0!==n?(this.groupName=n,this.getImgGroup(s,a)):this.getImgGroup(m.not("[data-group]"),a),this.open(),this.loadImg(a),this.options.draggable&&this.draggable(this.$magnify,this.dragHandle,".magnify-button"),this.options.movable&&this.movable(this.$stage,this.$image),this.options.resizable&&this.resizable(this.$magnify,this.$stage,this.$image,this.options.modalWidth,this.options.modalHeight)},_creatBtns:function(i,e){var o="";return t.each(i,function(t,i){o+=e[i]}),o},_creatTitle:function(){return this.options.title?'<div class="magnify-title"></div>':""},creatDOM:function(){var t={minimize:'<button class="magnify-button magnify-button-minimize" title="'+this.options.i18n.minimize+'">                      <i class="'+this.options.icons.minimize+'" aria-hidden="true"></i>                    </button>',maximize:'<button class="magnify-button magnify-button-maximize" title="'+this.options.i18n.maximize+'">                      <i class="'+this.options.icons.maximize+'" aria-hidden="true"></i>                    </button>',close:'<button class="magnify-button magnify-button-close" title="'+this.options.i18n.close+'">                      <i class="'+this.options.icons.close+'" aria-hidden="true"></i>                    </button>',zoomIn:'<button class="magnify-button magnify-button-zoom-in" title="'+this.options.i18n.zoomIn+'">                      <i class="'+this.options.icons.zoomIn+'" aria-hidden="true"></i>                    </button>',zoomOut:'<button class="magnify-button magnify-button-zoom-out" title="'+this.options.i18n.zoomOut+'">                      <i class="'+this.options.icons.zoomOut+'" aria-hidden="true"></i>                    </button>',prev:'<button class="magnify-button magnify-button-prev" title="'+this.options.i18n.prev+'">                      <i class="'+this.options.icons.prev+'" aria-hidden="true"></i>                    </button>',next:'<button class="magnify-button magnify-button-next" title="'+this.options.i18n.next+'">                      <i class="'+this.options.icons.next+'" aria-hidden="true"></i>                    </button>',fullscreen:'<button class="magnify-button magnify-button-fullscreen" title="'+this.options.i18n.fullscreen+'">                      <i class="'+this.options.icons.fullscreen+'" aria-hidden="true"></i>                    </button>',actualSize:'<button class="magnify-button magnify-button-actual-size" title="'+this.options.i18n.actualSize+'">                      <i class="'+this.options.icons.actualSize+'" aria-hidden="true"></i>                    </button>',rotateLeft:'<button class="magnify-button magnify-button-rotate-left" title="'+this.options.i18n.rotateLeft+'">                      <i class="'+this.options.icons.rotateLeft+'" aria-hidden="true"></i>                    </button>',rotateRight:'<button class="magnify-button magnify-button-rotate-right" title="'+this.options.i18n.rotateRight+'">                      <i class="'+this.options.icons.rotateRight+'" aria-hidden="true"></i>                    </button>'};return'<div class="magnify-modal">                        <div class="magnify-header">                          <div class="magnify-toolbar magnify-head-toolbar">'+this._creatBtns(this.options.headToolbar,t)+"                          </div>"+this._creatTitle()+'                        </div>                        <div class="magnify-stage">                          <img class="magnify-image" src="" alt="" />                        </div>                        <div class="magnify-footer">                          <div class="magnify-toolbar magnify-foot-toolbar">'+this._creatBtns(this.options.footToolbar,t)+"                          </div>                        </div>                      </div>"},build:function(){var i=this.creatDOM(),e=t(i);this.$magnify=e,this.$header=e.find(".magnify-header"),this.$headToolbar=e.find(".magnify-head-toolbar"),this.$footer=e.find(".magnify-footer"),this.$footToolbar=e.find(".magnify-foot-toolbar"),this.$stage=e.find(".magnify-stage"),this.$title=e.find(".magnify-title"),this.$image=e.find(".magnify-image"),this.$close=e.find(".magnify-button-close"),this.$maximize=e.find(".magnify-button-maximize"),this.$minimize=e.find(".magnify-button-minimize"),this.$zoomIn=e.find(".magnify-button-zoom-in"),this.$zoomOut=e.find(".magnify-button-zoom-out"),this.$actualSize=e.find(".magnify-button-actual-size"),this.$fullscreen=e.find(".magnify-button-fullscreen"),this.$rotateLeft=e.find(".magnify-button-rotate-left"),this.$rotateRight=e.find(".magnify-button-rotate-right"),this.$prev=e.find(".magnify-button-prev"),this.$next=e.find(".magnify-button-next"),this.$stage.addClass("stage-ready"),this.$image.addClass("image-ready"),this._triggerHook("beforeOpen",this.$el),t("body").append(e)},open:function(){if(this.options.multiInstances||t(".magnify-modal").eq(0).remove(),!t(".magnify-modal").length&&this.options.fixedContent&&(t("html").css({overflow:"hidden"}),document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight))){var i=function(){var t=document.createElement("div");t.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(t);var i=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),i}();i&&t("html").css({"padding-right":i})}this.build(),this.addEvents(),this.setModalPos(this.$magnify),this.$magnify.css("z-index",p),this.dragHandle=this.options.dragHandle?this.$magnify.find(this.options.dragHandle):this.$magnify,this._triggerHook("opened",this.$el)},close:function(i){this._triggerHook("beforeClose",this.$el),this.$magnify.remove(),this.isOpened=!1,this.isMaximized=!1,this.isRotated=!1,this.rotateAngle=0;var e=!t(".magnify-modal").length;e&&this.options.fixedContent&&t("html").css({overflow:"","padding-right":""}),e&&this.options.multiInstances&&(p=this.options.zIndex),t(".magnify-modal").length||(g.off("keydown.magnify"),r.off("resize.magnify")),this._triggerHook("closed",this.$el)},setModalPos:function(t){var i=r.width(),e=r.height(),o=g.scrollLeft(),a=g.scrollTop(),n=this.options.modalWidth,s=this.options.modalHeight;this.options.initMaximized?(t.addClass("magnify-maximize"),t.css({width:"100%",height:"100%",left:0,top:0}),this.isOpened=!0,this.isMaximized=!0):t.css({width:n,height:s,left:(i-n)/2+o+"px",top:(e-s)/2+a+"px"})},setModalSize:function(t){var i=this,o=r.width(),a=r.height(),n=g.scrollLeft(),s=g.scrollTop(),h={left:this.$stage.css("left"),right:this.$stage.css("right"),top:this.$stage.css("top"),bottom:this.$stage.css("bottom"),borderLeft:this.$stage.css("border-left-width"),borderRight:this.$stage.css("border-right-width"),borderTop:this.$stage.css("border-top-width"),borderBottom:this.$stage.css("border-bottom-width")},l=t.width+e(h.left)+e(h.right)+e(h.borderLeft)+e(h.borderRight),f=t.height+e(h.top)+e(h.bottom)+e(h.borderTop)+e(h.borderBottom),m=(this.options.gapThreshold>0?this.options.gapThreshold:0)+1,d=Math.min(o/(l*m),a/(f*m),1),c=Math.max(l*d,this.options.modalWidth),p=Math.max(f*d,this.options.modalHeight),u={width:(c=this.options.fixedModalSize?this.options.modalWidth:Math.round(c))+"px",height:(p=this.options.fixedModalSize?this.options.modalHeight:Math.round(p))+"px",left:(o-c)/2+n+"px",top:(a-p)/2+s+"px"};this.options.initAnimation?this.$magnify.animate(u,function(){i.setImageSize(t)}):(this.$magnify.css(u),this.setImageSize(t)),this.isOpened=!0},setImageSize:function(i){var e={w:this.$stage.width(),h:this.$stage.height()},a=1;a=this.isRotated?Math.min(e.w/i.height,e.h/i.width,1):Math.min(e.w/i.width,e.h/i.height,1),this.$image.css({width:Math.ceil(i.width*a)+"px",height:Math.ceil(i.height*a)+"px",left:(e.w-Math.ceil(i.width*a))/2+"px",top:(e.h-Math.ceil(i.height*a))/2+"px"}),t.extend(this.imageData,{width:i.width*a,height:i.height*a,left:(e.w-i.width*a)/2,top:(e.h-i.height*a)/2}),o({w:this.$image.width(),h:this.$image.height()},{w:this.$stage.width(),h:this.$stage.height()},this.$stage,this.isRotated),this.$magnify.find(".magnify-loader").remove(),this.options.initAnimation&&this.$image.fadeIn()},loadImg:function(t){var i=this,e='<div class="magnify-loader"><i class="'+this.options.icons.loader+'"></i></div>';this.$magnify.append(e),this.options.initAnimation&&this.$image.hide(),this.$image.attr("src",t),function(t,i,e){var o=new Image;o.onload=function(){i(o)},o.onerror=function(){e(o)},o.src=t}(t,function(t){i.imageData={originalWidth:t.width,originalHeight:t.height},i.isMaximized||i.isOpened&&i.options.fixedModalPos?i.setImageSize(t):i.setModalSize(t),i.$stage.removeClass("stage-ready"),i.$image.removeClass("image-ready")},function(){i.$magnify.find(".magnify-loader").remove()}),this.options.title&&this.setImgTitle(t)},getImgGroup:function(e,o){var a=this;a.groupData=[],t(e).each(function(e,n){var s=i(this);a.groupData.push({src:s,caption:t(this).attr("data-caption")}),o===s&&(a.groupIndex=e)})},setImgTitle:function(t){var i=this.groupIndex,e=(e=this.groupData[i].caption)||function(t){return t.replace(/^.*?\/*([^/?]*)\.[a-z]+(\?.+|$)/gi,"$1")}(t);this.$title.text(e)},jump:function(t){this.groupIndex=this.groupIndex+t,this.jumpTo(this.groupIndex)},jumpTo:function(t){(t%=this.groupData.length)>=0?t%=this.groupData.length:t<0&&(t=(this.groupData.length+t)%this.groupData.length),this.groupIndex=t,this.loadImg(this.groupData[t].src)},wheel:function(t){t.preventDefault();var i=1;t.originalEvent.deltaY?i=t.originalEvent.deltaY>0?1:-1:t.originalEvent.wheelDelta?i=-t.originalEvent.wheelDelta/120:t.originalEvent.detail&&(i=t.originalEvent.detail>0?1:-1);var e=-i*this.options.ratioThreshold,o={x:t.originalEvent.clientX-this.$stage.offset().left+g.scrollLeft(),y:t.originalEvent.clientY-this.$stage.offset().top+g.scrollTop()};this.zoom(e,o,t)},zoom:function(t,i,e){(t=t<0?1/(1-t):1+t)>.95&&t<1.05&&(t=1),t=this.$image.width()/this.imageData.originalWidth*t,t=Math.max(t,this.options.minRatio),t=Math.min(t,this.options.maxRatio),this.zoomTo(t,i,e)},zoomTo:function(i,e,a){var n=this.$image,s=this.$stage,h={w:this.imageData.width,h:this.imageData.height,x:this.imageData.left,y:this.imageData.top},l={w:s.width(),h:s.height(),x:s.offset().left,y:s.offset().top},r=this.imageData.originalWidth*i,g=this.imageData.originalHeight*i,f=e.x-(e.x-h.x)/h.w*r,m=e.y-(e.y-h.y)/h.h*g,d=this.isRotated?(r-g)/2:0,c=this.isRotated?g:r,p=this.isRotated?r:g,u=l.w-r,y=l.h-g;
// The conditions with image rotate 90 degree drive me crazy alomst!
m=p<=l.h?(l.h-g)/2:m>d?d:m>y-d?m:y-d,f=c<=l.w?(l.w-r)/2:f>-d?-d:f>u+d?f:u+d,n.css({width:Math.round(r)+"px",height:Math.round(g)+"px",left:Math.round(f)+"px",top:Math.round(m)+"px"}),t.extend(this.imageData,{width:r,height:g,left:f,top:m}),o({w:Math.round(c),h:Math.round(p)},{w:l.w,h:l.h},this.$stage)},rotate:function(t){this.rotateAngle=this.rotateAngle+t,this.rotateAngle/90%2==0?this.isRotated=!1:this.isRotated=!0,this.rotateTo(this.rotateAngle)},rotateTo:function(t){this.$image.css({transform:"rotate("+t+"deg)"}),this.setImageSize({width:this.imageData.originalWidth,height:this.imageData.originalHeight}),this.$stage.removeClass("is-grab")},resize:function(){var t=this;return function(t,i){var e=null;return function(){var o=this,a=arguments;clearTimeout(e),e=setTimeout(function(){t.apply(o,a)},i)}}(function(){t.isOpened&&(t.isMaximized?t.setImageSize({width:t.imageData.originalWidth,height:t.imageData.originalHeight}):t.setModalSize({width:t.imageData.originalWidth,height:t.imageData.originalHeight}))},500)},maximize:function(){this.isMaximized?(this.$magnify.removeClass("magnify-maximize"),this.$magnify.css({width:this.modalData.width?this.modalData.width:this.options.modalWidth,height:this.modalData.height?this.modalData.height:this.options.modalHeight,left:this.modalData.left?this.modalData.left:(r.width()-this.options.modalWidth)/2+g.scrollLeft(),top:this.modalData.top?this.modalData.top:(r.height()-this.options.modalHeight)/2+g.scrollTop()}),this.isMaximized=!1):(this.modalData={width:this.$magnify.width(),height:this.$magnify.height(),left:this.$magnify.offset().left,top:this.$magnify.offset().top},this.$magnify.addClass("magnify-maximize"),this.$magnify.css({width:"100%",height:"100%",left:0,top:0}),this.isMaximized=!0),this.setImageSize({width:this.imageData.originalWidth,height:this.imageData.originalHeight})},fullscreen:function(){!function(t){t.requestFullscreen?t.requestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen():t.msRequestFullscreen&&t.msRequestFullscreen()}(this.$magnify[0])},keydown:function(t){if(!this.options.keyboard)return!1;var i=t.keyCode||t.which||t.charCode,e=t.ctrlKey||t.metaKey,o=t.altKey||t.metaKey;switch(i){case 37:this.jump(-1);break;case 39:this.jump(1);break;case 187:this.zoom(3*this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 189:this.zoom(3*-this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 61:this.zoom(3*this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 173:this.zoom(3*-this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 48:e&&o&&this.zoomTo(1,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 188:e&&this.rotate(-90);break;case 190:e&&this.rotate(90)}},addEvents:function(){var t=this;this.$close.off("click.magnify").on("click.magnify",function(i){t.close()}),this.$stage.off("wheel mousewheel DOMMouseScroll.magnify").on("wheel mousewheel DOMMouseScroll.magnify",function(i){t.wheel(i)}),this.$zoomIn.off("click.magnify").on("click.magnify",function(i){t.zoom(3*t.options.ratioThreshold,{x:t.$stage.width()/2,y:t.$stage.height()/2},i)}),this.$zoomOut.off("click.magnify").on("click.magnify",function(i){t.zoom(3*-t.options.ratioThreshold,{x:t.$stage.width()/2,y:t.$stage.height()/2},i)}),this.$actualSize.off("click.magnify").on("click.magnify",function(i){t.zoomTo(1,{x:t.$stage.width()/2,y:t.$stage.height()/2},i)}),this.$prev.off("click.magnify").on("click.magnify",function(){t.jump(-1)}),this.$fullscreen.off("click.magnify").on("click.magnify",function(){t.fullscreen()}),this.$next.off("click.magnify").on("click.magnify",function(){t.jump(1)}),this.$rotateLeft.off("click.magnify").on("click.magnify",function(){t.rotate(-90)}),this.$rotateRight.off("click.magnify").on("click.magnify",function(){t.rotate(90)}),this.$maximize.off("click.magnify").on("click.magnify",function(){t.maximize()}),g.off("keydown.magnify").on("keydown.magnify",function(i){t.keydown(i)}),r.on("resize.magnify",t.resize())},_triggerHook:function(i,e){this.options.callbacks[i]&&this.options.callbacks[i].apply(this,t.isArray(e)?e:[e])}},t.magnify={instance:u.prototype},t.fn.magnify=function(i){m=t(this);for(var e in i)"string"!=typeof i[e]||isNaN(i[e])||(i[e]=parseFloat(i[e]));var o=t.extend(!0,{},f,i);return p=o.zIndex,"string"==typeof i||("dblclick"===o.initEvent&&m.off("click.magnify").on("click.magnify",function(t){t.preventDefault(),t.stopPropagation()}),m.off(o.initEvent+l).on(o.initEvent+l,function(e){e.preventDefault(),e.stopPropagation(),t(this).data("magnify",new u(this,i))})),m},g.on("click.magnify","[data-magnify]",function(i){m=t("[data-magnify]"),i.preventDefault(),t(this).data("magnify",new u(this,{}))});t.extend(u.prototype,{draggable:function(i,e,o){var a=this,r=!1,f=0,m=0,u=0,y=0,x=function(e){if((e=e||window.event).preventDefault(),r&&!d&&!c&&!a.isMaximized){var o="touchmove"===e.type?e.originalEvent.targetTouches[0].pageX:e.clientX,n="touchmove"===e.type?e.originalEvent.targetTouches[0].pageY:e.clientY,s=o-f,h=n-m;t(i).css({left:s+u+"px",top:h+y+"px"})}},w=function(t){g.off(s+l,x).off(h+l,w),r=!1};t(e).on(n+l,function(e){if(e=e||window.event,a.options.multiInstances&&i.css("z-index",++p),t(e.target).closest(o).length)return!0;r=!0,f="touchstart"===e.type?e.originalEvent.targetTouches[0].pageX:e.clientX,m="touchstart"===e.type?e.originalEvent.targetTouches[0].pageY:e.clientY,u=t(i).offset().left,y=t(i).offset().top,g.on(s+l,x).on(h+l,w)})}});t.extend(u.prototype,{movable:function(i,e){var o=this,a=!1,r=0,f=0,m=0,c=0,p=0,u=0,y=0,x=function(i){if((i=i||window.event).preventDefault(),a){var n="touchmove"===i.type?i.originalEvent.targetTouches[0].pageX:i.clientX,s="touchmove"===i.type?i.originalEvent.targetTouches[0].pageY:i.clientY,h=n-r,l=s-f,g=h+m,d=l+c;u>0?l+c>y?d=y:l+c<-u+y&&(d=-u+y):d=c,p>0?h+m>-y?g=-y:h+m<-p-y&&(g=-p-y):g=m,t(e).css({left:g+"px",top:d+"px"}),t.extend(o.imageData,{left:g,top:d})}},w=function(i){g.off(s+l,x).off(h+l,w),a=!1,d=!1,t("html,body,.magnify-modal,.magnify-stage,.magnify-button,.magnify-resizable-handle").removeClass("is-grabbing")};t(i).on(n+l,function(n){(n=n||window.event).preventDefault();var b=t(e).width(),v=t(e).height(),z=t(i).width(),$=t(i).height();r="touchstart"===n.type?n.originalEvent.targetTouches[0].pageX:n.clientX,f="touchstart"===n.type?n.originalEvent.targetTouches[0].pageY:n.clientY,y=o.isRotated?(b-v)/2:0,p=o.isRotated?v-z:b-z,u=o.isRotated?b-$:v-$,a=p>0||u>0,d=p>0||u>0,m=t(e).position().left-y,c=t(e).position().top+y,i.hasClass("is-grab")&&t("html,body,.magnify-modal,.magnify-stage,.magnify-button,.magnify-resizable-handle").addClass("is-grabbing"),g.on(s+l,x).on(h+l,w)})}});t.extend(u.prototype,{resizable:function(i,e,a,r,f){var m=this,d=t('<div class="magnify-resizable-handle magnify-resizable-handle-e"></div>'),p=t('<div class="magnify-resizable-handle magnify-resizable-handle-w"></div>'),u=t('<div class="magnify-resizable-handle magnify-resizable-handle-s"></div>'),y=t('<div class="magnify-resizable-handle magnify-resizable-handle-n"></div>'),x=t('<div class="magnify-resizable-handle magnify-resizable-handle-se"></div>'),w=t('<div class="magnify-resizable-handle magnify-resizable-handle-sw"></div>'),b=t('<div class="magnify-resizable-handle magnify-resizable-handle-ne"></div>'),v=t('<div class="magnify-resizable-handle magnify-resizable-handle-nw"></div>'),z={e:d,s:u,se:x,n:y,w:p,nw:v,ne:b,sw:w};t(i).append(d,p,u,y,x,w,b,v);var $=!1,M=0,T=0,D={w:0,h:0,l:0,t:0},k={w:0,h:0,l:0,t:0},I={w:0,h:0,l:0,t:0},R=0,E=0,H=0,C="",S=function(o,n){(n=n||window.event).preventDefault(),$=!0,c=!0,M="touchstart"===n.type?n.originalEvent.targetTouches[0].pageX:n.clientX,T="touchstart"===n.type?n.originalEvent.targetTouches[0].pageY:n.clientY,D={w:t(i).width(),h:t(i).height(),l:t(i).offset().left,t:t(i).offset().top},k={w:t(e).width(),h:t(e).height(),l:t(e).offset().left,t:t(e).offset().top},I={w:t(a).width(),h:t(a).height(),l:t(a).position().left,t:t(a).position().top},R=m.isRotated?(I.w-I.h)/2:0,E=m.isRotated?I.h:I.w,H=m.isRotated?I.w:I.h,C=o,t("html,body,.magnify-modal,.magnify-stage,.magnify-button").css("cursor",o+"-resize"),g.on(s+l,O).on(h+l,W)},O=function(e){if((e=e||window.event).preventDefault(),$&&!m.isMaximized){var o="touchmove"===e.type?e.originalEvent.targetTouches[0].pageX:e.clientX,n="touchmove"===e.type?e.originalEvent.targetTouches[0].pageY:e.clientY,s=o-M,h=n-T,l=function(t,i,e){var o=-i+D.w>r?i+D.l:D.l+D.w-r,a=-e+D.h>f?e+D.t:D.t+D.h-f;return{e:{width:Math.max(i+D.w,r)+"px"},s:{height:Math.max(e+D.h,f)+"px"},se:{width:Math.max(i+D.w,r)+"px",height:Math.max(e+D.h,f)+"px"},w:{width:Math.max(-i+D.w,r)+"px",left:o+"px"},n:{height:Math.max(-e+D.h,f)+"px",top:a+"px"},nw:{width:Math.max(-i+D.w,r)+"px",height:Math.max(-e+D.h,f)+"px",top:a+"px",left:o+"px"},ne:{width:Math.max(i+D.w,r)+"px",height:Math.max(-e+D.h,f)+"px",top:a+"px"},sw:{width:Math.max(-i+D.w,r)+"px",height:Math.max(e+D.h,f)+"px",left:o+"px"}}[t]}(C,s,h);t(i).css(l);var g=function(i,e,o){var n=e+D.w>r?k.w-E+e-R:r-(D.w-k.w)-E-R,s=o+D.h>f?k.h-H+o+R:f-(D.h-k.h)-H+R,h=-e+D.w>r?k.w-E-e-R:r-(D.w-k.w)-E-R,l=-o+D.h>f?k.h-H-o+R:f-(D.h-k.h)-H+R,g=t(a).position().left-R,m=t(a).position().top+R;return{e:{left:n>=-R?(n-R)/2+"px":g>n?g+"px":n+"px"},s:{top:s>=R?(s+R)/2+"px":m>s?m+"px":s+"px"},se:{top:s>=R?(s+R)/2+"px":m>s?m+"px":s+"px",left:n>=-R?(n-R)/2+"px":g>n?g+"px":n+"px"},w:{left:h>=-R?(h-R)/2+"px":g>h?g+"px":h+"px"},n:{top:l>=R?(l+R)/2+"px":m>l?m+"px":l+"px"},nw:{top:l>=R?(l+R)/2+"px":m>l?m+"px":l+"px",left:h>=-R?(h-R)/2+"px":g>h?g+"px":h+"px"},ne:{top:l>=R?(l+R)/2+"px":m>l?m+"px":l+"px",left:n>=-R?(n-R)/2+"px":g>n?g+"px":n+"px"},sw:{top:s>=R?(s+R)/2+"px":m>s?m+"px":s+"px",left:h>=-R?(h-R)/2+"px":g>h?g+"px":h+"px"}}[i]}(C,s,h);t(a).css(g)}},W=function(i){g.off(s+l,O).off(h+l,W),c&&o({w:E,h:H},{w:t(e).width(),h:t(e).height()},e),$=!1,c=!1,t("html,body,.magnify-modal,.magnify-stage,.magnify-button").css("cursor","")};t.each(z,function(t,i){i.on(n+l,function(i){S(t,i)})})}})});