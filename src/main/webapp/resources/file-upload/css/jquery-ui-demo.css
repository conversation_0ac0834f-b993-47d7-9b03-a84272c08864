@charset "UTF-8";
/*
 * jQuery File Upload Demo CSS
 * https://github.com/blueimp/jQuery-File-Upload
 *
 * Copyright 2013, <PERSON>
 * https://blueimp.net
 *
 * Licensed under the MIT license:
 * https://opensource.org/licenses/MIT
 */

body {
  max-width: 750px;
  margin: 0 auto;
  padding: 1em;
  font-family: "Lucida Grande", "Lucida Sans Unicode", Arial, sans-serif;
  font-size: 1em;
  line-height: 1.4em;
  background: #222;
  color: #fff;
  -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
}
a {
  color: orange;
  text-decoration: none;
}
img {
  border: 0;
  vertical-align: middle;
}
h1 {
  line-height: 1em;
}
blockquote {
  padding: 0 0 0 15px;
  margin: 0 0 20px;
  border-left: 5px solid #eee;
}
table {
  width: 100%;
  margin: 10px 0;
}

.fileupload-progress {
	margin: 10px 0;
}
.fileupload-progress .progress-extended {
	margin-top: 5px;
}
.error {
  color: red;
}

@media (min-width: 481px) {
  .navigation {
    list-style: none;
    padding: 0;
  }
  .navigation li {
    display: inline-block;
  }
  .navigation li:not(:first-child):before {
    content: "| ";
  }
}
