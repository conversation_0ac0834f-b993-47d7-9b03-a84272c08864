/**
 * Created by a<PERSON><PERSON> on 5/2/18.
 */


$(document).ready(function () {


    // FORM VALIDATION FEEDBACK ICONS
    // =================================================================


    //tableTypes

    // =================================================================


    $('#frmMain')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                "inspectionDto.inspectionId": {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                placeOfinspection: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        },
                        callback: ValidationForSpecialChar
                    }
                },
                currentLocation: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },
                // callCenterRemark: {
                //     validators: {
                //         callback: ValidationForSpecialChar
                //     }
                // },
                "assessorDto.assessorContactNo": {
                    validators: {
                        regexp: {
                            regexp: /^(\d{10},)*\d{10}$/,
                            message: 'Invalid Contact No (E.g. 0712345678,0112345678)'
                        }
                    }
                },
                /*"districtDto.districtCode": {
                    validators: {
                        callback: dropDownValidationForText
                    }
                },*/
                /*"cityDto.gramaCode": {
                    validators: {
                        callback: dropDownValidation
                    }
                },*/
                "assessorDto.code": {
                    validators: {
                        callback: dropDownValidationForText
                    }
                },
                "reassigningReasonDto.id": {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                "rejectReasonDto.reasonId": {
                    validators: {
                        callback: dropDownValidation
                    }
                }, "inspectionReasonDto.reasonId": {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                "rteCode": {
                    validators: {
                        callback: dropDownValidationForText
                    }
                }, "garageContactNo": {
                    validators: {
                        callback: numericValidationCallback
                    }
                }


            }
        }).on('success.form.fv', function (e) {
        // Prevent form submission
        e.preventDefault();

        var $form = $(e.target),
            fv = $form.data('formValidation');

        $.ajax({
            url: contextPath + "/AssessorAllocationController/validate",
            type: 'POST',
            data: $form.serialize(),
            success: function (result) {

                var obj = JSON.parse(result);
                if (obj.errorCode == '200') {
                    $.ajax({
                        url: contextPath + "/AssessorAllocationController/save",
                        type: 'POST',
                        data: $form.serialize(),
                        success: function (response) {
                            window.parent.notify('Successfully saved', 'success');
                            $('#addbtn').hide();
                            var currentUrl = window.location.href.split('?')[0];
                            var urlParams = new URLSearchParams(window.location.search);
                            var claimNo = urlParams.get('claimNo') || $('#claimId').val();
                            var formType = urlParams.get('FORM_TYPE') || 'ASSIGN';
                            var onsiteReview = urlParams.get('onsiteReview') || 'false';
                            window.location.href = currentUrl + '?claimNo=' + claimNo + '&FORM_TYPE=' + formType + '&onsiteReview=' + onsiteReview;
                        },
                        error: function (xhr, status, error) {
                            window.parent.notify('Error occurred while saving. Please try again.', 'danger');
                        }
                    });

                } else {
                    window.parent.notify(obj.message, 'danger');

                }

            }
        });
    });


});