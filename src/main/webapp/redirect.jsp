<%-- 
    Document   : redirect
    Created on : Nov 10, 2014, 1:25:40 PM
    Author     : User
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<% int appid = 0;
    String next = "";
    try {
        String p_app_id = (String) session.getAttribute("P_APP_ID");

        if ("Hr64jJWQKCDl0Jf8kc6I7g==".equals(p_app_id)) {
            appid = 2;
        }

        switch (appid) {
            case 1:
                next = "/WEB-INF/jsp/admin/user_password_reset/init_password_change.jsp";
                break;
            case 2:
                next = "/WEB-INF/jsp/admin/user_password_reset/password_change.jsp";
                break;
            default:
                out.print("Invalid request");
        }
    } catch (Exception e) {
        out.print("ERROR : " + e);
    }

    try {
        RequestDispatcher rd = request.getRequestDispatcher(next);
        rd.include(request, response);
    } catch (Exception e) {
        out.print("ERROR : " + e);
    }
%>

