package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.admin.admin.dto.BankDetailsDto;
import com.misyn.mcms.admin.admin.dto.BankDetailsSearchResponseDto;
import com.misyn.mcms.claim.dao.BankDetailsDao;
import com.misyn.mcms.claim.dao.ClaimCalculationSheetPayeeNameDao;
import com.misyn.mcms.claim.dao.ClaimDocumentTypeDao;
import com.misyn.mcms.claim.dao.SupplierDetailsMasterDao;
import com.misyn.mcms.claim.dao.impl.BankDetailsDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimCalculationSheetPayeeNameDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimDocumentTypeDaoImpl;
import com.misyn.mcms.claim.dao.impl.SupplierDetailsMasterDaoImpl;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeNameDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.BankDetailsService;
import com.misyn.mcms.claim.service.CalculationSheetService;
import com.misyn.mcms.claim.service.SupplyOrderService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.ListBoxItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class BankDetailsServiceImpl extends AbstractBaseService<BankDetailsServiceImpl> implements BankDetailsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BankDetailsServiceImpl.class);

    final String LOLC_NAME = "LOLC GENERAL INSURANCE LTD";

    private final ClaimCalculationSheetPayeeNameDao claimCalculationSheetPayeeNameDao = new ClaimCalculationSheetPayeeNameDaoImpl();
    private final ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private final BankDetailsDao bankDetailsDao = new BankDetailsDaoImpl();
    private final SupplyOrderService supplyOrderService = new SupplyOrderServiceImpl();
    private final CalculationSheetService calculationSheetService = new CalculationSheetServiceImpl();


    private List<ListBoxItem> getSelectListBox(Connection connection, String tblName, String valueField, String
            lableField, String searchKey, String orderField) throws Exception {
        List<ListBoxItem> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        String strSQL;
        try {
            strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName).concat(" ")
                    .concat(searchKey).concat(" ORDER BY ").concat(orderField.isEmpty() ? lableField : orderField);
            ps = connection.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                list.add(new ListBoxItem(rs.getString(valueField), rs.getString(lableField)));
            }
            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return list;
    }


    @Override
    public List<ClaimCalculationSheetPayeeNameDto> getPayeeTypeList() throws Exception {
        List<ClaimCalculationSheetPayeeNameDto> list = new ArrayList<>();
        try (Connection connection = getJDBCConnection()) {
            list = claimCalculationSheetPayeeNameDao.searchAll(connection);
        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving payee type list", e);
            throw new Exception("Error occurred while retrieving payee type list", e);
        }
        return list;
    }

    @Override
    public Map<Integer, String> getInstrumentTypeList() throws Exception {
        Connection connection = null;
        Map<Integer, String> instrumentTypeDetails = new HashMap<>();
        try {
            connection = getJDBCConnection();

            instrumentTypeDetails = bankDetailsDao.findAllInstrumentTypeByID(connection);
        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving instrument type list", e);
            throw new Exception("Error occurred while retrieving instrument type list", e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return instrumentTypeDetails;
    }

    @Override
    public Integer saveBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        Integer generatedCardIndex = 0;

        try {
            connection = getJDBCConnection();

            if (bankDetailsDao.isVerified(connection, bankDetailsDto)) {
                throw new Exception("Bank details have already been verified and cannot be updated.");
            }

            if (bankDetailsDao.isAvailable(connection, bankDetailsDto)) {
                generatedCardIndex = bankDetailsDao.updateByID(connection, bankDetailsDto);
                logTransaction(connection, bankDetailsDto, user, "Bank Details Updated");
            } else {
                generatedCardIndex = bankDetailsDao.saveByID(connection, bankDetailsDto);
                logTransaction(connection, bankDetailsDto, user, "New Bank Details Inserted");
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while saving bank details", e);
            throw new Exception("Failed to save bank details. Please try again.", e);
        } finally {
            releaseJDBCConnection(connection);
        }

        return generatedCardIndex;
    }


    private void logTransaction(Connection connection, BankDetailsDto bankDetailsDto, UserDto user, String action) throws Exception {
        String userId = user.getUserId();
        Integer claimNo = bankDetailsDto.getClaimNo();
        String instrumentType = bankDetailsDto.getInstrumentTypeDesc();
        String payeeType = bankDetailsDto.getPayeeTypeDesc();
        String payeeName = bankDetailsDto.getPayeeNameDesc();

        String message = String.format("%s %s with the following information  >  \n" +
                                "   (  INSTRUMENT TYPE : %s, \n" +
                                "   PAYEE TYPE : %s, \n" +
                                "   PAYEE NAME : %s  )",
                userId, action, instrumentType, payeeType, payeeName);
        saveClaimsLogs(connection, claimNo, user, "Bank Details", message);
    }


    @Override
    public void deleteBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;

        try {
            connection = getJDBCConnection();

            bankDetailsDao.deleteByID(connection, bankDetailsDto);
            logTransaction(connection, bankDetailsDto, user, "Bank details deleted");
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting bank details", e);
            throw new Exception("Error occurred while deleting bank details", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Integer updateBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        Integer   generatedCardIndex=0;

        try {
            connection = getJDBCConnection();

            generatedCardIndex= bankDetailsDao.updateByID(connection, bankDetailsDto);

        } catch (Exception e) {
            LOGGER.error("Error occurred while updating bank details", e);
            throw new Exception("Error occurred while updating bank details", e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return generatedCardIndex;
    }

    @Override
    public void updateVerification(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;

        try {
            connection = getJDBCConnection();
            boolean isDocUploaded = bankDetailsDao.isDocUploaded(connection, bankDetailsDto);

            if (isDocUploaded) {
                bankDetailsDao.verifyByID(connection, bankDetailsDto);
                logTransaction(connection, bankDetailsDto, user, "Bank details verified");
            } else {
                LOGGER.error("Document not uploaded");
                throw new Exception("Document not uploaded");
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while updating verification status", e);
            throw new Exception("Error occurred while updating verification status", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateRejection(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;

        try {
            connection = getJDBCConnection();

            bankDetailsDao.rejectByID(connection, bankDetailsDto);
            logTransaction(connection, bankDetailsDto, user, "Bank details rejected");

        } catch (Exception e) {
            LOGGER.error("Error occurred while updating verification status", e);
            throw new Exception("Error occurred while updating verification status", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateDocRefNo(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;

        try {
            connection = getJDBCConnection();

            bankDetailsDao.updateDocRefNO(connection, bankDetailsDto);
            logTransaction(connection, bankDetailsDto, user, "Bank details uploaded");
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating document reference number", e);
            throw new Exception("Error occurred while updating document reference number", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Integer getUploadedDocRef(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;

        try {
            connection = getJDBCConnection();

            return bankDetailsDao.findDocRefNO(connection, bankDetailsDto);

        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving uploaded document reference number", e);
            throw new Exception("Error occurred while retrieving uploaded document reference number", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<BankDetailsDto> getPrevBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;

        try {
            connection = getJDBCConnection();

            List<BankDetailsDto> bankDetailsDtos = bankDetailsDao.findAllByID(connection, bankDetailsDto);

            // Use a single stream processing pipeline to set common properties and populate the payeeNameList map for each BankDetailsDto
            bankDetailsDtos.forEach(dto -> {
                dto.setCustomerName(bankDetailsDto.getCustomerName());
                dto.setPolicyChannelType(bankDetailsDto.getPolicyChannelType());
                dto.setClaimNo(bankDetailsDto.getClaimNo());
                dto.setLoggedUser(bankDetailsDto.getLoggedUser());
                dto.setLoggedUserType(bankDetailsDto.getLoggedUserType());
                // Populate the payeeNameList map
                try {
                    List<ListBoxItem> payeeNameList = getPayeeNameListById(dto);
                    dto.setPayeeNameList(payeeNameList);
                } catch (Exception e) {
                    LOGGER.error("Error occurred while populating payeeNameList for BankDetailsDto: " + dto.getId(), e);
                }
            });


            return bankDetailsDtos;

        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving previous bank details", e);
            throw new Exception("Error occurred while retrieving previous bank details", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }


    @Override
    public BankDetailsDto getInsuredBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        BankDetailsDto dto = null;
        boolean isDocUploaded =false;


        try {
            connection = getJDBCConnection();
            if(bankDetailsDto.getId() != null && bankDetailsDto.getId() > AppConstant.ZERO_INT){
               isDocUploaded = bankDetailsDao.isDocUploaded(connection, bankDetailsDto);
                if(!isDocUploaded){
                    dto = bankDetailsDao.findInsuredBankDetails(connection, bankDetailsDto);
                }
            }else {
                dto = bankDetailsDao.findInsuredBankDetails(connection, bankDetailsDto);
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving insured bank details", e);
            throw new Exception("Error occurred while retrieving insured bank details", e);
        } finally {
            releaseJDBCConnection(connection);
        }

        return dto;
    }

    @Override
    public BankDetailsSearchResponseDto getBankDetails(BankDetailsDto bankDetailsDto, UserDto user, Integer payeeType, String payeeName) throws Exception {
        Connection connection = null;
        Integer payeeTypeId = null;
        String payeeNameId = null;
        BankDetailsSearchResponseDto searchResponseDto = new BankDetailsSearchResponseDto();

        try {
            connection = getJDBCConnection();

            // Retrieve payee type ID using streams
            List<ClaimCalculationSheetPayeeNameDto> payeeTypeList = getPayeeTypeList();
//            payeeTypeId = payeeTypeList.stream()
//                    .filter(dto -> dto.getPayeeName().equals(payeeType))
//                    .map(ClaimCalculationSheetPayeeNameDto::getCalSheetPayeeNameId)
//                    .findFirst()
//                    .orElse(null);

            bankDetailsDto.setPayeeType(payeeType);

            // Retrieve payee name ID using streams
            List<ListBoxItem> payeeNameListById = getPayeeNameListById(bankDetailsDto);
//            payeeNameId = (String) payeeNameListById.stream()
//                    .filter(listBoxItem -> listBoxItem.getLable().equals(payeeName))
//                    .map(ListBoxItem::getValue)
//                    .findFirst()
//                    .orElse(null);

            bankDetailsDto.setPayeeName(payeeName);


            List<BankDetailsDto> bankDetailsDtos = bankDetailsDao.findByPayeeTypeAndPayeeName(connection, bankDetailsDto);
            Integer lastIndex=AppConstant.ZERO_INT;
            lastIndex = bankDetailsDao.findLastIndex(connection, bankDetailsDto);


            // Use a single stream processing pipeline to set common properties and populate the payeeNameList map for each BankDetailsDto
            bankDetailsDtos.forEach(dto -> {
                dto.setCustomerName(bankDetailsDto.getCustomerName());
                dto.setPolicyChannelType(bankDetailsDto.getPolicyChannelType());
                dto.setClaimNo(bankDetailsDto.getClaimNo());
                dto.setLoggedUser(bankDetailsDto.getLoggedUser());
                dto.setLoggedUserType(bankDetailsDto.getLoggedUserType());
                // Populate the payeeNameList map
                try {
                    List<ListBoxItem> payeeNameList = getPayeeNameListById(dto);
                    dto.setPayeeNameList(payeeNameList);
                } catch (Exception e) {
                    LOGGER.error("Error occurred while populating payeeNameList for BankDetailsDto: " + dto.getId(), e);
                }
            });
            searchResponseDto.setBankDetailsDtoList(bankDetailsDtos);
            searchResponseDto.setPayeeTypeList(payeeTypeList);
            searchResponseDto.setPayeeNameList(payeeNameListById);
            searchResponseDto.setPayeeNameId(payeeName);
            searchResponseDto.setPayeeTypeId(payeeType);
            searchResponseDto.setLastID(lastIndex);
            return searchResponseDto;

        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving previous bank details", e);
            throw new Exception("Error occurred while retrieving previous bank details", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isAlreadyExistBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception {
        Connection connection = null;

        try {
            connection = getJDBCConnection();

            return bankDetailsDao.isAvailable(connection, bankDetailsDto);

        } catch (Exception e) {
            LOGGER.error("Error occurred while checking for existing bank details", e);
            throw new Exception("Error occurred while checking for existing bank details", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isBankDetailsVerified(Integer claimNo, Integer calSheetId, UserDto user) throws Exception {
        Connection connection = null;
        boolean isVerifiedAll = true;

        try {
            connection = getJDBCConnection();

            ClaimCalculationSheetMainDto calculationSheet = calculationSheetService.getCalculationSheet(claimNo, calSheetId);
            List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos = calculationSheet.getClaimCalculationSheetPayeeDtos();
            for (ClaimCalculationSheetPayeeDto dto : claimCalculationSheetPayeeDtos) {
                String payeeName = dto.getPayeeDesc();
                Integer payeeType = dto.getPayeeId(); // Added missing semicolon
                BankDetailsDto detailsDto = new BankDetailsDto();
                detailsDto.setClaimNo(claimNo);
                detailsDto.setPayeeType(payeeType);
                detailsDto.setPayeeName(payeeName);
                List<BankDetailsDto> byPayeeTypeAndPayeeName = bankDetailsDao.findByPayeeTypeAndPayeeName(connection, detailsDto);
                for (BankDetailsDto bankDetailsDto : byPayeeTypeAndPayeeName) {
                    if (!("Y".equals(bankDetailsDto.getUploadStatus()) && "Y".equals(bankDetailsDto.getVerifyStatus()))) {
                        isVerifiedAll = false;
                        break;
                    }
                }
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while checking for existing bank details", e);
            throw new Exception("Error occurred while checking for existing bank details", e);
        } finally {
            releaseJDBCConnection(connection);
        }


        return isVerifiedAll;
    }


    @Override
    public List<ListBoxItem> getPayeeNameListById(BankDetailsDto bankDetailsDto) throws Exception {
        Connection connection = null;
        List<ListBoxItem> listBoxItems = new ArrayList<>();
        String SEARCH_SQL;
        Integer payeeType = bankDetailsDto.getPayeeType();
        String customerName = bankDetailsDto.getCustomerName();
        String policyChannelType = bankDetailsDto.getPolicyChannelType();
        Integer claimNo = bankDetailsDto.getClaimNo();
        SupplierDetailsMasterDao supplierDetailsMasterDao = new SupplierDetailsMasterDaoImpl();

        try {
            connection = getJDBCConnection();
            switch (payeeType) {
                case 1://Insured
                    listBoxItems.add(new ListBoxItem(customerName, customerName));
                    break;
                case 2://Garage
                    SEARCH_SQL = " WHERE V_INST_TYPE='WS'";
                    listBoxItems = this.getSelectListBox(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main",
                            "N_REF_NO", "V_COMPANY_NAME",
                            SEARCH_SQL, AppConstant.EMPTY_STRING);

                    break;
                case 3://Leasing Company
                    SEARCH_SQL = " WHERE V_INST_TYPE='02'";
                    listBoxItems = this.getSelectListBox(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main",
                            "N_REF_NO", "V_COMPANY_NAME",
                            SEARCH_SQL, AppConstant.EMPTY_STRING);
                    break;
                case 4://NCB Reversal
                    listBoxItems.add(new ListBoxItem(LOLC_NAME, LOLC_NAME));
                    break;
                case 5://Insurance Company
                    listBoxItems.add(new ListBoxItem(LOLC_NAME, LOLC_NAME));
                    break;
                case 6://Government Authorities
                    break;
                case 7://Third Party Individual
                    break;
                case 8://Third Party Coorporate
                    break;
                case 9://Supplier

                    policyChannelType = supplyOrderService.getPolicyChannelType(claimNo);
                    listBoxItems = getListBoxItemList(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main ", "N_REF_NO", "V_COMPANY_NAME", "V_INST_TYPE='WS' ORDER BY V_COMPANY_NAME ");
                    break;
                case 10://Other
                    SEARCH_SQL = " WHERE V_INST_TYPE NOT IN('02','WS','POL','04','24','20','EVENT','SUC','Concrete','16')";
                    listBoxItems = this.getSelectListBox(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main",
                            "N_REF_NO", "V_COMPANY_NAME",
                            SEARCH_SQL, AppConstant.EMPTY_STRING);
                    break;

            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving payee name list by ID", e);
            throw new Exception("Error occurred while retrieving payee name list by ID", e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return listBoxItems;
    }

    public List<ListBoxItem> getListBoxItemList(String tblName, String valueField, String lableField, String searchKey) {
        List<ListBoxItem> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;
        try {
            if (searchKey.isEmpty()) {
                strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName);
            } else {
                strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName).concat(" WHERE  ").concat(searchKey);
            }
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                ListBoxItem listBoxItem = new ListBoxItem(rs.getString(valueField), rs.getString(lableField));
                list.add(listBoxItem);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return list;
    }

}
