package com.misyn.mcms.claim.service;

import com.misyn.mcms.admin.admin.dto.AccessUserTypeDto;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface UserService {

    UserMasterDto saveClaimUser(UserMasterDto userDto) throws MisynJDBCException;

    UserMasterDto updateClaimUser(UserMasterDto userDto) throws MisynJDBCException;

    DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, Integer type) throws Exception;

    List<ClaimDepartmentDto> getAllDepartments() throws Exception;

    List<AssessorDto> getActiveAssessors() throws Exception;
}
