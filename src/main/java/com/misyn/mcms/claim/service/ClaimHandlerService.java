package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 6/20/18.
 */
public interface ClaimHandlerService extends BaseService<ClaimHandlerDto> {

    DataGridDto getClaimHandlerDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer priority);

    DataGridDto getLetterPanelList(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    DataGridDto getClaimHandlerDataGridDtoByCalsheetStatus(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String calsheetStatus);

    ClaimHandlerDto searchByClaimNo(Integer id) throws Exception;

    List<LeasingCompanyDto> getLeasingCompanyDetails() throws Exception;

    boolean updateFinancialInfo(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception;

    boolean updateLiabilityCheck(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception;

    boolean saveRemark(SpecialRemarkDto specialRemarkDto, UserDto user, String type) throws Exception;

    List<ClaimLogsDto> getLogList(Integer claimNo) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNo(Integer claimNo) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNoAndDepartmentId(Integer claimNo, Integer departmentId) throws Exception;

    List<SpecialRemarkDto> searchAllRemarksByClaimNo(Integer claimNo) throws Exception;

    List<SpecialRemarkDto> searchDepartmentRemarksByClaimNo(Integer claimNo) throws Exception;

    ClaimDocumentDto checkedDocument(ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynJDBCException;

    ClaimDocumentDto holdDocument(ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynJDBCException;

    ClaimDocumentDto rejectDocument(ClaimDocumentDto claimDocumentDto, UserDto user, String emailVal, Integer claimNo) throws MisynJDBCException;

    ClaimDocumentDto getClaimDocumentDto(Integer refId);

    void storeFile(Integer claimNo, UserDto sessionUser, String remark) throws Exception;

    void restoreFile(Integer claimNo, UserDto sessionUser, String remark) throws Exception;

    void forwardToPanel(Integer claimNo, UserDto user, String panelId, String remark, Integer repudiatedReason) throws Exception;

    void approveByPanel(Integer claimNo, UserDto user, String panelId, String remark, Integer repudiatedReason) throws Exception;

    void rejectByPanel(Integer claimNo, UserDto user, String panelId, String remark) throws Exception;

    void requestForInvestigationByPanel(Integer claimNo, UserDto user, String panelId, String remark) throws Exception;

    List<String> getDecisionMakingUserIdList() throws Exception;

    void referClaimPanel(Integer claimNo, UserDto user, String remark, Integer repudiatedReason) throws Exception;

    void approveInitialLiability(Integer claimNo, UserDto user, String remark, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception;

    void approveLiability(Integer claimNo, UserDto user, String remark, Integer liabilityApproveType, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception;

    void pendingLiability(Integer claimNo, UserDto user, String remark) throws Exception;

    void requestForInvestigationByDecisionMaker(Integer claimNo, UserDto user, String remark) throws Exception;

    void requestForInvestigationByClaimHandler(Integer claimNo, UserDto user, String remark) throws Exception;

    List<ClaimClaimPanelUserDto> getPanelUser(String userId) throws Exception;

    void arrangeInvestigation(Integer claimNo, UserDto user, String panelId, String remark) throws Exception;

    void arrangeInvestigationByDecisionMaker(Integer claimNo, UserDto user, String remark) throws Exception;

    void generateRejectionLetter(Integer claimNo, UserDto user, String rejectReasonId, String remark, Integer rejectionLatterType) throws Exception;

    ClaimHandlerDto searchClaimByClaimNo(Integer claimNo) throws Exception;

    ClaimHandlerDto searchClaimByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void updateClaimAssignUser(Integer claimNo, UserDto user) throws Exception;

    void updateClaimAssignUser(Connection connection, Integer claimNo, UserDto user) throws Exception;

    List<ClaimRepudiatedReasonDto> getRepudiatedList() throws Exception;

    DataGridDto getClaimHandlerPanelDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type);

    List<String> getUserListByAccessUserType() throws Exception;

    List<String> getUserListByAccessUserType(String accessUserTypeList) throws Exception;
    List<String> getUserListByAccessUserType(String accessUserTypeList,UserDto sessionUser) throws Exception;

    boolean updateSpecialComment(Integer claimNo, String userId, UserDto user, String remark) throws Exception;

    boolean requestForSupplierOrder(Integer claimNo, UserDto user, String remark) throws Exception;

    void approveLiabilityAndStoreFile(Integer claimNo, UserDto user, String remark, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception;

    DataGridDto getClaimHandlerScrutinizingDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type);

    void approveInitialLiabilityAndStoreFile(Integer claimNo, UserDto user, String remark, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception;

    boolean isLiablityChecked(Integer claimNo) throws Exception;

    boolean requestedAri(Integer claimNo, Integer reason, String remark, UserDto user) throws Exception;

    void updateClaimClose(Integer claimNo, UserDto user, String remark,String reason) throws Exception;

    void updateClaimClose(Integer claimNo, UserDto user) throws Exception;

    void updateClaimReopen(Integer claimNo, String reOpenType, UserDto user, String remark) throws Exception;

    void forwardToClaimHandler(Integer claimNo, UserDto user, String remark) throws Exception;

    void addNotificationToBranchUsers(Integer claimNo, UserDto user) throws Exception;

    Map<Integer, Integer> getUploadedDocumentsType(Integer claimNo);

    void updateClaimClose(Connection connection, Integer claimNo, UserDto user) throws Exception;

    void updateClaimSettle(Connection connection, Integer claimNo, UserDto user) throws Exception;

    List<ClaimsDto> getTheftClaimsList() throws Exception;

    List<UserDto> getTotalUserList() throws Exception;

    void setUnderWritingDetails(ClaimsDto claimsDto);

    DataGridDto getClaimHandlerSupplierDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type);

    List<Integer> getSelectedList(String ids);

    String getCalsheetStatus(Integer claimNo) throws Exception;

    boolean recallDO(Integer claimNo, UserDto user, String remark) throws Exception;

    void coverNoteApprove(Integer claimNo, UserDto user);

    void saveInvestigationSelectedImages(String selectedeImagesRefNo, Integer claimNo, UserDto user) throws Exception;

    void returnToDecisionMaker(Integer claimNo, String remark, UserDto user) throws Exception;

    List<ClaimsDto> viewPreviousClaim(String vehicleNo, Integer claimNo) throws Exception;

    BigDecimal getEngineerApprovedAmount(Integer claimNo) throws Exception;

    void requestForAdvance(Integer claimNo, String remark, UserDto assignUser, UserDto user) throws Exception;

    void forwardForAdvance(Integer claimNo, Integer type, String remark, UserDto assignUser, UserDto user, BigDecimal advanceAmount) throws Exception;

    void returnToClaimHandlerAdvance(Integer claimNo, String remark, UserDto sessionUser) throws Exception;

    void ApproveAdvance(Integer claimNo, BigDecimal advance, String remark, UserDto sessionUser) throws Exception;

    void recallAdvance(Integer claimNo, String remark, UserDto user) throws Exception;

    DataGridDto getAdvanceForwardedList(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String user);

    void returnAdvance(Integer claimNo, String remark, UserDto sessionUser) throws Exception;

    boolean forwardToEngineer(Integer claimNo, String rteUser, UserDto user, String remark) throws Exception;

    boolean recallFromForwardedEngineer(Integer claimNo, UserDto user, String remark) throws Exception;

    RtePendingClaimsDto checkRtePendingDetails(Integer claimNoToSearch) throws Exception;

    boolean returnClaimByEngineer(Integer claimNo, String assignUser, UserDto user, String remark) throws Exception;

    DataGridDto getClaimsForEngineerDataGrid(List<FieldParameterDto> parameterList, int drawRendomId, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, boolean equals);

    boolean isForwardedToEngineer(Integer claimNo, String v_usrid) throws Exception;

    DataGridDto getMainPanelUsersGrid(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String columnOrder, String orderColumnName) throws Exception;

    ClaimPanelDto searchPanelDecision(Integer claimNo) throws Exception;

    String getClaimRejectedUser(Integer claimNo) throws Exception;

    boolean isApprovelPending(Integer claimNoToSearch, UserDto sessionUser) throws Exception;

    boolean isForwardedToMainPanel(Integer claimNoToSearch) throws Exception;

    String getDecisionMakingAssignUser(Integer claimNo) throws Exception;

    List<PanelMemberListDto> getPanelMembers(Integer claimNo) throws Exception;

    void updatePanelUsers(Integer claimNo, String userList, String removedUserList, UserDto user) throws Exception;

    List<UserDto> getUSersByAccessUsrType(Integer accessUsrType) throws Exception;

    List<ClaimRepudiatedLetterTypeDto> getActiveRejectionReasons(String activeStatus) throws Exception;

    void attachRejectionFileOnOtherSelect(Integer claimNo) throws Exception;

    int getRejectionRefNo(Integer claimNo, Integer rejectoinDocTypeId) throws Exception;

    boolean checkClaimStatusByClaimNO(Integer climNo) throws Exception;

    BigDecimal getReserveAmount(Integer claimNo) throws Exception;

    void updateBulkClose(Integer claimNo, Integer Days, UserDto user) throws Exception;

    BulkCloseDetailDto getBulkCloseDetails(Integer claimNoToSearch) throws Exception;

    String isAllMainPanelApproved(Integer claimNo) throws Exception;

    List<InvestigatorDetailsDto> getInvestigatorList() throws Exception;

    String getCallCenterClaimClose(Integer claimNo) throws Exception;

}
