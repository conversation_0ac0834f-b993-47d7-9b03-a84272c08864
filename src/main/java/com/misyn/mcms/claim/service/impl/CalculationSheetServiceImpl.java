package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.admin.admin.dao.BranchMstDao;
import com.misyn.mcms.admin.admin.dao.impl.BranchMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.admin.admin.service.impl.UserManagementServiceImpl;
import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.*;
import com.misyn.mcms.claim.exception.*;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.ListBoxItem;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CalculationSheetServiceImpl extends AbstractBaseService<CalculationSheetServiceImpl> implements CalculationSheetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CalculationSheetServiceImpl.class);
    private static final String CONST_TYPE_APPR_ESTIMATE = "121";
    private static final String CONST_TYPE_APPR_BILL = "61,120,124,125,126,133";
    private static final String CONST_TYPE_BILL = "60,73,74,107,128,129";
    private static final String CONST_TYPE_APPR_DR = "8";
    private static final String CONST_TYPE_APPR_SUPP_ESTIMATE = "9";
    private static final String CONST_TYPE_APPR_OTHER_ESTIMATE = "3,7,116";
    final String LOLC_NAME = "MI Synergy PVT LTD";
    private final DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private final ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private final ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private final ClaimCalculationSheetDetailDao claimCalculationSheetDetailDao = new ClaimCalculationSheetDetailDaoImpl();
    private final ClaimCalculationSheetPayeeDao claimCalculationSheetPayeeDao = new ClaimCalculationSheetPayeeDaoImpl();
    private final ClaimUserAllocationService claimUserAllocationService = new ClaimUserAllocationServiceImpl();
    private final ClaimCalculationSheetSupplierOrderDao claimCalculationSheetSupplierOrderDao = new ClaimCalculationSheetSupplierOrderDaoImpl();
    private final ClaimDocumentService claimDocumentService = new ClaimDocumentServiceImpl();
    private final EmailService emailService = new EmailServiceImpl();
    private final CallCenterService callCenterService = new CallCenterServiceImpl();
    private final EmailDao emailDao = new EmailDaoImpl();
    private final DocumentContentDao documentContentDao = new DocumentContentDaoImpl();
    private final McmsClaimOfflinePaymentDao claimOfflinePaymentDao = new McmsClaimOfflinePaymentDaoImpl();
    private final McmsClaimOfflineReserveClaimDao offlineReserveClaimDao = new McmsClaimOfflineReserveClaimDaoImpl();
    private final CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private final CalSheetSpecialRemarkDao calSheetSpecialRemarkDao = new CalSheetSpecialRemarkDaoImpl();
    private final SupplierDetailsMasterDao supplierDetailsMasterDao = new SupplierDetailsMasterDaoImpl();
    private final SupplyOrderSummaryDao supplyOrderSummaryDao = new SupplyOrderSummaryDaoImpl();
    private final CalculationProcessFlowDao calculationProcessFlowDao = new CalculationProcessFlowDaoImpl();
    private final ClaimHandlerService claimHandlerService = new ClaimHandlerServiceImpl();
    private final ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();
    private final ClaimCalculationSheetTypeDao claimCalculationSheetTypeDao = new ClaimCalculationSheetTypeDaoImpl();
    private final ClaimCalculationSheetMainTempDao claimCalculationSheetMainTempDao = new ClaimCalculationSheetMainTempDaoImpl();
    private final UserDao userDao = new UserDaoImpl();
    private final PolicyDao policyDao = new PolicyDaoImpl();
    private final MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private final RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private final UserAuthorityLimitDao userAuthorityLimitDao = new UserAuthorityLimitDaoImpl();
    private final CalculationSheetAssignMofaLevelDetailDao calculationSheetAssignMofaLevelDetailDao = new CalculationSheetAssignMofaLevelDetailDaoImpl();
    private final BranchMstDao branchMstDao = new BranchMstDaoImpl();
    private final MofaLevelHierarchyDao mofaLevelHierarchyDao = new MofaLevelHierarchyDaoImpl();
    private final ClaimUserAllocationDao claimUserAllocationDao = new ClaimUserAllocationDaoImpl();
    private final ClaimPaymentDispatchDao claimPaymentDispatchDao = new ClaimPaymentDispatchDaoImpl();
    private final LargeClaimEmailDao largeClaimEmailDao = new LargeClaimEmailDaoImpl();
    private final InvestigationDetailsDao investigationDetailsDao = new InvestigationDetailsDaoImpl();
    private final RestPolicyDetailsService restPolicyDetailsService = new RestPolicyDetailsServiceImpl();
    private final MotorEngineerService motorEngineerService = new MotorEngineerServiceImpl();
    private UserManagementService userManagementService = new UserManagementServiceImpl();
    private ClaimUserUpdateDao claimUserUpdateDao = new ClaimUserUpdateDaoImpl();

    @Override
    public void saveCalculationSheet(String action, UserDto user, ClaimCalculationSheetMainDto claimCalculationSheetMainDto, BigDecimal outStandingPremium, boolean isReserveAmountExceed, boolean isSAveAsDraft, boolean isCancelledPolicy) throws Exception {
        BigDecimal reserveAmountAfterApproved;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            boolean isOffline = false;
            BigDecimal reserveAmount = BigDecimal.ZERO;
            BigDecimal totPreviousPaybleAmount = BigDecimal.ZERO;
            Integer calSheetId;
            boolean isAlreadyLoss = false;
            claimCalculationSheetMainDto.setInputUser(user.getUserId());
            claimCalculationSheetMainDto.setInputDatetime(Utility.sysDateTime());

            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimCalculationSheetMainDto.getClaimNo());

            ClaimCalculationSheetMainTempDto calsheetTempDto = setClaimCalculationSheetMainTempDtoValues(claimHandlerDto);

            reserveAmount = claimHandlerDto.getReserveAmount();
            reserveAmountAfterApproved = claimHandlerDto.getReserveAmountAfterAprv();

            List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = claimCalculationSheetMainDto.getClaimCalculationSheetDetailReplacementDtos();
            List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos = claimCalculationSheetMainDto.getClaimCalculationSheetDetailLabourDtos();
            List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos = claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos();

            if ((AppConstant.CAL_SHEET_TYPE_FULL_FINAL.equals(claimCalculationSheetMainDto.getCalSheetType())
                    || AppConstant.CAL_SHEET_TYPE_RELEASE_ORDER.equals(claimCalculationSheetMainDto.getCalSheetType())
                    || AppConstant.CAL_SHEET_TYPE_BALANCE.equals(claimCalculationSheetMainDto.getCalSheetType())
                    || AppConstant.CAL_SHEET_TYPE_BALANCE_RELEASE_ORDER.equals(claimCalculationSheetMainDto.getCalSheetType()))
                    && null != claimCalculationSheetMainDto.getIsPayableAmount()
                    && !claimCalculationSheetMainDto.getIsPayableAmount().isEmpty()
                    && AppConstant.YES.equals(claimCalculationSheetMainDto.getIsPayableAmount())) {

                Integer mCalSheetId = 0;
                if (null != claimCalculationSheetMainDto.getCalSheetId() && claimCalculationSheetMainDto.getCalSheetId() != 0) {
                    mCalSheetId = claimCalculationSheetMainDto.getCalSheetId();
                }
                totPreviousPaybleAmount = claimCalculationSheetMainDao.getTotalPaybleAmount(connection, claimCalculationSheetMainDto.getClaimNo(), mCalSheetId);
                if (null == totPreviousPaybleAmount) {
                    totPreviousPaybleAmount = BigDecimal.ZERO;
                }

                reserveAmount = claimCalculationSheetMainDto.getPayableAmount().add(totPreviousPaybleAmount);
                claimHandlerDto.setReserveAmountAfterAprv(claimCalculationSheetMainDto.getPayableAmount());
                if (!isEmptyCalcualtionPaymentItems(claimCalculationSheetDetailReplacementDtos, claimCalculationSheetDetailLabourDtos, claimCalculationSheetPayeeDtos)) {
//                    claimHandlerDao.updateTotAcrAmount(connection, claimCalculationSheetMainDto.getClaimNo(), totAcr);
                    isOffline = true;
                }

            } else {
                if (claimCalculationSheetMainDto.getPayableDiff().compareTo(BigDecimal.ZERO) > AppConstant.ZERO_VALUE) {
                    claimHandlerDto.setReserveAmountAfterAprv(claimHandlerDto.getReserveAmountAfterAprv() == null ? BigDecimal.ZERO : claimHandlerDto.getReserveAmountAfterAprv().add(claimCalculationSheetMainDto.getPayableDiff()));
                    reserveAmount = claimHandlerDto.getReserveAmount() == null ? BigDecimal.ZERO : claimHandlerDto.getReserveAmount().add(claimCalculationSheetMainDto.getPayableDiff());
                    if (!isEmptyCalcualtionPaymentItems(claimCalculationSheetDetailReplacementDtos, claimCalculationSheetDetailLabourDtos, claimCalculationSheetPayeeDtos)) {
//                        claimHandlerDao.updateTotAcrAmount(connection, claimCalculationSheetMainDto.getClaimNo(), totAcr);
                        isOffline = true;
                    }
                }
            }
            if (null != reserveAmount && reserveAmount.compareTo(BigDecimal.ZERO) == -1) {
                throw new Exception("Reserve Amount Less than Zero");
            }
            String calSheetDesc = dbRecordCommonFunction.getValue("claim_calculation_sheet_type", "V_CAL_SHEET_TYPE_DESC", "N_CAL_SHEET_TYPE_ID", String.valueOf(claimCalculationSheetMainDto.getCalSheetType()));

            if ("SAVE".equals(action)) {
                //update balance ACR
                reserveAmountAfterApproved = claimHandlerDto.getReserveAmountAfterAprv();
                if (!isEmptyCalcualtionPaymentItems(claimCalculationSheetDetailReplacementDtos, claimCalculationSheetDetailLabourDtos, claimCalculationSheetPayeeDtos)) {
                    if (!isSAveAsDraft && claimCalculationSheetMainDto.getPayableAmount().compareTo(BigDecimal.ZERO) != AppConstant.ZERO_INT) {
                        claimHandlerDao.updateReserveAcrAmount(reserveAmount, reserveAmountAfterApproved, connection, claimCalculationSheetMainDto.getClaimNo());
                    }

                    calsheetTempDto.setReserveAmount(reserveAmount);
                    calsheetTempDto.setReserveAmountAfterApproved(reserveAmountAfterApproved);
                }
                if (isReserveAmountExceed) {
//                    claimCalculationSheetMainDto.setStatus(ClaimStatus.FORWARDED_TO_THE_MOTOR_ENGINEERING_TEAM_FOR_RESERVE_AMOUNT_APPROVAL.getClaimStatus());
                    claimCalculationSheetMainDto.setNoObjectionStatus(AppConstant.STRING_PENDING);
                    claimCalculationSheetMainDto.setPremiumOutstandingStatus(AppConstant.STRING_PENDING);
                    claimCalculationSheetMainDto.setNcbStatus(AppConstant.STRING_PENDING);


                }

                claimCalculationSheetMainDto.setAssignUserId(user.getUserId());
                claimCalculationSheetMainDto.setAssignDateTime(Utility.sysDateTime());
                ClaimCalculationSheetMainDto insertMasterCalculationSheetMainDto = claimCalculationSheetMainDao.insertMaster(connection, claimCalculationSheetMainDto);
                calSheetId = insertMasterCalculationSheetMainDto.getCalSheetId();

                calsheetTempDto.setCalsheetType(claimCalculationSheetMainDto.getCalSheetType());
                calsheetTempDto.setCalsheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
                calsheetTempDto.setPayableAmount(claimCalculationSheetMainDto.getPayableAmount());
                calsheetTempDto.setPrevPayableAmount(claimCalculationSheetMainDto.getPayableAmount());

                boolean isAlreadySaveTempDetails = claimCalculationSheetMainTempDao.isAlreadySaved(connection, calsheetTempDto.getCalsheetId());
                if (!isAlreadySaveTempDetails) {
                    claimCalculationSheetMainTempDao.insertMaster(connection, calsheetTempDto);
                }

                if (AppConstant.CAL_SHEET_TYPE_DO == claimCalculationSheetMainDto.getCalSheetType()) { // 3 --> D/O
                    claimCalculationSheetMainDto.getClaimCalculationSheetSupplierOrderDto().setCalSheetId(calSheetId);
                    claimCalculationSheetSupplierOrderDao.insertMaster(connection, claimCalculationSheetMainDto.getClaimCalculationSheetSupplierOrderDto());
                }

                isAlreadyLoss = false;
                if (isSAveAsDraft) {
                    isOffline = false;
                    saveClaimsLogs(connection, claimCalculationSheetMainDto.getClaimNo(), user, String.format("%s Calculation Sheet Saved as Draft", calSheetDesc), "Calculation Sheet Saved as Draft");
                    this.saveCalculationProcessFlow(connection, claimCalculationSheetMainDto.getClaimNo(), calSheetId, ClaimStatus.SAVE_AS_DRAFT.getClaimStatus(), String.format("%s Create Calculation Sheet / Saved as Draft", calSheetDesc), user.getUserId(), AppConstant.STRING_EMPTY);
                } else {
                    saveClaimsLogs(connection, claimCalculationSheetMainDto.getClaimNo(), user, String.format("%s Calculation Sheet Saved", calSheetDesc), "Calculation Sheet Saved");
                    this.saveCalculationProcessFlow(connection, claimCalculationSheetMainDto.getClaimNo(), calSheetId, 58, String.format("%s Calculation Sheet Created", calSheetDesc), user.getUserId(), AppConstant.STRING_EMPTY);
                    ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, insertMasterCalculationSheetMainDto.getCalSheetType());
                    if (isReserveAmountExceed && null != insertMasterCalculationSheetMainDto) {
                        String URL = AppConstant.MOTORENG_CAL_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(insertMasterCalculationSheetMainDto.getClaimNo()))
                                .concat("&P_CAL_SHEET_NO=")
                                .concat(String.valueOf(calSheetId))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(15));

                        saveNotification(connection, insertMasterCalculationSheetMainDto.getClaimNo(), user.getUserId(), insertMasterCalculationSheetMainDto.getRteAssignUserId(), "You have received a cal sheet from " + user.getUserId() + " for recommendation".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId))), URL);

                        saveConfirmationLogs(connection, insertMasterCalculationSheetMainDto.getClaimNo(), user, outStandingPremium, isCancelledPolicy);
                        saveClaimsLogs(connection, insertMasterCalculationSheetMainDto.getClaimNo(), user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet [" + calSheetId + "] has been forwarded to RTE", "Calculation sheet has been forwarded to RTE [" + insertMasterCalculationSheetMainDto.getRteAssignUserId() + "] " + "for recommendation [" + user.getUserId() + "]");
                        this.saveCalculationProcessFlow(connection, insertMasterCalculationSheetMainDto.getClaimNo(), calSheetId, ClaimStatus.FORWARDED_TO_THE_MOTOR_ENGINEERING_TEAM_FOR_RESERVE_AMOUNT_APPROVAL.getClaimStatus(), claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet [" + calSheetId + "] has been forwarded to RTE [" + insertMasterCalculationSheetMainDto.getRteAssignUserId() + "] " + "for recommendation [" + user.getUserId() + "]", user.getUserId(), insertMasterCalculationSheetMainDto.getRteAssignUserId());

                    }
                }
            } else if ("UPDATE".equals(action)) {

                calSheetId = claimCalculationSheetMainDto.getCalSheetId();
                isAlreadyLoss = claimCalculationSheetMainDao.getIsLoss(connection, calSheetId);
                reserveAmountAfterApproved = claimHandlerDto.getReserveAmountAfterAprv();

                if (!isEmptyCalcualtionPaymentItems(claimCalculationSheetDetailReplacementDtos, claimCalculationSheetDetailLabourDtos, claimCalculationSheetPayeeDtos)) {

                    ReserveAmountDto amountDto = updateReserveAmountIfAlreadySavedCalsheet(connection, claimCalculationSheetMainDto, reserveAmount, reserveAmountAfterApproved, isOffline);

                    reserveAmount = amountDto.getReserveAmount();
                    isOffline = amountDto.isOfline();
                }
                if (isReserveAmountExceed) {
                    claimCalculationSheetMainDto.setNoObjectionStatus(AppConstant.STRING_PENDING);
                    claimCalculationSheetMainDto.setPremiumOutstandingStatus(AppConstant.STRING_PENDING);
                    claimCalculationSheetMainDto.setNcbStatus(AppConstant.STRING_PENDING);
                }

                claimCalculationSheetMainDao.updateMaster(connection, claimCalculationSheetMainDto);
                claimCalculationSheetDetailDao.deleteByCalSheetNo(connection, calSheetId);
                claimCalculationSheetPayeeDao.deleteByCalSheetNo(connection, calSheetId);

                if (AppConstant.CAL_SHEET_TYPE_DO == claimCalculationSheetMainDto.getCalSheetType()) { // 3 --> D/O
                    ClaimCalculationSheetSupplierOrderDto calculationSheetSupplierOrderDtoSearched = claimCalculationSheetSupplierOrderDao.searchMasterByCalSheetId(connection, calSheetId);
                    claimCalculationSheetMainDto.getClaimCalculationSheetSupplierOrderDto().setCalSheetId(calSheetId);
                    if (null != calculationSheetSupplierOrderDtoSearched) {
                        claimCalculationSheetMainDto.getClaimCalculationSheetSupplierOrderDto().setCalSheetSoId(calculationSheetSupplierOrderDtoSearched.getCalSheetSoId());
                        claimCalculationSheetSupplierOrderDao.updateMaster(connection, claimCalculationSheetMainDto.getClaimCalculationSheetSupplierOrderDto());
                    } else {
                        claimCalculationSheetSupplierOrderDao.insertMaster(connection, claimCalculationSheetMainDto.getClaimCalculationSheetSupplierOrderDto());
                    }
                } else {
                    ClaimCalculationSheetSupplierOrderDto calculationSheetSupplierOrderDtoSearched = claimCalculationSheetSupplierOrderDao.searchMasterByCalSheetId(connection, calSheetId);
                    if (null != calculationSheetSupplierOrderDtoSearched) {
                        claimCalculationSheetSupplierOrderDao.deleteMaster(connection, calculationSheetSupplierOrderDtoSearched.getCalSheetSoId());
                    }
                }

                if (isSAveAsDraft) {
                    isOffline = false;
                    saveClaimsLogs(connection, claimCalculationSheetMainDto.getClaimNo(), user, String.format("%s Calculation Sheet Saved as Draft", calSheetDesc), "Calculation Sheet Saved as Draft");
                    this.saveCalculationProcessFlow(connection, claimCalculationSheetMainDto.getClaimNo(), calSheetId, ClaimStatus.SAVE_AS_DRAFT.getClaimStatus(), String.format("%s Update Calculation Sheet / Saved as Draft", calSheetDesc), user.getUserId(), AppConstant.STRING_EMPTY);
                } else {
                    this.saveCalculationProcessFlow(connection, claimCalculationSheetMainDto.getClaimNo(), calSheetId, 58, String.format("%s Calculation Sheet Updated", calSheetDesc), user.getUserId(), AppConstant.STRING_EMPTY);
                    saveClaimsLogs(connection, claimCalculationSheetMainDto.getClaimNo(), user, String.format("%s Calculation Sheet Updated", calSheetDesc), "Calculation Sheet Updated");
                    ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, claimCalculationSheetMainDto.getCalSheetType());
                    if (isReserveAmountExceed) {
                        String URL = AppConstant.MOTORENG_CAL_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimCalculationSheetMainDto.getClaimNo()))
                                .concat("&P_CAL_SHEET_NO=")
                                .concat(String.valueOf(calSheetId))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(15));

                        saveNotification(connection, claimCalculationSheetMainDto.getClaimNo(), user.getUserId(), claimCalculationSheetMainDto.getRteAssignUserId(), "You have received a cal sheet from " + user.getUserId() + " for recommendation".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId))), URL);
                        if (outStandingPremium.compareTo(BigDecimal.ZERO) > 0) {
                            saveClaimsLogs(connection, claimCalculationSheetMainDto.getClaimNo(), user, "Outstanding Premium Confirmation Alert", "Marked As Ok to \"This policy has Rs." + outStandingPremium + " of outstanding premium amount\" alert");
                        }
                        saveClaimsLogs(connection, claimCalculationSheetMainDto.getClaimNo(), user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet [" + calSheetId + "] has been forwarded to RTE", "Calculation sheet has been forwarded to RTE [" + claimCalculationSheetMainDto.getRteAssignUserId() + "] " + "for recommendation [" + user.getUserId() + "]");
                        this.saveCalculationProcessFlow(connection, claimCalculationSheetMainDto.getClaimNo(), calSheetId, ClaimStatus.FORWARDED_TO_THE_MOTOR_ENGINEERING_TEAM_FOR_RESERVE_AMOUNT_APPROVAL.getClaimStatus(), claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet [" + calSheetId + "] has been forwarded to RTE [" + claimCalculationSheetMainDto.getRteAssignUserId() + "] " + "for recommendation [" + user.getUserId() + "]", user.getUserId(), claimCalculationSheetMainDto.getRteAssignUserId());
                    }
                }

            } else {
                throw new RuntimeException("No Action Type Found");
            }

            for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailReplacementDto : claimCalculationSheetDetailReplacementDtos) {
                claimCalculationSheetDetailReplacementDto.setCalSheetId(calSheetId);
                claimCalculationSheetDetailReplacementDto.setRecordType("1"); //Replacement
                claimCalculationSheetDetailDao.insertMaster(connection, claimCalculationSheetDetailReplacementDto);
            }


            for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailLabourDto : claimCalculationSheetDetailLabourDtos) {
                claimCalculationSheetDetailLabourDto.setCalSheetId(calSheetId);
                claimCalculationSheetDetailLabourDto.setRecordType("2"); //LABOUR
                claimCalculationSheetDetailDao.insertMaster(connection, claimCalculationSheetDetailLabourDto);
            }


            for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : claimCalculationSheetPayeeDtos) {
                claimCalculationSheetPayeeDto.setCalSheetId(calSheetId);
                claimCalculationSheetPayeeDto.setEmailStatus(AppConstant.NO);
                claimCalculationSheetPayeeDto.setResponseDateTime(AppConstant.DEFAULT_DATE_TIME);
                claimCalculationSheetPayeeDto.setChequeStatus(AppConstant.STRING_PENDING);
                ClaimCalculationSheetPayeeDto generatedPayee = claimCalculationSheetPayeeDao.insertMaster(connection, claimCalculationSheetPayeeDto);

                claimPaymentDispatchDao.deleteByCalSheetId(connection, calSheetId);
                ClaimPaymentDispatchDto paymentDispatchDto = new ClaimPaymentDispatchDto();
                paymentDispatchDto.setPayeeId(generatedPayee.getCalSheetPayeeId());
                paymentDispatchDto.setCalSheetId(calSheetId);
                paymentDispatchDto.setClaimNo(claimCalculationSheetMainDto.getClaimNo());
                paymentDispatchDto.setDispatchLocation(claimCalculationSheetPayeeDto.getBranchDetailDto().getBranchCode());
                claimPaymentDispatchDao.savePaymentDispatch(connection, paymentDispatchDto);
            }

            if (isOffline) {
                offlineReserveClaim(connection, claimCalculationSheetMainDto.getClaimNo(), reserveAmount, 1, user);
            }

            if (null != claimCalculationSheetMainDto.getRemark() && !claimCalculationSheetMainDto.getRemark().isEmpty()) {
                saveSpecialRemark(connection, calSheetId, claimCalculationSheetMainDto.getRemark(), user);
            }
            if ((claimCalculationSheetMainDto.getLossType() == AppConstant.TOTAL_LOSS_TYPE || claimCalculationSheetMainDto.getLossType() == AppConstant.THEFT_TYPE) && claimHandlerDto.getLossType() == AppConstant.TOTAL_LOSS_TYPE && !isAlreadyLoss) {
                claimWiseDocumentDao.updateTotalLossDocumentList(connection, claimCalculationSheetMainDto.getClaimNo(), "Y");
            }
            if (null != claimCalculationSheetMainDto.getIsExcessInclude() && AppConstant.YES.equals(claimCalculationSheetMainDto.getIsExcessInclude())) {
                claimHandlerDao.updateExcessIncludeStatus(connection, AppConstant.YES, claimCalculationSheetMainDto.getClaimNo());
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }

    }

    private ReserveAmountDto updateReserveAmountIfAlreadySavedCalsheet(Connection connection, ClaimCalculationSheetMainDto claimCalculationSheetMainDto, BigDecimal reserveAmount, BigDecimal reserveAmountAfterApproved, boolean isOffline) throws Exception {
        ReserveAmountDto amountDto = new ReserveAmountDto();
        try {
            ClaimCalculationSheetMainTempDto searchCalsheetTempDto = claimCalculationSheetMainTempDao.searchByCalsheetId(connection, claimCalculationSheetMainDto.getCalSheetId());

            if (null != searchCalsheetTempDto) {
                BigDecimal claimReserveAmount = claimHandlerDao.getReserveAmount(connection, searchCalsheetTempDto.getClaimNo());
                if (!AppConstant.CAL_SHEET_TYPE_FULL_FINAL.equals(claimCalculationSheetMainDto.getCalSheetType())
                        && !AppConstant.CAL_SHEET_TYPE_RELEASE_ORDER.equals(claimCalculationSheetMainDto.getCalSheetType())
                        && !AppConstant.CAL_SHEET_TYPE_BALANCE.equals(claimCalculationSheetMainDto.getCalSheetType())
                        && !AppConstant.CAL_SHEET_TYPE_BALANCE_RELEASE_ORDER.equals(claimCalculationSheetMainDto.getCalSheetType())) {

                    BigDecimal actualDiff = claimCalculationSheetMainDto.getPayableAmount().subtract(searchCalsheetTempDto.getPrevReserveAmountAfterApproved());

                    if (actualDiff.compareTo(BigDecimal.ZERO) > AppConstant.ZERO_VALUE) {
                        reserveAmount = searchCalsheetTempDto.getPrevReserveAmount().add(actualDiff);
                        reserveAmountAfterApproved = searchCalsheetTempDto.getPrevReserveAmountAfterApproved().add(actualDiff);
                    } else {
                        reserveAmount = searchCalsheetTempDto.getPrevReserveAmount();
                        reserveAmountAfterApproved = searchCalsheetTempDto.getPrevReserveAmountAfterApproved();
                    }

                } else {

                    BigDecimal totPreviousPaybleAmount = claimCalculationSheetMainDao.getTotalPaybleAmount(connection, claimCalculationSheetMainDto.getClaimNo(), claimCalculationSheetMainDto.getCalSheetId());
                    if (null == totPreviousPaybleAmount) {
                        totPreviousPaybleAmount = BigDecimal.ZERO;
                    }

                    reserveAmount = claimCalculationSheetMainDto.getPayableAmount().add(totPreviousPaybleAmount);
                    reserveAmountAfterApproved = claimCalculationSheetMainDto.getPayableAmount();
                }

                if (claimReserveAmount.compareTo(reserveAmount) > 0 || reserveAmount.compareTo(claimReserveAmount) > 0) {
                    isOffline = true;
                }
                searchCalsheetTempDto.setReserveAmount(reserveAmount);
                searchCalsheetTempDto.setReserveAmountAfterApproved(reserveAmountAfterApproved);
                searchCalsheetTempDto.setCalsheetType(claimCalculationSheetMainDto.getCalSheetType());
                searchCalsheetTempDto.setPayableAmount(claimCalculationSheetMainDto.getPayableAmount());

                claimCalculationSheetMainTempDao.updateMaster(connection, searchCalsheetTempDto);
            }

            amountDto.setReserveAmount(reserveAmount);
            amountDto.setOfline(isOffline);

            claimHandlerDao.updateReserveAcrAmount(reserveAmount, reserveAmountAfterApproved, connection, claimCalculationSheetMainDto.getClaimNo());

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return amountDto;
    }

    private ClaimCalculationSheetMainTempDto setClaimCalculationSheetMainTempDtoValues(ClaimHandlerDto
                                                                                               claimHandlerDto) {
        ClaimCalculationSheetMainTempDto tempDto = new ClaimCalculationSheetMainTempDto();

        tempDto.setClaimNo(claimHandlerDto.getClaimNo());
        tempDto.setTotalApprovedAcr(claimHandlerDto.getAprvTotAcrAmount());
        tempDto.setReserveAmount(claimHandlerDto.getReserveAmount());
        tempDto.setPrevReserveAmount(claimHandlerDto.getReserveAmount());
        tempDto.setReserveAmountAfterApproved(claimHandlerDto.getReserveAmountAfterAprv());
        tempDto.setPrevReserveAmountAfterApproved(claimHandlerDto.getReserveAmountAfterAprv());
        tempDto.setAdvanceAmount(claimHandlerDto.getAprvAdvanceAmount());
        tempDto.setPrevAdvanceAmount(claimHandlerDto.getAprvAdvanceAmount());

        return tempDto;
    }

    private boolean isEmptyCalcualtionPaymentItems
            (List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos
                    , List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos
                    , List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos) {
        boolean result = false;
        try {
            if ((claimCalculationSheetDetailReplacementDtos.isEmpty()
                    && claimCalculationSheetDetailLabourDtos.isEmpty())
                    || claimCalculationSheetPayeeDtos.isEmpty()) {
                result = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    @Override
    public List<ClaimCalculationSheetMainDto> getCalculationSheetList(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : claimCalculationSheetMainDtos) {
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailDtos = claimCalculationSheetDetailDao.searchByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = new ArrayList<>();
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos = new ArrayList<>();

                for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto : claimCalculationSheetDetailDtos) {
                    switch (claimCalculationSheetDetailDto.getRecordType()) {
                        case "1":   //Replacement
                            claimCalculationSheetDetailReplacementDtos.add(claimCalculationSheetDetailDto);
                            break;
                        case "2": //LABOUR
                            claimCalculationSheetDetailLabourDtos.add(claimCalculationSheetDetailDto);
                            break;
                    }
                }

                List<ClaimCalculationSheetPayeeDto> list = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
                if (null != list) {
                    claimCalculationSheetMainDto.setClaimCalculationSheetPayeeDtos(list);
                }
                claimCalculationSheetMainDto.setClaimCalculationSheetDetailReplacementDtos(claimCalculationSheetDetailReplacementDtos);
                claimCalculationSheetMainDto.setClaimCalculationSheetDetailLabourDtos(claimCalculationSheetDetailLabourDtos);

                List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
                claimCalculationSheetMainDto.setClaimCalculationSheetPayeeDtos(claimCalculationSheetPayeeDtos);

                if (claimCalculationSheetMainDto.getCalSheetType() == AppConstant.CAL_SHEET_TYPE_DO) { // 3 --> D/O
                    ClaimCalculationSheetSupplierOrderDto calculationSheetSupplierOrderDto = claimCalculationSheetSupplierOrderDao.searchMasterByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
                    claimCalculationSheetMainDto.setClaimCalculationSheetSupplierOrderDto(calculationSheetSupplierOrderDto);
                }
            }
            return claimCalculationSheetMainDtos;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimCalculationSheetMainDto> getPaymentOptionCalculationSheetList(Integer claimNo) throws
            Exception {
        Connection connection = null;
        List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = null;
        try {
            connection = getJDBCConnection();
            claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimCalculationSheetMainDtos;
    }

    @Override
    public ClaimCalculationSheetMainDto getCalculationSheet(Integer claimNo, Integer calSheetNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calSheetNo);
            List<BranchDetailDto> branchDetailDtos = branchMstDao.searchAll(connection);
            if (null != claimCalculationSheetMainDto && calSheetNo != 0) {
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailDtos = claimCalculationSheetDetailDao.searchByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = new ArrayList<>();
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos = new ArrayList<>();
                for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto : claimCalculationSheetDetailDtos) {
                    switch (claimCalculationSheetDetailDto.getRecordType()) {
                        case "1":   //Replacement
                            claimCalculationSheetDetailReplacementDtos.add(claimCalculationSheetDetailDto);
                            break;
                        case "2": //LABOUR
                            claimCalculationSheetDetailLabourDtos.add(claimCalculationSheetDetailDto);
                            break;
                    }
                }
                claimCalculationSheetMainDto.setClaimCalculationSheetDetailReplacementDtos(claimCalculationSheetDetailReplacementDtos);
                claimCalculationSheetMainDto.setClaimCalculationSheetDetailLabourDtos(claimCalculationSheetDetailLabourDtos);

                List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
                claimCalculationSheetMainDto.setClaimCalculationSheetPayeeDtos(claimCalculationSheetPayeeDtos);

                claimCalculationSheetMainDto.setBranchDetailDtos(branchDetailDtos);

                if (AppConstant.CAL_SHEET_TYPE_DO == claimCalculationSheetMainDto.getCalSheetType()) { // 3 --> D/O
                    ClaimCalculationSheetSupplierOrderDto calculationSheetSupplierOrderDto = claimCalculationSheetSupplierOrderDao.searchMasterByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
                    claimCalculationSheetMainDto.setClaimCalculationSheetSupplierOrderDto(calculationSheetSupplierOrderDto);
                }

            } else {
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = new ArrayList<>();
                List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos = new ArrayList<>();
                for (int i = 1; i <= 10; i++) {
                    claimCalculationSheetDetailReplacementDtos.add(new ClaimCalculationSheetDetailDto(i, AppConstant.ADD_VAT_WITHOUT_NBT));
                    claimCalculationSheetDetailLabourDtos.add(new ClaimCalculationSheetDetailDto(i, AppConstant.ADD_VAT_WITHOUT_NBT));
                }
                claimCalculationSheetMainDto.setClaimCalculationSheetDetailReplacementDtos(claimCalculationSheetDetailReplacementDtos);
                claimCalculationSheetMainDto.setClaimCalculationSheetDetailLabourDtos(claimCalculationSheetDetailLabourDtos);
                List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos = new ArrayList<>();
                claimCalculationSheetPayeeDtos.add(new ClaimCalculationSheetPayeeDto());
                claimCalculationSheetMainDto.setClaimCalculationSheetPayeeDtos(claimCalculationSheetPayeeDtos);
                claimCalculationSheetMainDto.setBranchDetailDtos(branchDetailDtos);
            }

            if (null != claimCalculationSheetMainDto) {
                //Set document list
                ClaimDocumentStatusDto claimDocumentStatusDtoOtherBill = getClaimDocumentStatusDto(connection, claimNo, CONST_TYPE_BILL);
                ClaimDocumentStatusDto claimDocumentStatusDtoApprBill = getClaimDocumentStatusDto(connection, claimNo, CONST_TYPE_APPR_BILL);
                ClaimDocumentStatusDto claimDocumentStatusDtoDr = getClaimDocumentStatusDto(connection, claimNo, CONST_TYPE_APPR_DR);
                ClaimDocumentStatusDto claimDocumentStatusDtoEstimate = getClaimDocumentStatusDto(connection, claimNo, CONST_TYPE_APPR_ESTIMATE);
                ClaimDocumentStatusDto claimDocumentStatusDtoSuppEstimate = getClaimDocumentStatusDto(connection, claimNo, CONST_TYPE_APPR_SUPP_ESTIMATE);
                ClaimDocumentStatusDto claimDocumentStatusDtoOtherEstimate = getClaimDocumentStatusDto(connection, claimNo, CONST_TYPE_APPR_OTHER_ESTIMATE);
                ClaimDocumentStatusDto claimDocumentStatusDtoNoObjection = this.getClaimDocumentStatus(connection, claimNo, 30);
                ClaimDocumentStatusDto claimDocumentStatusDtoPremiumOutstanding = this.getClaimDocumentStatus(connection, claimNo, 65);

                claimCalculationSheetMainDto.setOtherBillClaimDocumentStatusDto(claimDocumentStatusDtoOtherBill);
                claimCalculationSheetMainDto.setBillClaimDocumentStatusDto(claimDocumentStatusDtoApprBill);
                claimCalculationSheetMainDto.setEstimateClaimDocumentStatusDto(claimDocumentStatusDtoEstimate);
                claimCalculationSheetMainDto.setDrClaimDocumentStatusDto(claimDocumentStatusDtoDr);
                claimCalculationSheetMainDto.setSupplyEstimateClaimDocumentStatusDto(claimDocumentStatusDtoSuppEstimate);
                claimCalculationSheetMainDto.setOtherEstimateClaimDocumentStatusDto(claimDocumentStatusDtoOtherEstimate);
                claimCalculationSheetMainDto.setClaimDocumentStatusNoObjectionDto(claimDocumentStatusDtoNoObjection);
                claimCalculationSheetMainDto.setClaimDocumentStatusPremiumOutstandingDto(claimDocumentStatusDtoPremiumOutstanding);

                //Set Calculation Type List
                List<CalSheetTypeDto> calSheetTypes = this.getCalSheetTypes(connection, claimNo, calSheetNo);
                claimCalculationSheetMainDto.setCalSheetTypeList(calSheetTypes);
                //Set Calculation Supply Order  List
                List<ClaimCalculationSheetSupplierOrderDto> claimCalculationSheetSupplierOrderDtos = this.getSupplierDoList(connection, claimNo);
                claimCalculationSheetMainDto.setSupplierOrderCalculationList(claimCalculationSheetSupplierOrderDtos);

                //Set Advance Payment List
                List<ClaimCalculationSheetMainDto> advanceListForClaimForFilter = this.getAdvanceListForClaim(connection, claimNo);
                List<ClaimCalculationSheetMainDto> advanceListForClaim = new ArrayList<>();

                for (ClaimCalculationSheetMainDto dto : advanceListForClaimForFilter) {
                    if (!calSheetNo.equals(dto.getCalSheetId())) {
                        advanceListForClaim.add(dto);
                    }
                }
                //Set Select Box Items
                this.setSelectBoxItem(connection, claimCalculationSheetMainDto, claimNo);
            }

            List<CalculationSheetHistoryDto> list = claimCalculationSheetMainDao.getCalculationSheetListByClaimNo(connection, claimNo);

            if (null != list && !list.isEmpty()) {
                claimCalculationSheetMainDto.setTotalPaidAmount(claimCalculationSheetMainDao.getTotalPayableAmount(connection, claimNo));
                claimCalculationSheetMainDto.setCalculationSheetHistoryDtoList(list);
            }

            return claimCalculationSheetMainDto;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ClaimCalculationSheetMainDto getClaimCalculationSheetMainDto(Integer claimNo, Integer calSheetNo, UserDto user) throws
            Exception {
        Connection connection = null;
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
        try {
            connection = getJDBCConnection();
            claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calSheetNo);

            setUserAithorityLimitDetails(user, connection, claimNo, claimCalculationSheetMainDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimCalculationSheetMainDto;
    }

    private void setUserAithorityLimitDetails(UserDto user, Connection connection, Integer claimNo, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        UserAuthorityLimitDto userAuthorityLimitDto;
        BigDecimal totalApprovedAcr;
        BigDecimal totalPaidAmount;
        try {
            boolean isAvailableFullAndFinal = !claimCalculationSheetMainDao.isAvailableCalSheetByType(connection, claimNo, AppConstant.CAL_SHEET_TYPE_FULL_FINAL).equals(AppConstant.ZERO_INT) && !claimCalculationSheetMainDao.isAvailableCalSheetByType(connection, claimNo, AppConstant.CAL_SHEET_TYPE_FULL_FINAL).equals(claimCalculationSheetMainDto.getCalSheetId());
            totalApprovedAcr = claimHandlerDao.getTotalAcrAmount(connection, claimNo);
            totalPaidAmount = claimCalculationSheetMainDao.getTotalPayableAmount(connection, claimNo);

            if (claimCalculationSheetMainDto.getCalSheetType().equals(AppConstant.CAL_SHEET_TYPE_ADVANCED) || (claimCalculationSheetMainDto.getCalSheetType().equals(AppConstant.CAL_SHEET_TYPE_DO) && !isAvailableFullAndFinal)) {
                userAuthorityLimitDto = userAuthorityLimitDao.searchByLimitAndDepartmentId(connection, totalApprovedAcr, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            } else {
                userAuthorityLimitDto = userAuthorityLimitDao.searchByLimitAndDepartmentId(connection, totalPaidAmount, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            }
            if (null != userAuthorityLimitDto) {
                if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_MOFA_TEAM || user.getAccessUserType() == AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM) {
                    UserAuthorityLimitDto assignUserLimit = userAuthorityLimitDao.searchByLimitAndDepartmentId(connection, BigDecimal.valueOf(user.getPaymentAuthLimit()), AppConstant.MOFA_TEAM_DEPARTMENT_ID);
                    if (null != assignUserLimit) {
                        userAuthorityLimitDto.setLogMofaUserLevelCode(assignUserLimit.getLevelCode());
                    }
                }
                claimCalculationSheetMainDto.setUserAuthorityLimitDto(userAuthorityLimitDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public BigDecimal getTotalPaidAdvanceForClaim(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
            BigDecimal total = BigDecimal.ZERO;
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : claimCalculationSheetMainDtos) {
                if (AppConstant.CAL_SHEET_TYPE_ADVANCED == claimCalculationSheetMainDto.getCalSheetType() && (AppConstant.CAL_SHEET_VOUCHER_GENERATE == claimCalculationSheetMainDto.getStatus())) { //Advance
                    total = total.add(claimCalculationSheetMainDto.getPayableAmount());
                }
            }
            return total;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private List<ClaimCalculationSheetMainDto> getAdvanceListForClaim(Connection connection, Integer claimNo) throws
            Exception {
        try {
            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
            List<ClaimCalculationSheetMainDto> advanceCalculationSheetMainDtos = new ArrayList<>();
            for (ClaimCalculationSheetMainDto calculationSheetMainDto : claimCalculationSheetMainDtos) {
                if (AppConstant.CAL_SHEET_TYPE_ADVANCED == calculationSheetMainDto.getCalSheetType()
                        && AppConstant.CAL_SHEET_PAYMENT_REJECTED != calculationSheetMainDto.getStatus()
                        && ClaimStatus.PAYMENT_CANCELLED.getClaimStatus() != calculationSheetMainDto.getStatus()) {
                    advanceCalculationSheetMainDtos.add(calculationSheetMainDto);
                }
            }
            return advanceCalculationSheetMainDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public List<ClaimCalculationSheetMainDto> getAdvanceListForClaim(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<ClaimCalculationSheetMainDto> advanceCalculationSheetMainDtos = this.getAdvanceListForClaim(connection, claimNo);

            return advanceCalculationSheetMainDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public BigDecimal getTotalPaidForClaim(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
            BigDecimal total = BigDecimal.ZERO;
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : claimCalculationSheetMainDtos) {
                if (AppConstant.CAL_SHEET_VOUCHER_GENERATE == claimCalculationSheetMainDto.getStatus()) {
                    total = total.add(claimCalculationSheetMainDto.getPayableAmount());
                }
            }
            return total;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToSparePartsCoordinator(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                .concat("&P_CAL_SHEET_NO=")
                .concat(String.valueOf(calSheetId))
                .concat("&TYPE=7")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            String userId = null == searchMaster.getSparePartCordinatorAssignUserId() ? AppConstant.STRING_EMPTY : searchMaster.getSparePartCordinatorAssignUserId();
            if (userId.isEmpty()) {
                userId = claimUserAllocationService.getNextAssignUser(connection, 27, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
            }

            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Forwarded By Claim Handler.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 59);
            claimCalculationSheetMainDao.updateSparePartCordAssignUser(connection, calSheetId, userId, Utility.sysDateTime());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Forwarded ".concat(String.valueOf(calSheetId)), notificationMsg.concat(" [" + userId + "]"));
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 59, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been forwarded to the \"Spare Parts Coordinator\"", user.getUserId(), userId);

            if (motorEngineerDetailsDao.isInspectionPending(connection, claimNo, AppConstant.ZERO_INT)) {
                if (motorEngineerDetailsDao.isAriSalvagePending(connection, claimNo)) {
                    claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
                    saveClaimsLogs(connection, claimNo, user, "Auto Store File", "Claim File Auto Stored due to Pending ARI/ Salvage Inspection");
                    saveClaimProcessFlow(connection, claimNo, 0, "Auto Store File", userId, Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                }
                UserDto user1 = new UserDto();
                user1.setUserId(userId);
                user1.setAccessUserType(AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR);
                sendNotificationForJobPendingRte(claimNo, user1, connection, true);
                rtePendingClaimDetailDao.savePendingJobs(connection, claimNo, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR, userId);
                saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL, "#FFFFBF");
            } else {
                saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL);
            }
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToScrutinizingTeam(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                .concat("&P_CAL_SHEET_NO=")
                .concat(String.valueOf(calSheetId))
                .concat("&TYPE=6")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(10));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            //get ScrutinizingTeam User to Assign | 28 --> ScrutinizingTeam
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            String userId = (null == searchMaster.getScrutinizeTeamAssignUserId()) ? AppConstant.STRING_EMPTY : searchMaster.getScrutinizeTeamAssignUserId();
            if (userId.isEmpty()) {
                userId = claimUserAllocationService.getNextAssignUser(connection, 28, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
            }

            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Forwarded By Spare Parts Coordinator.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 61);
            claimCalculationSheetMainDao.updateScrutinizingTeamAssignUser(connection, calSheetId, userId, Utility.sysDateTime());
            boolean isPending = motorEngineerService.checkPendingInspection(claimNo);
            saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL, NotificationPriority.MEDIUM, isPending ? "#FFFFBF" : "#FFFFFF");
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet No : ".concat(String.valueOf(calSheetId)), notificationMsg.concat(" [" + userId + "]"));
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 61, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been forwarded to the \"Bill Check Team\"", user.getUserId(), userId);
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToScrutinizingTeamByClaimHandler(Integer calSheetId, Integer claimNo, UserDto user) throws
            Exception {
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                .concat("&P_CAL_SHEET_NO=")
                .concat(String.valueOf(calSheetId))
                .concat("&TYPE=6")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(10));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            //get ScrutinizingTeam User to Assign | 28 --> ScrutinizingTeam
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            String userId = (null == searchMaster.getScrutinizeTeamAssignUserId()) ? AppConstant.STRING_EMPTY : searchMaster.getScrutinizeTeamAssignUserId();
            if (userId.isEmpty()) {
                userId = claimUserAllocationService.getNextAssignUser(connection, 28, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
            }
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet (Bill check) Forwarded By Claim Handler.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 61);
            claimCalculationSheetMainDao.updateScrutinizingTeamAssignUser(connection, calSheetId, userId, Utility.sysDateTime());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] (Bill check) Forwarded ", notificationMsg.concat(" [" + userId + "]"));
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 61, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been forwarded to the \"Bill Check Team\"", user.getUserId(), userId);
            if (motorEngineerDetailsDao.isInspectionPending(connection, claimNo, AppConstant.ZERO_INT)) {
                if (motorEngineerDetailsDao.isAriSalvagePending(connection, claimNo)) {
                    claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
                    saveClaimsLogs(connection, claimNo, user, "Auto Store File", "Claim File Auto Stored due to Pending ARI/ Salvage Inspection");
                    saveClaimProcessFlow(connection, claimNo, 0, "Auto Store File", userId, Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                }
                UserDto user1 = new UserDto();
                user1.setUserId(userId);
                user1.setAccessUserType(AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM);
                sendNotificationForJobPendingRte(claimNo, user1, connection, true);
                rtePendingClaimDetailDao.savePendingJobs(connection, claimNo, AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM, userId);
                saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL, NotificationPriority.HIGH, "#FFFFBF");
            } else {
                saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL, NotificationPriority.HIGH);
            }
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToSpecialTeam(Integer calSheetId, Integer claimNo, UserDto user, BigDecimal outStandingPremium, boolean isCancelledPolicy, String userId) throws
            Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);


            //get SpecialTeam User to Assign | 43 --> SpecialTeam
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);

            validatePayeeDetails(connection, searchMaster);

            if (AppConstant.CAL_SHEET_TYPE_DO.equals(searchMaster.getCalSheetType())) {
                Integer updatedDo = supplyOrderSummaryDao.isUpdatedDo(connection, calSheetId);
                if (AppConstant.ZERO_INT < updatedDo) {
                    supplyOrderSummaryDao.updateDOUpdatedStatus(connection, calSheetId, AppConstant.NO);
                }
            }

//            String userId = (null == searchMaster.getSpecialTeamAssignUserId()) ? AppConstant.STRING_EMPTY : searchMaster.getSpecialTeamAssignUserId();
            boolean isProvideOffer = claimHandlerDao.isProvideOffer(connection, claimNo);
            boolean isLeave = false;
//            if (userId.isEmpty()) {
//                if (isProvideOffer) {
//                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
//                } else {
//                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
//                }
//            } else {
//                isLeave = claimUserAllocationDao.checkIfLeave(connection, isProvideOffer ? AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM : AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), userId);
//                if (isLeave) {
//                    userId = claimUserAllocationService.getNextAssignUser(connection, isProvideOffer ? AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM : AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
//                }
//            }
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 63);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());

            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been forwarded to the \"Special Team\" By Claim Handler".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            if ("C".equals(searchMaster.getNoObjectionStatus())) {
                claimCalculationSheetMainDao.updateNoObjectionStatus(connection, calSheetId, "A");
            }
            if ("C".equals(searchMaster.getPremiumOutstandingStatus())) {
                claimCalculationSheetMainDao.updatePremiumOutstandingStatus(connection, calSheetId, "A");
            }

            claimCalculationSheetMainDao.updateSpecialTeamAssignUser(connection, calSheetId, userId, Utility.sysDateTime());
            saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL, NotificationPriority.HIGH);
            saveConfirmationLogs(connection, claimNo, user, outStandingPremium, isCancelledPolicy);
            saveCalculationSheetAssignMofaLevel(connection, calSheetId, claimNo, user, userId, AppConstant.LEVEL_ONE);
            if (isLeave) {
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Forwarded", claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been Forward to " + userId + " as " + searchMaster.getSpecialTeamAssignUserId() + " is on Leave");
            } else {
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Forwarded", notificationMsg.concat(" [" + userId + "]"));
            }
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 63, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been forwarded to the \"Special Team\"", user.getUserId(), userId);
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToMofa(Integer calSheetId, Integer claimNo, String forwardingUserId, UserDto user) throws
            Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        String notificationMsg;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            validatePayeeDetails(connection, searchMaster);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Forwarded By Special Team.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 64);
            claimCalculationSheetMainDao.updateMofaTeamAssignUser(connection, calSheetId, forwardingUserId, Utility.sysDateTime());
            saveNotification(connection, claimNo, user.getUserId(), forwardingUserId, notificationMsg, URL);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 64, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been forwarded to the \"MOFA Team\"", user.getUserId(), forwardingUserId);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Forwarded ", notificationMsg.concat(" [" + forwardingUserId + "]"));
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToClaimHandlerBySpc(Integer calSheetId, Integer claimNo, UserDto user, boolean isAriRequestNReturn) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By Spare Parts Coordinator.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            saveNotification(connection, claimNo, user.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL, NotificationPriority.HIGH);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned", user.getUserId(), searchMaster.getAssignUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Returned ", notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
            saveClaimProcessFlow(connection, claimNo, 0, "Returned Claim File", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);

            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo) && !isAriRequestNReturn) {
                sendNotificationForJobPendingRte(claimNo, user, connection, false);
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToClaimHandlerBySpecialTeam(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By Special Team.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            if (AppConstant.CAL_SHEET_TYPE_DO.equals(searchMaster.getCalSheetType())) {
                Integer updatedDo = supplyOrderSummaryDao.isUpdatedDo(connection, calSheetId);
                if (updatedDo > AppConstant.ZERO_INT) {
                    notificationMsg = "DO amended. Please proceed DO again";
                    URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER)).concat("&N_REF_NO=").concat(String.valueOf(updatedDo));
                    supplyOrderSummaryDao.updateDOUpdatedStatus(connection, calSheetId, AppConstant.NO);
                }
            }
            saveNotification(connection, claimNo, user.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Returned", notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned", user.getUserId(), searchMaster.getAssignUserId());
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToClaimHandlerBySpecialTeamAfterApproved(Integer calSheetId, Integer claimNo, String
            specialRemark, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            claimHandlerDao.updateReserveAmountAfterApproved(connection, searchMaster.getPayableAmount(), searchMaster.getClaimNo());

            ClaimCalculationSheetMainTempDto tempDto = claimCalculationSheetMainTempDao.searchByCalsheetId(connection, calSheetId);

            if (null != tempDto) {
                claimCalculationSheetMainTempDao.updateReserveAmountAfterApproved(connection, tempDto.getReserveAmountAfterApproved().add(searchMaster.getPayableAmount()), calSheetId);
            }

            if (AppConstant.CAL_SHEET_TYPE_ADVANCED == searchMaster.getCalSheetType()) {//Advance =2
                ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, searchMaster.getClaimNo());
                if (searchMaster.getPayableAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal advanced = claimHandlerDto.getAprvAdvanceAmount() == null ? BigDecimal.ZERO : claimHandlerDto.getAprvAdvanceAmount();
                    if (!(BigDecimal.ZERO.compareTo(advanced) > 0)) {
                        claimHandlerDao.updateAdvanceAmount(connection, searchMaster.getClaimNo(), advanced.add(searchMaster.getPayableAmount()));
                        if (null != tempDto) {
                            claimCalculationSheetMainTempDao.updateAdvanceAmount(connection, tempDto.getAdvanceAmount().add(searchMaster.getPayableAmount()), calSheetId);
                        }
                    }
                }
            }
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = "Approved " + claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By Special Team .".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            if (AppConstant.CAL_SHEET_TYPE_DO.equals(searchMaster.getCalSheetType())) {
                Integer updatedDo = supplyOrderSummaryDao.isUpdatedDo(connection, calSheetId);
                if (AppConstant.ZERO_INT < updatedDo) {
                    notificationMsg = "DO amended. Please proceed DO again";
                    URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER)).concat("&N_REF_NO=").concat(String.valueOf(updatedDo));
                    //supplyOrderSummaryDao.updateDOUpdatedStatus(connection, calSheetId, AppConstant.NO);
                }
            }
            saveNotification(connection, claimNo, user.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
            saveClaimsLogs(connection, claimNo, user, "approved " + claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Returned", notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned after approved", user.getUserId(), searchMaster.getAssignUserId());

            saveClaimSpecialRemark(connection, user, claimNo, "Approved Calculation Sheet Returned", specialRemark);
            shiftNotificationOnAction(connection, claimNo, user);

            if (AppConstant.CAL_SHEET_TYPE_FULL_FINAL == searchMaster.getCalSheetType() || AppConstant.CAL_SHEET_TYPE_RELEASE_ORDER == searchMaster.getCalSheetType()) {
                cancelForAlreadyCreatedBalanceCalSheet(connection, claimNo, calSheetId, URL, user);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToClaimHandlerByMofa(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By MOFA User.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            if (AppConstant.CAL_SHEET_TYPE_DO.equals(searchMaster.getCalSheetType())) {
                Integer updatedDo = supplyOrderSummaryDao.isUpdatedDo(connection, calSheetId);
                if (updatedDo > AppConstant.ZERO_INT) {
                    notificationMsg = "DO amended. Please proceed DO again";
                    URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER)).concat("&N_REF_NO=").concat(String.valueOf(updatedDo));
                    supplyOrderSummaryDao.updateDOUpdatedStatus(connection, calSheetId, AppConstant.NO);
                }
            }
            saveNotification(connection, claimNo, user.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned", user.getUserId(), searchMaster.getAssignUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Returned", notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToClaimHandlerByStm(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);//62
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By Scrutinizing Team Member.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            saveNotification(connection, claimNo, user.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL, NotificationPriority.HIGH);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned by \"Scrutinizing Team\" ", user.getUserId(), searchMaster.getAssignUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Returned ", notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                sendNotificationForJobPendingRte(claimNo, user, connection, false);
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void cancelForAlreadyCreatedBalanceCalSheet(Connection connection, Integer claimNo, Integer calSheetId, String URL, UserDto user) throws Exception {
        try {
            ClaimCalculationSheetMainDto balanceDetailDto = claimCalculationSheetMainDao.getPendingBalanceCalsheet(connection, claimNo);
            if (null != balanceDetailDto) {
                claimCalculationSheetMainDao.updateStatus(connection, balanceDetailDto.getCalSheetId(), ClaimStatus.PAYMENT_CANCELLED.getClaimStatus());
                ClaimCalculationSheetMainTempDto tempDto = claimCalculationSheetMainTempDao.searchByCalsheetId(connection, calSheetId);
                if (null != tempDto) {
                    claimHandlerDao.updateReserveAmountAndReserveAmountAfterApproved(connection, tempDto.getReserveAmount(), tempDto.getReserveAmountAfterApproved(), claimNo);
                    ClaimCalculationSheetMainDto claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
                    offlineReserveClaim(connection, claimCalculationSheetMainDto.getClaimNo(), tempDto.getPrevReserveAmount(), 1, user);
                }

                String calSheetAssignUser = getCalsheetAssignUser(balanceDetailDto);
                String notificationMsg = "The Balance / Balance-Release Order Cal Sheet has been auto cancelled due to returning of the Full & Final / Release Order Cal Sheet by " + user.getUserId();

                if (calSheetAssignUser.equalsIgnoreCase(balanceDetailDto.getAssignUserId())) {
                    saveNotification(connection, claimNo, user.getUserId(), balanceDetailDto.getAssignUserId(), notificationMsg, URL, NotificationPriority.HIGH);
                } else {
                    saveNotification(connection, claimNo, user.getUserId(), calSheetAssignUser, notificationMsg, URL, NotificationPriority.HIGH);
                    saveNotification(connection, claimNo, user.getUserId(), balanceDetailDto.getAssignUserId(), notificationMsg, URL, NotificationPriority.HIGH);
                }

                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, ClaimStatus.PAYMENT_CANCELLED.getClaimStatus(), balanceDetailDto.getCalSheetTypeDesc() + " Cal Sheet has been auto cancelled due to returning of the Full & Final / Release Order Cal Sheet by Special Team", user.getUserId(), balanceDetailDto.getAssignUserId());
                saveClaimsLogs(connection, claimNo, user, balanceDetailDto.getCalSheetTypeDesc() + " Calculation Sheet[" + balanceDetailDto.getCalSheetId() + "] Cancelled ", "The Balance / Balance-Release Order Cal Sheet has been auto cancelled due to returning the Full & Final / Release Order Cal Sheet by " + user.getUserId());

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void returnToSparePartCoordinatorByStm(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        String assignUser;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);

            if (null == searchMaster.getSparePartCordinatorAssignUserId()) {
                assignUser = claimUserAllocationService.getNextAssignUser(connection, 27, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                claimCalculationSheetMainDao.updateSparePartCoordinatorAssignDetails(connection, calSheetId, 59, assignUser);
            } else {
                assignUser = searchMaster.getSparePartCordinatorAssignUserId();
                claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 59);
            }
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By Scrutinizing Team Member.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            saveNotification(connection, claimNo, user.getUserId(), assignUser, notificationMsg, URL, NotificationPriority.MEDIUM);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 59, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned by \"Scrutinizing Team\" ", user.getUserId(), assignUser);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Returned : ", notificationMsg.concat(" [" + searchMaster.getSparePartCordinatorAssignUserId() + "]"));
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void validatePayeeDetails(Connection connection, ClaimCalculationSheetMainDto
            claimCalculationSheetMainDto) throws MisynAppException {
        List<ClaimCalculationSheetPayeeDto> payeeList = null;
        try {
            payeeList = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, claimCalculationSheetMainDto.getCalSheetId());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        if (payeeList == null || (null != payeeList && payeeList.isEmpty())) {
            throw new MisynAppException("301");//Please select valid payee
        }

        for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : payeeList) {
            if (claimCalculationSheetPayeeDto.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new MisynAppException("302");//Please select valid payee amount
            }
            if (null == claimCalculationSheetPayeeDto.getPayeeDesc() || claimCalculationSheetPayeeDto.getPayeeDesc().isEmpty() ||
                    AppConstant.ZERO.equals(claimCalculationSheetPayeeDto.getPayeeDesc())) {
                throw new MisynAppException("303");
            }
        }
    }

    @Override
    public void approvePayment(Integer calSheetId, Integer claimNo, UserDto recommendUser, BigDecimal outstandingPremium, boolean isCancelledPolicy,String userId) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserDto assignUser = userDao.getUserByUsrid(connection, userId);
            checkReserveAmount(connection, claimNo);
            isCheckLastPaymentAlreadyVoucherGenerated(connection, claimNo);
            claimCalculationSheetMainDao.updatePaymentApproveStatus(connection, calSheetId, 65, recommendUser.getUserId());
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            claimUserUpdateDao.updateCalsheetAssignMofaUser(connection,assignUser.getUserId(),calSheetId);
            int calSheetType = searchMaster.getCalSheetType();
            checkExcessReserveAmount(connection, claimNo, calSheetType);

            validatePayeeDetails(connection, searchMaster);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());

            if (!recommendUser.getUserId().equals(searchMaster.getSpecialTeamAssignUserId())) {
                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Approved By MOFA User.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                if (calSheetType == AppConstant.CAL_SHEET_TYPE_DO) {
                    notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved. Please proceed Delivery Order ".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                    claimHandlerDao.updateStoreStatus(connection, claimNo, AppConstant.YES);
                    setDefineDocument(connection, claimNo, AppConstant.DO_BILL_DOCUMENT_TYPE_ID, recommendUser.getUserId());
                    saveNotification(connection, claimNo, recommendUser.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
                    saveClaimsLogs(connection, claimNo, recommendUser, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Approved. [".concat(recommendUser.getUserId()).concat("]"), notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
                    this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 65, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved by \"MOFA Team\" .Please proceed Delivery Order", recommendUser.getUserId(), searchMaster.getAssignUserId());


                } else if (calSheetType == AppConstant.CAL_SHEET_TYPE_RELEASE_ORDER) {
                    notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved. Please Generate Release Order Letter ".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                    // claimHandlerDao.updateStoreStatus(connection, claimNo, AppConstant.YES);
                    setDefineDocument(connection, claimNo, AppConstant.GARAGE_BILL_DOCUMENT_TYPE_ID, recommendUser.getUserId());
                    saveNotification(connection, claimNo, recommendUser.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
                    saveClaimsLogs(connection, claimNo, recommendUser, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Approved. [".concat(recommendUser.getUserId()).concat("]"), notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
                    this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 65, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved by \"MOFA Team\" .Please Generate Release Order Letter", recommendUser.getUserId(), searchMaster.getAssignUserId());

                } else {
                    saveNotification(connection, claimNo, recommendUser.getUserId(), searchMaster.getSpecialTeamAssignUserId(), notificationMsg, URL);
                    saveClaimsLogs(connection, claimNo, recommendUser, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Approved. [".concat(recommendUser.getUserId()).concat("]"), notificationMsg.concat(" [" + searchMaster.getSpecialTeamAssignUserId() + "]"));
                    this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 65, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet approved by \"MOFA Team\"", recommendUser.getUserId(), searchMaster.getSpecialTeamAssignUserId());
                }
            } else {
                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Approved By Special Team.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                if (calSheetType == AppConstant.CAL_SHEET_TYPE_DO) {
                    notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved. Please proceed Delivery Order ".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                    claimHandlerDao.updateStoreStatus(connection, claimNo, AppConstant.YES);
                    setDefineDocument(connection, claimNo, AppConstant.DO_BILL_DOCUMENT_TYPE_ID, recommendUser.getUserId());
                    saveNotification(connection, claimNo, recommendUser.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
                    saveClaimsLogs(connection, claimNo, recommendUser, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Approved. [".concat(recommendUser.getUserId()).concat("]"), notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
                    this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 65, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved.Please proceed Delivery Order", recommendUser.getUserId(), searchMaster.getAssignUserId());
                } else if (calSheetType == AppConstant.CAL_SHEET_TYPE_RELEASE_ORDER) {
                    notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved. Please Generate Release Order Letter ".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                    setDefineDocument(connection, claimNo, AppConstant.GARAGE_BILL_DOCUMENT_TYPE_ID, recommendUser.getUserId());
                    saveNotification(connection, claimNo, recommendUser.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
                    saveClaimsLogs(connection, claimNo, recommendUser, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Approved. [".concat(recommendUser.getUserId()).concat("]"), notificationMsg.concat(" [" + searchMaster.getAssignUserId() + "]"));
                    this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 65, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Payment approved. Please Generate Release Order Letter ", recommendUser.getUserId(), searchMaster.getAssignUserId());
                } else {
                    saveClaimsLogs(connection, claimNo, recommendUser, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Approved. [".concat(recommendUser.getUserId()).concat("]"), notificationMsg.concat(" [" + searchMaster.getSpecialTeamAssignUserId() + "]"));
                    this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 65, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet approved by \"Special Team\"", recommendUser.getUserId(), searchMaster.getSpecialTeamAssignUserId());
                    saveNotification(connection, claimNo, recommendUser.getUserId(), assignUser.getUserId(), notificationMsg, URL);
                }
                saveConfirmationLogs(connection, claimNo, recommendUser, outstandingPremium, isCancelledPolicy);
            }
            claimHandlerDao.updateReserveAcrAmountAfetrApproved(searchMaster.getPayableAmount(), connection, searchMaster.getClaimNo());
            BigDecimal acrAmount = claimHandlerDao.getTotalAcrAmount(connection, claimNo);
            if(recommendUser.getAccessUserType() != 49){
                sendLargeClaimEmail(connection, claimNo, acrAmount, searchMaster.getPayableAmount(), claimCalculationSheetTypeDto.getVCalSheetTypeDesc().concat(" Paid"), recommendUser, calSheetType);
            }
            CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto = new CalculationSheetAssignMofaLevelDetailDto();
            calculationSheetAssignMofaLevelDetailDto.setCalsheetId(calSheetId);
            calculationSheetAssignMofaLevelDetailDto.setClaimNo(claimNo);
            calculationSheetAssignMofaLevelDetailDto.setInputUserId(recommendUser.getUserId());
            calculationSheetAssignMofaLevelDetailDto.setInputMofaLevel(userAuthorityLimitDao.getMofaLevel(connection, BigDecimal.valueOf(recommendUser.getPaymentAuthLimit()), AppConstant.MOFA_TEAM_DEPARTMENT_ID));
            calculationSheetAssignMofaLevelDetailDto.setAssignUserId(AppConstant.STRING_EMPTY);
            calculationSheetAssignMofaLevelDetailDto.setAssignMofaLevel(AppConstant.ZERO_INT);
            calculationSheetAssignMofaLevelDetailDto.setAssignDatetime(Utility.sysDateTime());
            calculationSheetAssignMofaLevelDetailDao.insertMaster(connection, calculationSheetAssignMofaLevelDetailDto);
            calculationSheetAssignMofaLevelDetailDao.insertHistory(connection, calculationSheetAssignMofaLevelDetailDto);

            ClaimCalculationSheetMainTempDto tempDto = claimCalculationSheetMainTempDao.searchByCalsheetId(connection, calSheetId);
            if (null != tempDto) {
                claimCalculationSheetMainTempDao.updateReserveAmountAfterApproved(connection, tempDto.getReserveAmountAfterApproved().subtract(searchMaster.getPayableAmount()), calSheetId);
            }
            if (AppConstant.CAL_SHEET_TYPE_ADVANCED.equals(searchMaster.getCalSheetType())) {//Advance =2
                ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, searchMaster.getClaimNo());
                BigDecimal advanced = claimHandlerDto.getAprvAdvanceAmount() == null ? BigDecimal.ZERO : claimHandlerDto.getAprvAdvanceAmount();
                BigDecimal newAdvanceAmount = advanced.subtract(searchMaster.getPayableAmount());
                if (searchMaster.getPayableAmount().compareTo(BigDecimal.ZERO) > 0
                        && advanced.compareTo(BigDecimal.ZERO) > 0
                        && !(newAdvanceAmount.compareTo(BigDecimal.ZERO) < 0)) {
                    claimHandlerDao.updateAdvanceAmount(connection, searchMaster.getClaimNo(), advanced.subtract(searchMaster.getPayableAmount()));
                    if (null != tempDto) {
                        claimCalculationSheetMainTempDao.updateAdvanceAmount(connection, tempDto.getAdvanceAmount().subtract(searchMaster.getPayableAmount()), calSheetId);
                    }
                } else {
                    throw new WrongValueException(AppConstant.ADVANCE_AMOUNT, "Advance Amount Error");
                }
            }
            shiftNotificationOnAction(connection, claimNo, recommendUser);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void checkExcessReserveAmount(Connection connection, Integer claimNo, int calSheetType) throws Exception {
        try {
            if (AppConstant.CAL_SHEET_TYPE_BALANCE.equals(calSheetType)
                    || AppConstant.CAL_SHEET_TYPE_BALANCE_RELEASE_ORDER.equals(calSheetType)) {
                BigDecimal reserveAmount = claimHandlerDao.getReserveAmount(connection, claimNo);
                BigDecimal totalPayableAmount = claimCalculationSheetDetailDao.getTotalPayableAmount(connection, claimNo);
                if (reserveAmount.compareTo(totalPayableAmount) > 0) {
                    throw new ErrorMsgException(AppConstant.EXCESS_RESERVE_ERROR_MSG, "Excess Reserve Available in the System");
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;

        }
    }

    private void isCheckLastPaymentAlreadyVoucherGenerated(Connection connection, Integer claimNo) throws Exception {
        try {
            if (claimCalculationSheetMainDao.isAvailableCalSheetByStatus(connection, claimNo, ClaimStatus.PAYMENT_APPROVED.getClaimStatus())) {
                throw new ErrorMsgException(AppConstant.PAYMENT_APPROVE_ERROR_MSG, "Please generate voucher for the previous cal sheet prior to approving this cal sheet");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }


    private void checkReserveAmount(Connection connection, Integer claimNo) throws Exception {
        try {
            BigDecimal reserveAmount = claimHandlerDao.getReserveAmount(connection, claimNo);
            BigDecimal totalPayableAmount = claimCalculationSheetDetailDao.getTotalPayableAmount(connection, claimNo);
            if (totalPayableAmount.compareTo(reserveAmount) > 0) {
                throw new ErrorMsgException();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;

        }
    }

    private void setDefineDocument(Connection connection, int claimNo, int documentTypeId, String userId) throws
            Exception {
        try {
            ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
            claimWiseDocumentDto.setClaimNo(claimNo);
            claimWiseDocumentDto.setIsMandatory(AppConstant.YES);
            claimWiseDocumentDto.setDocReqFrom(5);
            claimWiseDocumentDto.setDocumentTypeId(documentTypeId);
            claimWiseDocumentDto.setInpUserId(userId);
            claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
            claimWiseDocumentDao.updateDefineDocumentByDocumentTypeId(connection, claimWiseDocumentDto);
        } catch (MisynJDBCException e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void setRevokeDoc(Connection connection, int claimNo, int documentTypeId, String userId) throws
            Exception {
        try {
            ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
            claimWiseDocumentDto.setClaimNo(claimNo);
            claimWiseDocumentDto.setIsMandatory(AppConstant.NO);
            claimWiseDocumentDto.setDocReqFrom(5);
            claimWiseDocumentDto.setDocumentTypeId(documentTypeId);
            claimWiseDocumentDto.setInpUserId(userId);
            claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
            claimWiseDocumentDao.updateDefineDocumentByDocumentTypeId(connection, claimWiseDocumentDto);
        } catch (MisynJDBCException e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    public void setStoredYes(Integer claimNo) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimHandlerDao.updateStoreStatus(connection, claimNo, AppConstant.YES);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateReleaseOrderDetails(int claimNo, int calSheetId, String isReleaseOrderGenerate, String
            releaseOrderGenerateUserId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimHandlerDao.updateStoreStatus(connection, claimNo, AppConstant.YES);
            claimCalculationSheetMainDao.updateReleaseOrderDetails(connection, calSheetId, isReleaseOrderGenerate, releaseOrderGenerateUserId);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void rejectPayment(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimCalculationSheetMainDao.updatePaymentApproveStatus(connection, calSheetId, 66, user.getUserId());
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            if (!user.getUserId().equals(searchMaster.getSpecialTeamAssignUserId())) {
                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Rejected By MOFA User.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                saveNotification(connection, claimNo, user.getUserId(), searchMaster.getSpecialTeamAssignUserId(), notificationMsg, URL);
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Rejected [ ".concat(user.getUserId()).concat("]"), notificationMsg);
                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 66, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet rejected by \"MOFA User\"", user.getUserId(), searchMaster.getSpecialTeamAssignUserId());
            } else {
                String userId = searchMaster.getInputUser();
                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Rejected By Special Team.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Rejected [ ".concat(user.getUserId()).concat("]"), notificationMsg);
                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 66, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet rejected by \"Special Team\"", user.getUserId(), userId);
                saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL);
            }

            ClaimCalculationSheetMainTempDto tempDto = claimCalculationSheetMainTempDao.searchByCalsheetId(connection, calSheetId);
            if (null != tempDto) {
                claimHandlerDao.updateReserveAmountAndReserveAmountAfterApproved(connection, tempDto.getPrevReserveAmount(), tempDto.getPrevReserveAmountAfterApproved(), searchMaster.getClaimNo());
                ClaimCalculationSheetMainDto claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
                offlineReserveClaim(connection, claimCalculationSheetMainDto.getClaimNo(), tempDto.getPrevReserveAmount(), 1, user);

                claimCalculationSheetMainTempDao.deleteMaster(connection, calSheetId);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void generateVoucher(Integer calSheetId, Integer claimNo, BigDecimal outstandingPremium, boolean isCancelledPolicy, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            checkReserveAmountApprovedCalsheet(connection, claimNo);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            if (AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING != searchMaster.getStatus()) {
                validatePayeeDetails(connection, searchMaster);
                claimCalculationSheetMainDao.updateStatusVoucherGenerate(connection, calSheetId, 70, user.getUserId());
                saveClaimOfflinePayemnt(connection, calSheetId, claimNo);
                claimCalculationSheetMainTempDao.deleteMaster(connection, calSheetId);
                checkExcessReserveAmountApprovedCalsheet(connection, claimNo, searchMaster.getCalSheetType());

                if (searchMaster.getCalSheetType().equals(AppConstant.CAL_SHEET_TYPE_DO)) {
                    supplyOrderSummaryDao.updateDOUpdatedStatus(connection, calSheetId, AppConstant.NO);
                }

                ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
                if (searchMaster.getCalSheetType() == AppConstant.CAL_SHEET_TYPE_FULL_FINAL
                        || searchMaster.getCalSheetType() == AppConstant.CAL_SHEET_TYPE_RELEASE_ORDER) {
                    claimHandlerService.updateClaimSettle(connection, claimNo, user);
                    BigDecimal advanceAmount = claimHandlerDao.getAdvanceAmount(connection, claimNo);
                    boolean isAdvanceReset = claimHandlerDao.checkAndUpdateAdvanceAmount(connection, claimNo, BigDecimal.ZERO);
                    if (isAdvanceReset) {
                        BigDecimal paidTotalAdvanceAmount = claimCalculationSheetMainDao.getPaidTotalAdvanceAmount(connection, claimNo);
                        saveClaimsLogs(connection, claimNo, user, "Advance Summary", "‘Balance Engineering Approved Advance Total Amount (Rs." + advanceAmount + ") and the Paid Advance Total Amount (Rs." + paidTotalAdvanceAmount + ")");
                        saveClaimsLogs(connection, claimNo, user, "Advance Amount Reset", "Voucher Generated for " + claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet and Balance Advance Amount (Rs." + advanceAmount + ") Set to '0.00'");
                    }
                }

                List<ClaimCalculationSheetPayeeDto> calculationSheetPayeeDtos = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, calSheetId);

                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Voucher Generated in CMS - Pending in ISF.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                saveNotification(connection, claimNo, user.getUserId(), searchMaster.getInputUser(), notificationMsg, URL);

                saveConfirmationLogs(connection, claimNo, user, outstandingPremium, isCancelledPolicy);
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet No : ".concat(String.valueOf(calSheetId)), "Calculation Sheet Voucher Generated in CMS - Pending in ISF.");
                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 70, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Voucher Generated in CMS - Pending in ISF.", user.getUserId(), searchMaster.getInputUser());
                shiftNotificationOnAction(connection, claimNo, user);
                commitTransaction(connection);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void checkExcessReserveAmountApprovedCalsheet(Connection connection, Integer claimNo, Integer calSheetType) throws Exception {
        try {
            if (AppConstant.CAL_SHEET_TYPE_BALANCE.equals(calSheetType)
                    || AppConstant.CAL_SHEET_TYPE_BALANCE_RELEASE_ORDER.equals(calSheetType)) {
                BigDecimal reserveAmount = claimHandlerDao.getReserveAmount(connection, claimNo);
                BigDecimal totalPayableAmount = claimCalculationSheetDetailDao.getTotalPayableAmountApprovedCalsheet(connection, claimNo);
                if (reserveAmount.compareTo(totalPayableAmount) > 0) {
                    throw new ErrorMsgException(AppConstant.EXCESS_RESERVE_ERROR_MSG, "Excess Reserve Available in the System");
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void checkReserveAmountApprovedCalsheet(Connection connection, Integer claimNo) throws Exception {
        try {
            BigDecimal reserveAmount = claimHandlerDao.getReserveAmount(connection, claimNo);
            BigDecimal totalPayableAmount = claimCalculationSheetDetailDao.getTotalPayableAmountApprovedCalsheet(connection, claimNo);
            if (totalPayableAmount.compareTo(reserveAmount) > 0) {
                throw new ErrorMsgException();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;

        }
    }

    @Override
    public void returnToClaimHandlerByScrutinizingTeam(Integer calSheetId, Integer claimNo, UserDto user, boolean isAriRequestNReturn) throws
            Exception {

        /*String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                .concat("&P_CAL_SHEET_NO=")
                .concat(String.valueOf(calSheetId))
                .concat("&P_TAB_INDEX=").concat(String.valueOf(15));*/

        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By Scrutinizing Team Member.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            saveNotification(connection, claimNo, user.getUserId(), searchMaster.getAssignUserId(), notificationMsg, URL);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Returned By Scrutinizing Team Member : "
                    .concat(" [").concat(searchMaster.getAssignUserId()).concat("] "), notificationMsg);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned by \"Scrutinizing Team\"", user.getUserId(), searchMaster.getAssignUserId());

            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo) && !isAriRequestNReturn) {
                sendNotificationForJobPendingRte(claimNo, user, connection, false);
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }

            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }

    }

    @Override
    public void recallByClaimHandler(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        String assignUserId = AppConstant.STRING_EMPTY;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            if (searchMaster != null) {
                if (searchMaster.getStatus() == 59) {
                    assignUserId = searchMaster.getSparePartCordinatorAssignUserId();
                } else if (searchMaster.getStatus() == 61) {
                    assignUserId = searchMaster.getScrutinizeTeamAssignUserId();
                }
            }
            if (AppConstant.FORWARD_TO_ENGINEER.equals(claimHandlerDto.getClaimStatus())) {
                throw new ErrorMsgException("RECALL", "Recall failed! Claim file is already forwarded to Engineer By - [" + assignUserId + "]");
            }

            if (null != searchMaster) {
                if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                    UserDto user1 = new UserDto();
                    if (searchMaster.getStatus() == 59) { //Forwarded to the Spare Parts Coordinator
                        user1.setUserId(searchMaster.getSparePartCordinatorAssignUserId());
                    } else if (searchMaster.getStatus() == 61) { //Forwarded to the Bill Checking Team
                        user1.setUserId(searchMaster.getScrutinizeTeamAssignUserId());
                    }
                    sendNotificationForJobPendingRte(claimNo, user1, connection, false);
                    rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
                }
                ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
                claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);
                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Recall By Claim handler [" + user.getUserId() + "].".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                saveNotification(connection, claimNo, user.getUserId(), assignUserId, notificationMsg, URL);
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Recall By Claim handler : "
                        .concat(" [").concat(user.getUserId()).concat("] "), notificationMsg);
                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet recall by \"Claim handler\"", user.getUserId(), user.getUserId());
            }
            commitTransaction(connection);
        } catch (ErrorMsgException e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new ErrorMsgException(e.getField(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToSparePartCoordinatorByScrutinizingTeam(Integer calSheetId, Integer claimNo, UserDto user) throws
            Exception {
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        String URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                .concat("&P_CAL_SHEET_NO=")
                .concat(String.valueOf(calSheetId))
                .concat("&TYPE=7")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(9));

        Connection connection = null;
        String assignUserId;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 59);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            if (null == searchMaster.getSparePartCordinatorAssignUserId()) {
                assignUserId = claimUserAllocationService.getNextAssignUser(connection, 27, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                claimCalculationSheetMainDao.updateSparePartCoordinatorAssignDetails(connection, calSheetId, 59, assignUserId);
            } else {
                assignUserId = searchMaster.getSparePartCordinatorAssignUserId();
            }
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Returned By Scrutinizing Team Member.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            saveNotification(connection, claimNo, user.getUserId(), assignUserId, notificationMsg, URL);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Returned By Scrutinizing Team Member : "
                    .concat(" [").concat(assignUserId).concat("] "), notificationMsg);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 59, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet returned by \" Scrutinizing Team\"", user.getUserId(), assignUserId);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void callNoObjection(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            claimCalculationSheetMainDao.updateNoObjectionStatus(connection, calSheetId, "C");
            setDefineDocument(connection, claimNo, AppConstant.NO_OBJECTION_LETTER_DOCUMENT_TYPE_ID, user.getUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet No : ".concat(String.valueOf(calSheetId)), "Call No objection");
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, searchMaster.getStatus(), "\"No Objection\"has been called", user.getUserId(), user.getUserId());
            createNoObejctionEmail(connection, email, claimNo, calSheetId, searchMaster);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void revokeNoObjection(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            claimCalculationSheetMainDao.updateNoObjectionStatus(connection, calSheetId, "P");
            claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            setRevokeDoc(connection, claimNo, AppConstant.NO_OBJECTION_LETTER_DOCUMENT_TYPE_ID, user.getUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet No : ".concat(String.valueOf(calSheetId)), "Revoke No objection");
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, searchMaster.getStatus(), "\"No Objection\"has been revoked", user.getUserId(), user.getUserId());
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void callPremiumOutstanding(Integer calSheetId, Integer claimNo, String email, UserDto user) throws
            Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            claimCalculationSheetMainDao.updatePremiumOutstandingStatus(connection, calSheetId, "C");
            setDefineDocument(connection, claimNo, AppConstant.PREMIUM_OUTSTANDING_CONFIRMATION_DOCUMENT_TYPE_ID, user.getUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet No : ".concat(String.valueOf(calSheetId)), "Call premium outstanding");
            createPremiumConfirmationEmail(connection, email, claimNo);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, searchMaster.getStatus(), claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been  called \"Premium Outstanding\"", user.getUserId(), user.getUserId());
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void revokePremiumOutstanding(Integer calSheetId, Integer claimNo, String email, UserDto user) throws
            Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            claimCalculationSheetMainDao.updatePremiumOutstandingStatus(connection, calSheetId, "P");
            setRevokeDoc(connection, claimNo, AppConstant.PREMIUM_OUTSTANDING_CONFIRMATION_DOCUMENT_TYPE_ID, user.getUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet No : ".concat(String.valueOf(calSheetId)), "revoke premium outstanding");
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, searchMaster.getStatus(), claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been  revoked \"Premium Outstanding\"", user.getUserId(), user.getUserId());
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getCalculationSheetDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId,
                                                      int start, int length, String orderType, String orderField, String fromDate, String toDate, int type) {
        Connection connection = null;
        DataGridDto dataGridDto = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimCalculationSheetDetailDao.getClaimHandlerDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return dataGridDto;
    }

    @Override
    public List<UserDto> getMofaUserList(String amount, Integer claimNo) throws Exception {

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            int accessUserType;
            accessUserType = getAccessUserType(connection, claimNo);
            List<UserDto> userList = claimCalculationSheetMainDao.getMofaUserList(connection, amount, accessUserType);
            return userList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private int getAccessUserType(Connection connection, Integer claimNo) throws Exception {
        int accessUserType;
        try {
            if (claimHandlerDao.isProvideOffer(connection, claimNo)) {
                accessUserType = AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM;
            } else {
                accessUserType = AppConstant.ACCESS_LEVEL_MOFA_TEAM;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return accessUserType;
    }

    private int getAccessUserType(Connection connection, String assignUser) throws Exception {
        try {
            return userDao.getAccessUserTypeByUserId(connection, assignUser);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private ClaimDocumentStatusDto getClaimDocumentStatus(Connection connection, Integer claimNo, int type) throws
            Exception {
        try {
            ClaimDocumentStatusDto documentStatusDto = claimDocumentService.getDocumentStatus(connection, claimNo, type);
            return documentStatusDto;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public ClaimDocumentStatusDto getClaimDocumentStatus(Integer claimNo, int type) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            ClaimDocumentStatusDto documentStatusDto = claimDocumentService.getDocumentStatus(connection, claimNo, type);
            return documentStatusDto;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Integer isSupplyOrderCalculationSheetApproved(Integer supplyOrderId) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimCalculationSheetMainDao.isSupplyOrderCalculationSheetApproved(connection, supplyOrderId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public BigDecimal getTotalPaidForClaim(Connection connection, Integer claimNo) throws Exception {

        try {
            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
            BigDecimal total = BigDecimal.ZERO;
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : claimCalculationSheetMainDtos) {
                if (67 == claimCalculationSheetMainDto.getStatus() || 65 == claimCalculationSheetMainDto.getStatus() || 70 == claimCalculationSheetMainDto.getStatus()) {
                    total = total.add(claimCalculationSheetMainDto.getPayableAmount());
                }
            }
            return total;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void saveSpecialRemark(Integer calsheetId, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            CalSheetSpecialRemarkDto calSheetSpecialRemarkDto = new CalSheetSpecialRemarkDto();
            calSheetSpecialRemarkDto.setCalsheetId(calsheetId);
            calSheetSpecialRemarkDto.setRemark(remark);
            calSheetSpecialRemarkDto.setInpDatetime(Utility.sysDateTime());
            calSheetSpecialRemarkDto.setInpUser(user.getUserId());
            calSheetSpecialRemarkDao.save(connection, calSheetSpecialRemarkDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void saveSpecialRemark(Connection connection, Integer calsheetId, String remark, UserDto user) throws
            Exception {

        try {
            CalSheetSpecialRemarkDto calSheetSpecialRemarkDto = new CalSheetSpecialRemarkDto();
            calSheetSpecialRemarkDto.setCalsheetId(calsheetId);
            calSheetSpecialRemarkDto.setRemark(remark);
            calSheetSpecialRemarkDto.setInpDatetime(Utility.sysDateTime());
            calSheetSpecialRemarkDto.setInpUser(user.getUserId());
            calSheetSpecialRemarkDao.save(connection, calSheetSpecialRemarkDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public List<CalSheetSpecialRemarkDto> calSheetRemarkList(Integer calSheetId) throws Exception {
        List<CalSheetSpecialRemarkDto> list = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = calSheetSpecialRemarkDao.calSheetRemarkList(connection, calSheetId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public boolean isPendingAdvancedPayment(List<ClaimCalculationSheetMainDto> list) throws Exception {
        try {
            for (ClaimCalculationSheetMainDto calSheet : list) {
                if (calSheet.getCalSheetType() == AppConstant.CAL_SHEET_TYPE_ADVANCED && calSheet.getStatus() != AppConstant.CAL_SHEET_PAYMENT_APPROVED
                        && calSheet.getStatus() != AppConstant.CAL_SHEET_PAYMENT_REJECTED
                        && calSheet.getStatus() != ClaimStatus.PAYMENT_CANCELLED.getClaimStatus()
                        && calSheet.getStatus() != AppConstant.CAL_SHEET_VOUCHER_GENERATE
                        && calSheet.getStatus() != AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING) {
                    return true;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean isPendingPayment(List<ClaimCalculationSheetMainDto> list) throws Exception {

        try {
            for (ClaimCalculationSheetMainDto calSheet : list) {
                if (calSheet.getStatus() != AppConstant.CAL_SHEET_PAYMENT_APPROVED
                        && calSheet.getStatus() != AppConstant.CAL_SHEET_PAYMENT_REJECTED
                        && calSheet.getStatus() != ClaimStatus.PAYMENT_CANCELLED.getClaimStatus()
                        && calSheet.getStatus() != AppConstant.CAL_SHEET_VOUCHER_GENERATE
                        && calSheet.getStatus() != AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING) {
                    return true;
                }

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public SupplyOrderSummaryDto getSupplierDetails(Integer supplierId) throws Exception {
        SupplyOrderSummaryDto supplyOrderSummaryDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, supplierId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return supplyOrderSummaryDto;
    }

    private List<CalSheetTypeDto> getCalSheetTypes(Connection connection, Integer claimNo, Integer calSheetId) {
        List<CalSheetTypeDto> list = new ArrayList<>();
        try {
            List<CalSheetTypeDto> calSheetType = claimCalculationSheetMainDao.getCalSheetType(connection);
            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
            if (calSheetId > 0) {
                claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            }

            boolean fullAndFinal = false;
            boolean releaseOrder = false;
            for (ClaimCalculationSheetMainDto cal : claimCalculationSheetMainDtos) {
                if ((cal.getCalSheetType() == 1)
                        && cal.getCalSheetId() != 0
                        && cal.getCalSheetId() != calSheetId
                        && (cal.getStatus() == ClaimStatus.PAYMENT_APPROVED.getClaimStatus()
                        || cal.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE
                        || cal.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING)) {
                    fullAndFinal = true;
                    break;
                } else if ((cal.getCalSheetType() == 5)
                        && cal.getCalSheetId() != 0
                        && cal.getCalSheetId() != calSheetId
                        && (cal.getStatus() == ClaimStatus.PAYMENT_APPROVED.getClaimStatus()
                        || cal.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE
                        || cal.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING)) {
                    releaseOrder = true;
                    break;
                }
            }
            if (null != claimCalculationSheetMainDto && claimCalculationSheetMainDto.getCalSheetType() == 2
                    && (claimCalculationSheetMainDto.getStatus() == ClaimStatus.PAYMENT_APPROVED.getClaimStatus()
                    || claimCalculationSheetMainDto.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE
                    || claimCalculationSheetMainDto.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING)) {
                for (CalSheetTypeDto calSheetTypeDto : calSheetType) {
                    if (calSheetTypeDto.getTypeId() != 4 && calSheetTypeDto.getTypeId() != 7) {
                        list.add(calSheetTypeDto);
                    }
                }
            } else if (!fullAndFinal && !releaseOrder) {
                for (CalSheetTypeDto calSheetTypeDto : calSheetType) {
                    if (calSheetTypeDto.getTypeId() != 4 && calSheetTypeDto.getTypeId() != 7) {
                        list.add(calSheetTypeDto);
                    }
                }
            } else if (fullAndFinal) {
                for (CalSheetTypeDto calSheetTypeDto : calSheetType) {
                    if ((calSheetTypeDto.getTypeId() == 1) && null != claimCalculationSheetMainDto
                            && (claimCalculationSheetMainDto.getStatus() == ClaimStatus.PAYMENT_APPROVED.getClaimStatus()
                            || claimCalculationSheetMainDto.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE
                            || claimCalculationSheetMainDto.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING)) {
                        if (calSheetTypeDto.getTypeId() != 7) {
                            list.add(calSheetTypeDto);
                        }
                    } else if (calSheetTypeDto.getTypeId() != 1 && calSheetTypeDto.getTypeId() != 2 && calSheetTypeDto.getTypeId() != 5 && calSheetTypeDto.getTypeId() != 7) {
                        list.add(calSheetTypeDto);
                    }

                }
            } else if (releaseOrder) {
                for (CalSheetTypeDto calSheetTypeDto : calSheetType) {
                    if ((calSheetTypeDto.getTypeId() == 5) && null != claimCalculationSheetMainDto
                            && (claimCalculationSheetMainDto.getStatus() == ClaimStatus.PAYMENT_APPROVED.getClaimStatus()
                            || claimCalculationSheetMainDto.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE
                            || claimCalculationSheetMainDto.getStatus() == AppConstant.CAL_SHEET_VOUCHER_GENERATE_PENDING)) {
                        if (calSheetTypeDto.getTypeId() != 4) {
                            list.add(calSheetTypeDto);
                        }
                    } else if (calSheetTypeDto.getTypeId() != 1 && calSheetTypeDto.getTypeId() != 2 && calSheetTypeDto.getTypeId() != 5 && calSheetTypeDto.getTypeId() != 4) {
                        list.add(calSheetTypeDto);
                    }

                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    private List<ClaimCalculationSheetSupplierOrderDto> getSupplierDoList(Connection connection, Integer claimNo) {
        List<ClaimCalculationSheetSupplierOrderDto> list = new ArrayList<>();
        try {

            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDao.searchByClaimNo(connection, claimNo);
            for (ClaimCalculationSheetMainDto cal : claimCalculationSheetMainDtos) {
                if (cal.getCalSheetType() == 3) {
                    ClaimCalculationSheetSupplierOrderDto claimSupplier = new ClaimCalculationSheetSupplierOrderDto();
                    claimSupplier.setAmount(cal.getPayableAmount());
                    ClaimCalculationSheetSupplierOrderDto claimCalculationSheetSupplierOrderDto = claimCalculationSheetSupplierOrderDao.searchMasterByCalSheetId(connection, cal.getCalSheetId());
                    if (null != claimCalculationSheetSupplierOrderDto) {
                        claimSupplier.setSerialNumber(supplyOrderSummaryDao.searchMasterBySupplyOrderRefNo(connection, claimCalculationSheetSupplierOrderDto.getSupplierOrderId()).getSupplyOrderSerialNo());
                        list.add(claimSupplier);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public void updateClaSheetPrintStatus(Integer calSheetId, String user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimCalculationSheetMainDao.updateClaSheetPrintStatus(connection, calSheetId, user, Utility.sysDate());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private Email createPremiumConfirmationEmail(Connection connection, String emailVal, Integer claimNo) throws
            Exception {
        try {
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.PREMIUM_CONFIRMATION_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();
                if (null != search.getVehicleNo() && !search.getVehicleNo().isEmpty()) {
                    list.add(search.getVehicleNo());
                } else {
                    list.add(search.getCoverNoteNo());
                }

                list.add(search.getPolicyDto().getPolicyNumber());
                list.add(getTotalPaidForClaim(connection, claimNo).toString());
                list.add(search.getAccidDate());
                list.add(null == search.getPolicyDto().getPolicyBranch() || search.getPolicyDto().getPolicyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getPolicyBranch());
                list.add(null == search.getPolicyDto().getFinanceCompany() || search.getPolicyDto().getFinanceCompany().isEmpty() ? "N/A" : search.getPolicyDto().getFinanceCompany());
                list.add(null == search.getPolicyDto().getFinCompanyBranch() || search.getPolicyDto().getFinCompanyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getFinCompanyBranch());
                list.add(search.getPolicyDto().getCustName());
                list.add(search.getPolicyDto().getCustNic());
                list.add(search.getIsfClaimNo());
                list.add(getMailSendUser());
                email.setParameterEmail(list);
                String subject = messageContentDetails.getSubject().concat("- Claim No :-").
                        concat(search.getClaimNo().toString());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

    private Email createNoObejctionEmail(Connection connection, String emailVal, Integer claimNo, Integer
            calSheetId, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        try {
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.NO_OBJECTION_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            List<ClaimCalculationSheetPayeeDto> pendingEmailList = claimCalculationSheetPayeeDao.getPendingEmailList(connection, calSheetId, false);

            StringBuilder sb = new StringBuilder();
            for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : pendingEmailList) {
                sb = sb.append(" <tr style=\"border: 1px solid black;\">");
                sb = sb.append(" <td style=\"border: 1px solid black;\">").append(commonUtilDao.findOne(connection, "claim_calculation_sheet_payee_name", "V_PAYEE_NAME", "N_CAL_SHEET_PAYEE_NAME_ID=" + claimCalculationSheetPayeeDto.getPayeeId())).append("</td>");
                String name;
                name = claimCalculationSheetPayeeDto.getPayeeDesc();
                switch (claimCalculationSheetPayeeDto.getPayeeId()) {

                    case 2:
                    case 3:
                    case 9:
                    case 10:
                        String policyChannelType = claimCalculationSheetMainDao.getPolicyChannelType(connection, claimNo);
                        PopupItemDto popupItemDto = dbRecordCommonFunction.getPopupItemDto(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_CODE", "V_COMPANY_NAME", "N_REF_NO=" + claimCalculationSheetPayeeDto.getPayeeDesc());
                        name = popupItemDto.getLabel();
                        break;
                }


                sb = sb.append(" <td style=\"border: 1px solid black;\">").append(name).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\" align=\"right\">").append(claimCalculationSheetPayeeDto.getAmount()).append("</td></tr>");

            }
            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();
                if (null != search.getVehicleNo() && !search.getVehicleNo().isEmpty()) {
                    list.add(search.getVehicleNo());
                } else {
                    list.add(search.getCoverNoteNo());
                }

                list.add(search.getAccidDate());
                // list.add(getTotalPaidForClaim(connection, claimNo).toString());
                list.add(claimCalculationSheetMainDto.getPayableAmount().toString());
                list.add(commonUtilDao.findOne(connection, "claim_calculation_sheet_type", "V_CAL_SHEET_TYPE_DESC", "N_CAL_SHEET_TYPE_ID=" + claimCalculationSheetMainDto.getCalSheetType()));
                list.add(null == search.getPolicyDto().getPolicyBranch() || search.getPolicyDto().getPolicyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getPolicyBranch());
                list.add(null == search.getPolicyDto().getFinanceCompany() || search.getPolicyDto().getFinanceCompany().isEmpty() ? "N/A" : search.getPolicyDto().getFinanceCompany());
                list.add(null == search.getPolicyDto().getFinCompanyBranch() || search.getPolicyDto().getFinCompanyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getFinCompanyBranch());
                list.add(search.getPolicyDto().getPolicyNumber());
                list.add(search.getPolicyDto().getCustName());
                list.add(null == search.getPolicyDto().getCustNic() ? "N/A" : search.getPolicyDto().getCustNic());
                list.add(search.getIsfClaimNo());
                list.add(sb.toString());
                list.add(getMailSendUser());
                email.setParameterEmail(list);
                String subject = "Vehicle No :-".concat(search.getVehicleNo()).concat(",Claim No :-").
                        concat(search.getClaimNo().toString()).concat(",Date Of Accident :-").concat(search.getAccidDate()).concat("/").concat(messageContentDetails.getSubject());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

    private Email createNcbEmail(Connection connection, String emailVal, Integer claimNo, Integer calSheetId, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) throws Exception {
        try {
            StringBuilder stringBuilder = new StringBuilder();
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.NCB_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            List<ClaimCalculationSheetPayeeDto> calculationSheetPayeeDtos = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, calSheetId);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);

            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();

                String paymentType = commonUtilDao.findOne(connection, "claim_calculation_sheet_type", "V_CAL_SHEET_TYPE_DESC", "N_CAL_SHEET_TYPE_ID=" + claimCalculationSheetMainDto.getCalSheetType());
                UserDto userByUsrid = userDao.getUserByUsrid(connection, claimCalculationSheetMainDto.getAssignUserId());

                list.add(search.getVehicleNo());
                list.add(search.getPolicyNumber());
                list.add(search.getPolicyDto().getCustName());
                list.add(search.getAccidDate());
                list.add(search.getPolicyDto().getInspecDate() + " - " + search.getPolicyDto().getExpireDate());
                list.add(paymentType);
                String comma = "";
                for (ClaimCalculationSheetPayeeDto payeeDto : calculationSheetPayeeDtos) {
                    stringBuilder.append(comma);
                    stringBuilder.append(payeeDto.getPayeeDesc());
                    comma = ",";
                }
                list.add(stringBuilder.toString());
                list.add(null == search.getPolicyDto().getPolicySellingAgentDetailsDto().getAgentName() || search.getPolicyDto().getPolicySellingAgentDetailsDto().getAgentName().isEmpty() ? "N/A" : search.getPolicyDto().getPolicySellingAgentDetailsDto().getAgentName());
                list.add(null == search.getPolicyDto().getPolicyBranch() || search.getPolicyDto().getPolicyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getPolicyBranch());
                list.add(null == search.getPolicyDto().getFinanceCompany() || search.getPolicyDto().getFinanceCompany().isEmpty() ? "N/A" : search.getPolicyDto().getFinanceCompany());
                list.add(null == search.getPolicyDto().getFinCompanyBranch() || search.getPolicyDto().getFinCompanyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getFinCompanyBranch());
                list.add("N/A");
                list.add(claimHandlerDto.getAprvTotAcrAmount().toString());
                list.add(claimCalculationSheetMainDto.getPayableAmount().toString());
                String contact = null != userByUsrid.getLandPhone() && !userByUsrid.getLandPhone().isEmpty() ? userByUsrid.getLandPhone() : "N/A";
                list.add(userByUsrid.getFirstName() + " " + userByUsrid.getLastName() + " - " + (null != userByUsrid.getMobile() && !userByUsrid.getMobile().isEmpty() ? userByUsrid.getMobile() : contact));
                list.add(search.getPolicyDto().getCustNic());
                list.add(search.getIsfClaimNo());
                list.add(getMailSendUser());

                email.setParameterEmail(list);
                String subject = "Vehicle No :-".concat(search.getVehicleNo()).concat(",Claim No :-").
                        concat(search.getClaimNo().toString()).concat(",Date Of Accident :-").concat(search.getAccidDate())
                        .concat("/").concat(messageContentDetails.getSubject());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

    private void saveClaimOfflinePayemnt(Connection connection, Integer calSheetId, Integer claimNo) throws
            Exception {
        try {
            List<ClaimCalculationSheetPayeeDto> list = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, calSheetId);
            ClaimsDto claimsDto = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
//            PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
            for (ClaimCalculationSheetPayeeDto calculationSheetPayeeDto : list) {
                String payeeType = AppConstant.STRING_EMPTY;
                McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto = new McmsClaimOfflinePaymentDto();
                mcmsClaimOfflinePaymentDto.setReferenceId(calculationSheetPayeeDto.getCalSheetPayeeId());
                mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(null == calculationSheetPayeeDto.getBankName() ? AppConstant.STRING_EMPTY : calculationSheetPayeeDto.getBankName());
                mcmsClaimOfflinePaymentDto.setOvInstitutionCode(null == calculationSheetPayeeDto.getBankCode() ? AppConstant.STRING_EMPTY : calculationSheetPayeeDto.getBankCode());
                mcmsClaimOfflinePaymentDto.setOvIdentificationNo(AppConstant.STRING_EMPTY);
                mcmsClaimOfflinePaymentDto.setOvIdentificationCode(AppConstant.STRING_EMPTY);
                String policyChannelType = claimCalculationSheetMainDao.getPolicyChannelType(connection, claimNo);
                switch (calculationSheetPayeeDto.getPayeeId()) {
                    case 1:
                        payeeType = AppConstant.INSURED;

                        if (null != claimsDto && null != claimsDto.getPolicyDto() && claimsDto.getPolicyDto().getIndComFlag().equals("I")) {
                            mcmsClaimOfflinePaymentDto.setOvIdentificationNo(null != claimsDto ? claimsDto.getPolicyDto().getCustNic() : AppConstant.STRING_EMPTY);
                            mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                            mcmsClaimOfflinePaymentDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
//                            mcmsClaimOfflinePaymentDto.setOvIdentificationCode(AppConstant.VOUCER_GENERATE_CODE);
                        } else {

                            mcmsClaimOfflinePaymentDto.setOvIdentificationNo(AppConstant.STRING_EMPTY);
//                            mcmsClaimOfflinePaymentDto.setOvIdentificationCode(AppConstant.STRING_EMPTY);
                            mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(claimsDto.getPolicyDto().getCompanyBranch());
                            mcmsClaimOfflinePaymentDto.setOvInstitutionCode(claimsDto.getPolicyDto().getCompanyCode());

                        }
                        if (null != claimsDto && null != claimsDto.getPolicyDto()
                                && null != claimsDto.getPolicyDto().getIdenCode()
                                && !AppConstant.STRING_EMPTY.equalsIgnoreCase(claimsDto.getPolicyDto().getIdenCode())) {
                            mcmsClaimOfflinePaymentDto.setOvIdentificationCode(
                                    claimsDto.getPolicyDto().getIdenCode().equals(AppConstant.IDENTIFICATION_CODE) &&
                                            null != claimsDto.getPolicyDto().getCompanyCode() &&
                                            !claimsDto.getPolicyDto().getCompanyCode().isEmpty() ?
                                            AppConstant.STRING_EMPTY : claimsDto.getPolicyDto().getIdenCode());
                        }

                        break;
                    case 2:
                    case 9:
                        payeeType = AppConstant.GARAGE;
                        PopupItemDto popupItemDto = dbRecordCommonFunction.getPopupItemDto(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_CODE", "V_COMPANY_BRANCH", "N_REF_NO=" + calculationSheetPayeeDto.getPayeeDesc());
                        if (null != popupItemDto) {
                            mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(popupItemDto.getLabel());
                            mcmsClaimOfflinePaymentDto.setOvInstitutionCode(popupItemDto.getValue());
                        }
                        break;
                    case 3:
                        payeeType = AppConstant.LEASING_COMPANY;
                        popupItemDto = dbRecordCommonFunction.getPopupItemDto(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_CODE", "V_COMPANY_BRANCH", "N_REF_NO=" + calculationSheetPayeeDto.getPayeeDesc());
                        if (null != popupItemDto) {
                            mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(popupItemDto.getLabel());
                            mcmsClaimOfflinePaymentDto.setOvInstitutionCode(popupItemDto.getValue());
                        }
                        break;
                    case 4:
                        payeeType = AppConstant.NCB_REVERSAL;
                        mcmsClaimOfflinePaymentDto.setOvInstitutionBranch("HEAD OFFICE");
                        mcmsClaimOfflinePaymentDto.setOvInstitutionCode("LOLCIC");
                        break;
                    case 5:
                        payeeType = AppConstant.INSURANCE_COMPANY;
                        mcmsClaimOfflinePaymentDto.setOvInstitutionBranch("HEAD OFFICE");
                        mcmsClaimOfflinePaymentDto.setOvInstitutionCode("LOLCIC");
                        break;
                    case 6:
                        payeeType = AppConstant.GOVERNMENT_AUTHORITIES;
                        break;
                    case 7:
                        payeeType = AppConstant.THIRD_PARTY_COORPORATE;
                        break;
                    case 10:
                        payeeType = AppConstant.PAYEE_OTHER;
                        popupItemDto = dbRecordCommonFunction.getPopupItemDto(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_CODE", "V_COMPANY_BRANCH", "N_REF_NO=" + calculationSheetPayeeDto.getPayeeDesc());
                        if (null != popupItemDto) {
                            mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(popupItemDto.getLabel());
                            mcmsClaimOfflinePaymentDto.setOvInstitutionCode(popupItemDto.getValue());
                        }
                        break;
                }
                mcmsClaimOfflinePaymentDto.setOvPayeeType(payeeType);
                mcmsClaimOfflinePaymentDto.setOvClaimNo(null != claimsDto ? claimsDto.getIsfClaimNo() : AppConstant.STRING_EMPTY);
                mcmsClaimOfflinePaymentDto.setOnPaidAmount(calculationSheetPayeeDto.getAmount());
                mcmsClaimOfflinePaymentDto.setOnTotalPayable(calculationSheetPayeeDto.getAmount());
                mcmsClaimOfflinePaymentDto.setOvVoucherFlag(AppConstant.VOUCHER_FLAG);
                mcmsClaimOfflinePaymentDto.setNRetryAttempt(AppConstant.ZERO_INT);
                mcmsClaimOfflinePaymentDto.setdIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflinePaymentDto.setVIsfsUpdateStat(AppConstant.NO);
                mcmsClaimOfflinePaymentDto.setdInsertDateTime(Utility.sysDateTime());
                mcmsClaimOfflinePaymentDto.setClaimNo(claimNo);
                mcmsClaimOfflinePaymentDto.setPolicyChannelType(claimsDto.getPolicyChannelType());
                claimOfflinePaymentDao.insertMaster(connection, mcmsClaimOfflinePaymentDto);


            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    private void offlineReserveClaim(Connection connection, Integer claimNo, BigDecimal reserveAmount, Integer lossType, UserDto user) throws Exception {
        try {
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, claimNo);
//            PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
            McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto = new McmsClaimOfflineReserveClaimDto();
            String lossCode = commonUtilDao.findOne(connection, "claim_loss_type", "V_LOSS_CODE", "N_ID=" + lossType);
            if (null != claimsDto) {
                mcmsClaimOfflineReserveClaimDto.setOvClaimNo(claimsDto.getIsfClaimNo());
                mcmsClaimOfflineReserveClaimDto.setOnBillAmount(reserveAmount);
                mcmsClaimOfflineReserveClaimDto.setOnAllowedAmount(reserveAmount);
                mcmsClaimOfflineReserveClaimDto.setOnDepPer(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOnPaEstimateAmount(reserveAmount);
                mcmsClaimOfflineReserveClaimDto.setOvReportType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationNo(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAppointment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvPanelCategory(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvPanelType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvLossType(null != lossCode ? lossCode : AppConstant.LOSS_TYPE);
                mcmsClaimOfflineReserveClaimDto.setCvRequired(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvRiskNo(Integer.toString(1));
                mcmsClaimOfflineReserveClaimDto.setDInsertDateTime(Utility.sysDateTime());
                mcmsClaimOfflineReserveClaimDto.setNRetryAttempt(AppConstant.ZERO_INT);
                mcmsClaimOfflineReserveClaimDto.setVIsfsUpdateStat(AppConstant.NO);
                mcmsClaimOfflineReserveClaimDto.setDIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssesSub(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setClaimNo(claimNo);
                mcmsClaimOfflineReserveClaimDto.setPolicyChannelType(claimsDto.getPolicyChannelType());
                offlineReserveClaimDao.insertMaster(connection, mcmsClaimOfflineReserveClaimDto);
                saveClaimsLogs(connection, claimNo, user, "Claim Reserve Changed", "Total Reserve Amount : " + reserveAmount);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }


    }

    private void saveCalculationProcessFlow(Connection connection, Integer claimNo, Integer calSheetId, Integer
            calSheetStatus, String task, String userId, String assignUserId) throws Exception {
        try {
            CalculationProcessFlowDto calculationProcessFlowDto = new CalculationProcessFlowDto();
            calculationProcessFlowDto.setClaimNo(claimNo);
            calculationProcessFlowDto.setCalSheetId(calSheetId);
            calculationProcessFlowDto.setCalSheetStatus(calSheetStatus);
            calculationProcessFlowDto.setInpUserId(userId);
            calculationProcessFlowDto.setInpDateTime(Utility.sysDateTime());
            calculationProcessFlowDto.setTask(task);
            calculationProcessFlowDto.setAssignUserId(assignUserId);
            calculationProcessFlowDao.insert(connection, calculationProcessFlowDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private ClaimDocumentStatusDto getClaimDocumentStatusDto(Connection conn, Integer claimNo, String constType) throws
            Exception {
        String[] typeArr = constType.split(",");
        ClaimDocumentStatusDto claimDocumentStatusDto = null;
        for (String type : typeArr) {
            if (null == claimDocumentStatusDto) {
                claimDocumentStatusDto = this.getClaimDocumentStatus(conn, claimNo, Integer.parseInt(type));
            } else {
                claimDocumentStatusDto.getClaimDocumentDtoList()
                        .addAll(this.getClaimDocumentStatus(conn, claimNo, Integer.parseInt(type)).getClaimDocumentDtoList());
            }
        }
        return claimDocumentStatusDto;
    }

    private String getSelectList(Connection connection, String tblName, String valueField, String
            lableField, String searchKey) {
        StringBuilder sb = new StringBuilder();

        PreparedStatement ps;
        ResultSet rs;
        String strSQL;
        try {
            strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName).concat(" ")
                    .concat(searchKey).concat(" ORDER BY ").concat(lableField);
            ps = connection.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                sb.append("<option value=").append("'");
                sb.append(rs.getString(valueField)).append("'");
                sb.append(">");
                sb.append(rs.getString(lableField));
                sb.append("</option>");
            }
            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return sb.toString();
    }

    private List<ListBoxItem> getSelectListBox(Connection connection, String tblName, String valueField, String
            lableField, String searchKey, String orderField) {
        List<ListBoxItem> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        String strSQL;
        try {
            strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName).concat(" ")
                    .concat(searchKey).concat(" ORDER BY ").concat(orderField.isEmpty() ? lableField : orderField);
            ps = connection.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                list.add(new ListBoxItem(rs.getString(valueField), rs.getString(lableField)));
            }
            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    private String getSelectList(Connection connection, String tblName, String valueField, String
            lableField, String searchKey, String orderField) {
        StringBuilder sb = new StringBuilder();

        PreparedStatement ps;
        ResultSet rs;
        String strSQL;
        try {
            strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName).concat(" ")
                    .concat(searchKey).concat(" ORDER BY ").concat(orderField);
            ps = connection.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                sb.append("<option value=").append("'");
                sb.append(rs.getString(valueField)).append("'");
                sb.append(">");
                sb.append(rs.getString(lableField));
                sb.append("</option>");
            }
            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return sb.toString();
    }

    private void setSelectBoxItem(Connection connection, ClaimCalculationSheetMainDto
            claimCalculationSheetMainDto, Integer claimNo) {

        claimCalculationSheetMainDto.setSupplyOrderSelectItem(this.getSelectList(connection, "claim_supply_order_summary",
                "n_supply_order_ref_no", "v_supply_order_serial_no",
                "WHERE n_claim_no=".concat(String.valueOf(claimNo)).concat(" AND (v_supply_order_status IN('SCRUTINIZING-A','G') OR v_is_updated='Y')")));
        Integer status = claimCalculationSheetMainDto.getStatus() == null ? 0 : claimCalculationSheetMainDto.getStatus();
        switch (status) {
            case 63:
            case 64:
            case 65:
            case 67:
            case 70:
                claimCalculationSheetMainDto.setLossTypeSelectItem(this.getSelectList(connection, "claim_loss_type",
                        "N_ID", "V_CAUSE_OF_LOSS",
                        " WHERE N_ID=" + claimCalculationSheetMainDto.getLossType()));
                claimCalculationSheetMainDto.setCauseOfLossSelectItem(this.getSelectList(connection, "claim_cause_of_loss_type",
                        "N_ID", "V_CAUSE_OF_LOSS",
                        " WHERE N_ID=" + claimCalculationSheetMainDto.getCauseOfLoss()));
                claimCalculationSheetMainDto.setPaymentTypeSelectItem(this.getSelectList(connection, "claim_payment_type",
                        "N_ID", "V_PAYMENT_TYPE_DESC",
                        " WHERE N_ID=" + claimCalculationSheetMainDto.getPaymentType()));
                claimCalculationSheetMainDto.setPayeeSelectItem(this.getSelectList(connection, "claim_calculation_sheet_payee_name",
                        "N_CAL_SHEET_PAYEE_NAME_ID", "V_PAYEE_NAME",
                        " WHERE V_STATUS = 'A' AND N_CAL_SHEET_PAYEE_NAME_ID=" + claimCalculationSheetMainDto.getCalSheetType(), "N_CAL_SHEET_PAYEE_NAME_ID"));

                break;
            default:
                claimCalculationSheetMainDto.setLossTypeSelectItem(this.getSelectList(connection, "claim_loss_type",
                        "N_ID", "V_CAUSE_OF_LOSS",
                        AppConstant.EMPTY_STRING));
                claimCalculationSheetMainDto.setCauseOfLossSelectItem(this.getSelectList(connection, "claim_cause_of_loss_type",
                        "N_ID", "V_CAUSE_OF_LOSS",
                        AppConstant.EMPTY_STRING));
                claimCalculationSheetMainDto.setPaymentTypeSelectItem(this.getSelectList(connection, "claim_payment_type",
                        "N_ID", "V_PAYMENT_TYPE_DESC",
                        AppConstant.EMPTY_STRING));
                claimCalculationSheetMainDto.setPayeeSelectItem(this.getSelectList(connection, "claim_calculation_sheet_payee_name",
                        "N_CAL_SHEET_PAYEE_NAME_ID", "V_PAYEE_NAME",
                        " WHERE V_STATUS = 'A'", "N_CAL_SHEET_PAYEE_NAME_ID"));
        }

        claimCalculationSheetMainDto.setOaRateSelectItem(this.getSelectList(connection, "claim_oa_rate",
                "n_oa_rate", "n_oa_rate",
                AppConstant.EMPTY_STRING));

        claimCalculationSheetMainDto.setNbtRateSelectItem(this.getSelectList(connection, "claim_nbt_rate",
                "n_nbt_rate", "n_nbt_rate",
                AppConstant.EMPTY_STRING));

        claimCalculationSheetMainDto.setVatRateSelectItem(this.getSelectList(connection, "claim_vat_rate",
                "n_vat_rate", "n_vat_rate",
                AppConstant.EMPTY_STRING));


    }

    @Override
    public List<ListBoxItem> getPayeeSelectItem(int payeeType, Integer supplyOrderRefNo, String customerName, boolean isReadonly, String selectPayeeDesc, Integer claimNo) {
        Connection connection = null;
        List<ListBoxItem> listBoxItems = new ArrayList<>();
        String SEARCH_SQL;
        try {
            connection = getJDBCConnection();
            String policyChannelType = supplyOrderSummaryDao.getPolicyChannelType(connection, supplyOrderRefNo, claimNo);
            switch (payeeType) {
                case 1://Insured
                    listBoxItems.add(new ListBoxItem(customerName, customerName));
                    break;
                case 2://Garage
                    SEARCH_SQL = " WHERE V_INST_TYPE='WS'";
                    if (isReadonly || !selectPayeeDesc.isEmpty()) {
                        SEARCH_SQL = " WHERE V_INST_TYPE='WS' AND N_REF_NO='" + selectPayeeDesc + "'";
                    }
                    listBoxItems = this.getSelectListBox(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main",
                            "N_REF_NO", "V_COMPANY_NAME",
                            SEARCH_SQL, AppConstant.EMPTY_STRING);
                    if (!isReadonly && selectPayeeDesc.isEmpty()) {
                        listBoxItems.add(0, new ListBoxItem("0", "Please Select One"));
                    }

                    break;
                case 3://Leasing Company
                    SEARCH_SQL = " WHERE V_INST_TYPE='02'";
                    if (isReadonly || !selectPayeeDesc.isEmpty()) {
                        SEARCH_SQL = " WHERE V_INST_TYPE='02' AND N_REF_NO='" + selectPayeeDesc + "'";
                    }
                    listBoxItems = this.getSelectListBox(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main",
                            "N_REF_NO", "V_COMPANY_NAME",
                            SEARCH_SQL, AppConstant.EMPTY_STRING);
                    if (!isReadonly && selectPayeeDesc.isEmpty()) {
                        listBoxItems.add(0, new ListBoxItem("0", "Please Select One"));
                    }

                    break;
                case 4://NCB Reversal
                    listBoxItems.add(new ListBoxItem(LOLC_NAME, LOLC_NAME));
                    break;
                case 5://Insurance Company
                    listBoxItems.add(new ListBoxItem(LOLC_NAME, LOLC_NAME));
                    break;
                case 6://Government Authorities
                    break;
                case 7://Third Party Individual
                    break;
                case 8://Third Party Coorporate
                    break;
                case 9://Supplier
                    SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchBysupplyOrderRefNo(connection, supplyOrderRefNo);
                    listBoxItems.add(new ListBoxItem(supplyOrderSummaryDto.getSupplierId(),
                            supplyOrderSummaryDto.getSupplierName()
                            , Utility.formatCurrency(supplyOrderSummaryDto.getTotalAmount())
                            , Utility.formatCurrency(supplyOrderSummaryDto.getTotalOwnersAccountAmount())
                            , Utility.formatCurrency(supplyOrderSummaryDto.getOthertDeductionAmount())
                            , Utility.formatCurrency(supplyOrderSummaryDto.getPolicyExcess())
                            , Utility.formatCurrency(supplyOrderSummaryDto.getFinalAmount())
                            , Utility.formatCurrency(supplyOrderSummaryDto.getVatAmount())
                            , supplyOrderSummaryDto.getVatStatus()));
                    break;
                case 10://Other
                    SEARCH_SQL = " WHERE V_INST_TYPE NOT IN('02','WS','POL','04','24','20','EVENT','SUC','Concrete','16')";
                    if (isReadonly || !selectPayeeDesc.isEmpty()) {
                        SEARCH_SQL = " WHERE V_INST_TYPE NOT IN('02','WS','POL','04','24','20','EVENT','SUC','Concrete','16') AND N_REF_NO='" + selectPayeeDesc + "'";
                    }
                    listBoxItems = this.getSelectListBox(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main",
                            "N_REF_NO", "V_COMPANY_NAME",
                            SEARCH_SQL, AppConstant.EMPTY_STRING);
                    if (!isReadonly && selectPayeeDesc.isEmpty()) {
                        listBoxItems.add(0, new ListBoxItem("0", "Please Select One"));
                    }
                    break;

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return listBoxItems;
    }

    @Override
    public List<ListBoxItem> getPayeeTypeSelectItem(int calculationType, boolean isReadonly, String
            selectPayeeType) {
        String SEARCH_SQL;
        Connection connection = null;
        List<ListBoxItem> listBoxItems = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            switch (calculationType) {
                case 3://DO
                    listBoxItems = this.getSelectListBox(connection, "claim_calculation_sheet_payee_name",
                            "N_CAL_SHEET_PAYEE_NAME_ID", "V_PAYEE_NAME",
                            " WHERE V_STATUS = 'A'  AND N_CAL_SHEET_PAYEE_NAME_ID=9 ", "N_CAL_SHEET_PAYEE_NAME_ID");
                    break;
                default:
                    SEARCH_SQL = " WHERE V_STATUS = 'A'";
                    if (isReadonly) {
                        SEARCH_SQL = " WHERE V_STATUS = 'A' AND N_CAL_SHEET_PAYEE_NAME_ID='" + selectPayeeType + "'";
                    }
                    listBoxItems = this.getSelectListBox(connection, "claim_calculation_sheet_payee_name",
                            "N_CAL_SHEET_PAYEE_NAME_ID", "V_PAYEE_NAME",
                            SEARCH_SQL, "N_CAL_SHEET_PAYEE_NAME_ID");

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return listBoxItems;
    }

    @Override
    public boolean saveClaimSpecialRemarkByCalsheetId(Integer calsheetId, String remark, UserDto user) throws
            Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            Integer claimNo = claimCalculationSheetMainDao.getClaimNoByCalSheetId(connection, calsheetId);
            saveClaimSpecialRemark(connection, user, claimNo, AppConstant.CALSHEET_SPECIAL_REMARK, remark);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return true;
    }

    @Override
    public String isExcessAlreadyApply(Integer claimNo, Integer calSheetId) throws Exception {
        Connection connection = null;
        String isExcessAlreadyApplyStr = AppConstant.YES;
        try {
            connection = getJDBCConnection();
            boolean isPending = claimCalculationSheetMainDao.isPendingCalsheet(connection, calSheetId);
            boolean isExcessAlreadyApply = claimCalculationSheetMainDao.isExcessAlreadyApply(connection, claimNo, calSheetId);

            if (isPending && !isExcessAlreadyApply) {
                isExcessAlreadyApplyStr = AppConstant.NO;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isExcessAlreadyApplyStr;
    }

    @Override
    public void recallCalsheetforwardedtoSpTeamByClaimHandler(Integer calSheetId, Integer claimNo, UserDto user) throws
            Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 58);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Recall By Claim Handler.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            if (AppConstant.CAL_SHEET_TYPE_DO.equals(searchMaster.getCalSheetType())) {
                Integer updatedDo = supplyOrderSummaryDao.isUpdatedDo(connection, calSheetId);
                if (updatedDo > AppConstant.ZERO_INT) {
                    notificationMsg = "DO amended. Please proceed DO again";
                    URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER)).concat("&N_REF_NO=").concat(String.valueOf(updatedDo));
                    supplyOrderSummaryDao.updateDOUpdatedStatus(connection, calSheetId, AppConstant.NO);
                }
            }
            saveNotification(connection, claimNo, user.getUserId(), searchMaster.getSpecialTeamAssignUserId(), notificationMsg, URL);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Recalled", "Recalled the Cal Sheet forwarded to Special Team By " + user.getUserId());
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 58, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Recalled the Cal Sheet forwarded to Special Team By \"Claim Handler\"", user.getUserId(), user.getUserId());
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ClaimCalculationSheetMainTempDto getCalsheetTempDetails(Integer calSheetId) throws Exception {
        Connection connection = null;
        ClaimCalculationSheetMainTempDto tempDto = null;
        try {
            connection = getJDBCConnection();
            tempDto = claimCalculationSheetMainTempDao.searchByCalsheetId(connection, calSheetId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return tempDto;
    }

    @Override
    public void recommendAndForwardToNextLevel(Integer calSheetId, Integer claimNo, UserDto user, BigDecimal outStandingPremium, boolean isCancelledPolicy) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        String notificationMsg;
        Connection connection = null;
        CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto;
        String forwardUserAndLevel;
        try {

            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            checkExcessReserveAmount(connection, claimNo, searchMaster.getCalSheetType());
            validatePayeeDetails(connection, searchMaster);
            saveConfirmationLogs(connection, claimNo, user, outStandingPremium, isCancelledPolicy);
            calculationSheetAssignMofaLevelDetailDto = getNextAssignMofaDetails(connection, claimNo, calSheetId, searchMaster.getCalSheetType(), user);
            forwardUserAndLevel = getForwardUserAndLevel(calculationSheetAssignMofaLevelDetailDto);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Recommended & Forwarded By ".concat(forwardUserAndLevel).concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, 64);
            claimCalculationSheetMainDao.updateMofaTeamAssignUser(connection, calSheetId, calculationSheetAssignMofaLevelDetailDto.getAssignUserId(), Utility.sysDateTime());
            saveNotification(connection, claimNo, user.getUserId(), calculationSheetAssignMofaLevelDetailDto.getAssignUserId(), notificationMsg, URL);
            sendEmailForAssignMofaUser(connection, claimNo, calculationSheetAssignMofaLevelDetailDto.getAssignUserId(), searchMaster.getPayableAmount(), URL);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 64, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet has been forwarded to the \"MOFA Team\"".concat("( level-" + calculationSheetAssignMofaLevelDetailDto.getAssignMofaLevel() + " )"), user.getUserId(), calculationSheetAssignMofaLevelDetailDto.getAssignUserId());
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Forwarded ", notificationMsg.concat(" [" + calculationSheetAssignMofaLevelDetailDto.getAssignUserId().concat(" ( level-" + calculationSheetAssignMofaLevelDetailDto.getAssignMofaLevel() + " )") + "]"));
            shiftNotificationOnAction(connection, claimNo, user);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void sendEmailForAssignMofaUser(Connection connection, Integer claimNo, String assignUserId, BigDecimal payableAmount, String url) throws Exception {
        try {
            if (userDao.isNeedToSendEmailByUserId(connection, assignUserId)) {
                Email email = new Email();

                DecimalFormat decimalFormat = new DecimalFormat("#,###.00");

                MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.MOFA_USER_EMAIL);
                email.setEmailMassege(messageContentDetails.getMessageBody());
                String vehicleNo = callCenterDao.getVehicleNoByClaimNo(connection, claimNo);
                String emailAddress = userDao.getEmailByUserId(connection, assignUserId);
                email.setToAddresses(emailAddress);
                ArrayList<String> list = new ArrayList<>();
//                String appUrl = Parameters.getAppUrl().substring(0, Parameters.getAppUrl().length() - 1);
                String appUrl = getAppUrl().concat("?url=ClaimHandlerController/viewEdit").concat("?P_N_CLIM_NO=").concat(claimNo.toString()).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
                String internalAppUrl = getInternalAppUrl().concat("?url=ClaimHandlerController/viewEdit").concat("?P_N_CLIM_NO=").concat(claimNo.toString()).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
                list.add(decimalFormat.format(payableAmount));
                list.add(appUrl);
                list.add(internalAppUrl);
                email.setParameterEmail(list);
                String subject = "Vehicle No :- ".concat(vehicleNo).concat(" / Claim No :- ").
                        concat(claimNo.toString()).concat(" - ").concat(messageContentDetails.getSubject());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void approveIncreaseReserveByRte(Integer claimNo, Integer calSheetId, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);

            validatePayeeDetails(connection, searchMaster);

            claimHandlerDao.updateEngineerApprovedAmount(connection, claimNo, searchMaster.getPayableAmount());

            String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

            String userId = (null == searchMaster.getSpecialTeamAssignUserId()) ? AppConstant.STRING_EMPTY : searchMaster.getSpecialTeamAssignUserId();
            boolean isProvideOffer = claimHandlerDao.isProvideOffer(connection, claimNo);
            if (userId.isEmpty()) {
                if (isProvideOffer) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                } else {
                    userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }
            } else {
                if (claimUserAllocationDao.checkIfLeave(connection, isProvideOffer ? AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM : AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), userId)) {
                    userId = claimUserAllocationService.getNextAssignUser(connection, isProvideOffer ? AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM : AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                }
            }
            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, ClaimStatus.FORWARDED_TO_THE_PAYMENT_APPROVAL_SPECIAL_TEAM_FOR_PAYMENT_APPROVAL.getClaimStatus());
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());

            String notificationMsg = "You have received " + claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " cal sheet forwarded by RTE with recommendation".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
            if ("C".equals(searchMaster.getNoObjectionStatus())) {
                claimCalculationSheetMainDao.updateNoObjectionStatus(connection, calSheetId, "A");
            }
            if ("C".equals(searchMaster.getPremiumOutstandingStatus())) {
                claimCalculationSheetMainDao.updatePremiumOutstandingStatus(connection, calSheetId, "A");
            }

            claimCalculationSheetMainDao.updateSpecialTeamAssignUser(connection, calSheetId, userId, Utility.sysDateTime());
            claimCalculationSheetMainDao.updateRteAction(connection, calSheetId, AppConstant.APPROVE);
            saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL, NotificationPriority.HIGH);
            saveCalculationSheetAssignMofaLevel(connection, calSheetId, claimNo, user, userId, AppConstant.LEVEL_ONE);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet [" + calSheetId + "] Forwarded", "Cal sheet with recommendation has been forwarded to special team user [" + userId + "] for approval");
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 63, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Cal sheet [" + calSheetId + "] with recommendation has been forwarded to special team user [" + userId + "] for approval", user.getUserId(), userId);

            saveClaimSpecialRemark(connection, user, claimNo, "Cal Sheet recommended by RTE", remark);
            shiftNotificationOnAction(connection, claimNo, user);

            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToClaimHandlerByRte(Integer claimNo, Integer calSheetId, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));

            String assignUserId = claimCalculationSheetMainDao.getAssignUserId(connection, calSheetId);

            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.getCalsheetTypeDetailByCalSheetId(connection, calSheetId);

            claimCalculationSheetMainDao.updateStatus(connection, calSheetId, ClaimStatus.CALCULATION_VERIFY_PENDING.getClaimStatus());

            claimCalculationSheetMainDao.updateRteAction(connection, calSheetId, AppConstant.REJECT);
            saveNotification(connection, claimNo, user.getUserId(), assignUserId, "You have received a cal sheet returned by RTE".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId))), URL);
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, ClaimStatus.CALCULATION_VERIFY_PENDING.getClaimStatus(), claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Cal sheet [" + calSheetId + "] has been returned to claim handler [" + assignUserId + "] by RTE [" + user.getUserId() + "]", user.getUserId(), assignUserId);
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Cal Sheet [" + calSheetId + "] returned by RTE [" + assignUserId + "]", "Cal sheet has been returned to claim handler [" + assignUserId + "] by RTE [" + user.getUserId() + "]");

            saveClaimSpecialRemark(connection, user, claimNo, "Cal Sheet returned by RTE", remark);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<CalculationSheetPayeeHistoryDto> getPayeeHistoryDetails(Integer calSheetId, Integer claimNo) {
        List<CalculationSheetPayeeHistoryDto> calculationSheetPayeeHistoryDtoList = new ArrayList<>();

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            calculationSheetPayeeHistoryDtoList = claimCalculationSheetPayeeDao.getPayeeListByCalsheetId(connection, calSheetId);

            if (null != calculationSheetPayeeHistoryDtoList && !calculationSheetPayeeHistoryDtoList.isEmpty()) {
                for (CalculationSheetPayeeHistoryDto dto : calculationSheetPayeeHistoryDtoList) {
                    dto.setPayeeName(getPayeeName(connection, dto, claimNo));
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return calculationSheetPayeeHistoryDtoList;
    }

    @Override
    public List<CalculationSheetMofaLevelHistoryDto> getMofaHistoryDetails(Integer calSheetId) {

        List<CalculationSheetMofaLevelHistoryDto> calculationSheetMofaLevelHistoryDtoList = new ArrayList<>();

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            calculationSheetMofaLevelHistoryDtoList = calculationSheetAssignMofaLevelDetailDao.getApprovedMofaListByCalsheetId(connection, calSheetId);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return calculationSheetMofaLevelHistoryDtoList;


    }

    @Override
    public void forwardToRte(Integer calSheetId, Integer claimNo, BigDecimal payableAmount, UserDto user, BigDecimal outStandingPremium) throws Exception {
        Connection connection = null;
        try {
            String notificationUrl = AppConstant.MOTORENG_CAL_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&PCAL_SHEET_NO=").concat(String.valueOf(calSheetId)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
            String notificationMsg = "You have received a cal sheet from " + user.getUserId() + " for recommendation Cal Sheet No - " + calSheetId;
            connection = getJDBCConnection();
            String action = claimCalculationSheetMainDao.getRteAction(connection, calSheetId);

            String rteAssignUserId = AppConstant.STRING_EMPTY;
            if (null != action && action.equals(AppConstant.REJECT)) {
                rteAssignUserId = claimCalculationSheetMainDao.getAssignRte(connection, calSheetId);
            }

            if (null == rteAssignUserId || rteAssignUserId.isEmpty()) {
                BigDecimal amount = this.getTotalPaidAmount(claimNo, calSheetId).add(payableAmount);
                UserAuthorityLimitDto userAuthorityLimitDto =
                        userAuthorityLimitDao.searchByLimitAndDepartmentId(connection, amount, AppConstant.AUTORITY_LIMIT_RTE);
                if (null != userAuthorityLimitDto) {
                    rteAssignUserId =
                            claimUserAllocationService.getNextAuthLimitAssignUser(connection, AppConstant.ACCESS_LEVEL_RTE,
                                    AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(),
                                    claimNo, true, amount, userAuthorityLimitDto.getToLimit(),
                                    userAuthorityLimitDto.getLevelCode(), false);
                }
            }
            if (null != rteAssignUserId && !AppConstant.STRING_EMPTY.equalsIgnoreCase(rteAssignUserId)) {
                claimCalculationSheetMainDao.updateMotorEngineer(connection, calSheetId, rteAssignUserId, Utility.sysDateTime());
                if (outStandingPremium.compareTo(BigDecimal.ZERO) > 0) {
                    saveClaimsLogs(connection, claimNo, user, "Outstanding Premium Confirmation Alert", "Marked As Ok to \"This policy has Rs." + outStandingPremium + " of outstanding premium amount\" alert");
                }
                ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.getCalsheetTypeDetailByCalSheetId(connection, calSheetId);
                saveNotification(connection, claimNo, user.getUserId(), rteAssignUserId, notificationMsg, notificationUrl);
                saveClaimsLogs(connection, claimNo, user, "Forward To Motor Engineer", "Forwarded To Motor Engineer [" + rteAssignUserId + "] For Payment Approval");
                saveClaimProcessFlow(connection, claimNo, 0, "Calculation Sheet Forward For Engineering Approval", user.getUserId(), Utility.sysDateTime(), rteAssignUserId);
                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, ClaimStatus.FORWARDED_TO_THE_MOTOR_ENGINEERING_TEAM_FOR_RESERVE_AMOUNT_APPROVAL.getClaimStatus(), claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Cal sheet [" + calSheetId + "] has been forwarded to RTE [" + rteAssignUserId + "] by claim handler [" + user.getUserId() + "]", user.getUserId(), rteAssignUserId);
                shiftNotificationOnAction(connection, claimNo, user);
            } else {
                throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User not found to assign claim");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public BigDecimal getPaidTotalAdvanceAmount(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimCalculationSheetMainDao.getPaidTotalAdvanceAmount(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public BigDecimal getTotalPaidAmount(Integer claimNo, Integer calSheetId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimCalculationSheetMainDao.getTotalPayableAmount(connection, claimNo, calSheetId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<String> getAssignUserListByLevel(Integer claimNo, Integer level) {
        Connection connection = null;
        List<String> nextAuthLimitAssignUserList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            int accessUserType = getAccessUserType(connection, claimNo);

            for (; level <= AppConstant.LEVEL_NINE; level++) {
                UserAuthorityLimitDto userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, level, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
                nextAuthLimitAssignUserList = claimUserAllocationService.getNextAuthLimitAssignUserList(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, userAuthorityLimitDto, false);
                if (null != nextAuthLimitAssignUserList) {
                    break;
                } else {
                    if (level == AppConstant.LEVEL_NINE) {
                        nextAuthLimitAssignUserList = claimUserAllocationService.getNextAuthLimitAssignUserList(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, userAuthorityLimitDto, true);
                        if (null == nextAuthLimitAssignUserList) {
                            throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User Not Found To Assign Claim");
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return nextAuthLimitAssignUserList;
    }

    @Override
    public List<String> getUserListByLevel(Integer assignUserType, Integer level) {
        Connection connection = null;
        Integer accessUserType = null;
        try {
            connection = getJDBCConnection();
            if (assignUserType == 12 && level == 1) {
                return claimHandlerDao.searchAllUserByInAccessUserType(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM.toString());
            } else if (assignUserType == 12) {
                accessUserType = AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM;
            } else if (assignUserType == 6 && level == 1) {
                return claimHandlerDao.searchAllUserByInAccessUserType(connection, AppConstant.ACCESS_LEVEL_SPECIAL_TEAM.toString());
            } else {
                accessUserType = AppConstant.ACCESS_LEVEL_MOFA_TEAM;
            }
            UserAuthorityLimitDto userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, level, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            return claimUserAllocationService.getNextAuthLimitAssignUserList(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, userAuthorityLimitDto, true);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public List<String> getNextAssignUsersByCurrentUserName(String assignUser, Integer claimNo, Boolean levelReassign, Integer Status) throws Exception {
        Connection connection = null;
        Integer level = AppConstant.ZERO_INT;
        UserAuthorityLimitDto userAuthorityLimitDto = null;
        List<String> nextAuthLimitAssignUserList = new ArrayList<>();
        List<String> nextAuthLimitAssignUsers = new ArrayList<>();
        List<String> levelOneUserList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            if (AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT == Status) {
                nextAuthLimitAssignUserList = claimHandlerDao.getUserListByAccessUserType(connection);
            } else {
                Integer mofaMaxLevel = mofaLevelHierarchyDao.getMofaMaxLevel(connection);
                Boolean isSpecialComment = claimHandlerDao.checkClaimStatusByClaimNO(connection, claimNo);
                if (levelReassign) {
                    userAuthorityLimitDto = new UserAuthorityLimitDto();
                    userAuthorityLimitDto.setFromLimit(BigDecimal.ZERO);
                    userAuthorityLimitDto.setToLimit(userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, mofaMaxLevel, AppConstant.MOFA_TEAM_DEPARTMENT_ID).getToLimit());
                } else {
                    BigDecimal paymentAuthLevelByUserName = userDao.getPaymentAuthLevelByUserName(connection, assignUser);
                    userAuthorityLimitDto = userAuthorityLimitDao.searchByLimitAndDepartmentId(connection, paymentAuthLevelByUserName, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
                }
                if (null != userAuthorityLimitDto) {
                    level = userAuthorityLimitDto.getLevelCode();
                    int accessUserType;

                    if (claimNo == AppConstant.ZERO_INT) {
                        accessUserType = getAccessUserType(connection, assignUser);
                    } else {
                        //accessUserType = getAccessUserType(connection, claimNo);
                        accessUserType = getAccessUserType(connection, assignUser);
                    }
                    if (accessUserType == AppConstant.ACCESS_LEVEL_SPECIAL_TEAM || accessUserType == AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM) {
                        if (userAuthorityLimitDto.getLevelCode() == AppConstant.ONE_INT || isSpecialComment) {
                            levelOneUserList = claimUserAllocationService.getNextAuthLimitAssignUserList(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, userAuthorityLimitDto, false);
                        }
                        if (accessUserType == AppConstant.ACCESS_LEVEL_SPECIAL_TEAM) {
                            accessUserType = AppConstant.ACCESS_LEVEL_MOFA_TEAM;
                        }
                        if (accessUserType == AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM) {
                            accessUserType = AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM;
                        }
                    }
                    nextAuthLimitAssignUsers = claimUserAllocationService.getNextAuthLimitAssignUserList(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, userAuthorityLimitDto, false);

                    if (!nextAuthLimitAssignUsers.isEmpty()) {
                        if (!levelOneUserList.isEmpty()) {
                            for (String list : levelOneUserList) {
                                nextAuthLimitAssignUserList.add(list);
                            }
                        }
                        for (String list : nextAuthLimitAssignUsers) {
                            nextAuthLimitAssignUserList.add(list);
                        }

                    }
                    nextAuthLimitAssignUserList.remove(assignUser);
                    if (!nextAuthLimitAssignUserList.isEmpty()) {
                        nextAuthLimitAssignUserList.add(userAuthorityLimitDto.getLevelName());
                        nextAuthLimitAssignUserList.add(String.valueOf(userAuthorityLimitDto.getLevelCode()));
                        return nextAuthLimitAssignUserList;
                    } else {
                        if (level != mofaMaxLevel) {
                            ++level;
                        }
                        for (; level <= mofaMaxLevel; level++) {
                            userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, level, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
                            nextAuthLimitAssignUsers = claimUserAllocationService.getNextAuthLimitAssignUserList(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, userAuthorityLimitDto, false);
                            if (!levelOneUserList.isEmpty()) {
                                for (String list : levelOneUserList) {
                                    nextAuthLimitAssignUserList.add(list);
                                }
                            }
                            if (!nextAuthLimitAssignUsers.isEmpty()) {
                                for (String list : nextAuthLimitAssignUsers) {
                                    nextAuthLimitAssignUserList.add(list);
                                }
                            }
                            nextAuthLimitAssignUserList.remove(assignUser);
                            if (null != nextAuthLimitAssignUserList && !nextAuthLimitAssignUserList.isEmpty()) {
                                if (levelOneUserList.isEmpty()) {
                                    nextAuthLimitAssignUserList.add(userAuthorityLimitDto.getLevelName());
                                    nextAuthLimitAssignUserList.add(String.valueOf(userAuthorityLimitDto.getLevelCode()));
                                } else {
                                    nextAuthLimitAssignUserList.add("LEVEL ONE");
                                    nextAuthLimitAssignUserList.add(String.valueOf(AppConstant.ONE_INT));
                                }

                                break;
                            } else {
                                if (level == mofaMaxLevel) {
                                    nextAuthLimitAssignUserList = claimUserAllocationService.getNextAuthLimitAssignUserList(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, userAuthorityLimitDto, true);
                                    nextAuthLimitAssignUserList.remove(assignUser);
                                    if (null != nextAuthLimitAssignUserList && !nextAuthLimitAssignUserList.isEmpty()) {
                                        nextAuthLimitAssignUserList.add(userAuthorityLimitDto.getLevelName());
                                        nextAuthLimitAssignUserList.add(String.valueOf(userAuthorityLimitDto.getLevelCode()));
                                    } else {
                                        throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User Not Found To Assign Claim");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return nextAuthLimitAssignUserList;
    }

    @Override
    public void saveCalculationSheetAssignMofaLevels(Connection connection, Integer txnId, Integer claimNo, String user, String assignUser, Integer level) throws Exception {
        try {
            UserDto userByUsrid = userDao.getUserByUsrid(connection, user);
            UserDto userEntity = new UserDto();

            userEntity.setUserId(userByUsrid.getUserId());
            userEntity.setUserCode(userByUsrid.getUserCode());
            userEntity.setAccessUserType(userByUsrid.getAccessUserType());
            userEntity.setPaymentAuthLimit(userByUsrid.getPaymentAuthLimit());
            saveCalculationSheetAssignMofaLevel(connection, txnId, claimNo, userEntity, assignUser, level);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean hasVoucherGeneratedCalSheetForDo(Integer supplyOrderRefNo) {
        Connection connection = null;
        try {
            if (supplyOrderRefNo.equals(AppConstant.ZERO_INT)) {
                return false;
            }
            connection = getJDBCConnection();
            return claimCalculationSheetMainDao.isHavingVoucherGeneratedCalsheetForDo(connection, supplyOrderRefNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public void updateBillCheck(Integer id, String isChecked, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            if (AppConstant.YES.equalsIgnoreCase(isChecked) || AppConstant.NO.equalsIgnoreCase(isChecked)) {
                claimCalculationSheetDetailDao.updateBillCheckById(connection, id, isChecked);
            } else {
                throw new ErrorMsgException();
            }
//            cal
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Boolean validateDO(Integer calsheetId) throws Exception {
        Connection connection = null;
        boolean isValid = false;
        try {
            connection = getJDBCConnection();
            ClaimCalculationSheetMainDto claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calsheetId);
            if (null != claimCalculationSheetMainDto) {
                if (AppConstant.CAL_SHEET_TYPE_DO.equals(claimCalculationSheetMainDto.getCalSheetType())) {
                    Integer updatedDo = supplyOrderSummaryDao.isUpdatedDo(connection, calsheetId);
                    if (updatedDo.equals(AppConstant.ZERO_INT)) {
                        isValid = true;
                    } else {
                        SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMasterBySupplyOrderRefNo(connection, updatedDo);
                        if (supplyOrderSummaryDto.getSupplyOrderStatus().equals(SupplyOrderStatusEnum.APPROVED_SCRUTINIZING_TEAM.getSupplyOrderStatusEnum())) {
                            isValid = false;
                        } else {
                            isValid = true;
                        }
                    }
                } else {
                    isValid = true;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isValid;
    }

    @Override
    public String getPolicyChannelType(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimCalculationSheetMainDao.getPolicyChannelType(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Integer getCalsheetForBillChecking(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimCalculationSheetMainDao.searchByClaimNoForBillChecking(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean checkPendingPayments(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimCalculationSheetMainDao.checkPendingPayment(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void cancelPayment(Integer calSheetId, Integer claimNo, UserDto user) throws Exception {
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimCalculationSheetMainDao.updatePaymentApproveStatus(connection, calSheetId, ClaimStatus.PAYMENT_CANCELLED.getClaimStatus(), user.getUserId());
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            if (!user.getUserId().equals(searchMaster.getSpecialTeamAssignUserId())) {
                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Cancelled By MOFA User.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                saveNotification(connection, claimNo, user.getUserId(), searchMaster.getSpecialTeamAssignUserId(), notificationMsg, URL);
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Cancelled [ ".concat(user.getUserId()).concat("]"), notificationMsg);
                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 66, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet cancelled by \"MOFA User\"", user.getUserId(), searchMaster.getSpecialTeamAssignUserId());
            } else {
                String userId = searchMaster.getInputUser();
                String notificationMsg = claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet Cancelled By Special Team.".concat(" Cal Sheet No - ".concat(String.valueOf(calSheetId)));
                saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet[" + calSheetId + "] Cancelled [ ".concat(user.getUserId()).concat("]"), notificationMsg);
                this.saveCalculationProcessFlow(connection, claimNo, calSheetId, 66, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation sheet cancelled by \"Special Team\"", user.getUserId(), userId);
                saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, URL);
            }

            ClaimCalculationSheetMainTempDto tempDto = claimCalculationSheetMainTempDao.searchByCalsheetId(connection, calSheetId);
            if (null != tempDto) {
                claimHandlerDao.updateReserveAmountAndReserveAmountAfterApproved(connection, tempDto.getPrevReserveAmount(), tempDto.getPrevReserveAmountAfterApproved(), searchMaster.getClaimNo());
                ClaimCalculationSheetMainDto claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
                offlineReserveClaim(connection, claimCalculationSheetMainDto.getClaimNo(), tempDto.getPrevReserveAmount(), 1, user);

                claimCalculationSheetMainTempDao.deleteMaster(connection, calSheetId);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private String getPayeeName(Connection connection, CalculationSheetPayeeHistoryDto dto, Integer claimNo) throws Exception {
        String payeeName = AppConstant.STRING_EMPTY;
        String policyChannelType = claimCalculationSheetMainDao.getPolicyChannelType(connection, claimNo);
        switch (dto.getPayeeId()) {
            case 1://Insured
            case 4://NCB Reversal
            case 5://Insurance Company
                payeeName = dto.getPayeeDesc();
                break;
            case 2://Garage
            case 3://Leasing Company
            case 6://Government Authorities
            case 7://Third Party Individual
            case 8://Third Party Coorporate
            case 9://Supplier
            case 10://Other
                PopupItemDto popupItemDto = dbRecordCommonFunction.getPopupItemDto(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_CODE", "V_COMPANY_NAME", "N_REF_NO=" + dto.getPayeeDesc());
                payeeName = popupItemDto.getLabel();
                break;
        }
        return payeeName;
    }

    private String getForwardUserAndLevel(CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto) {
        if (null != calculationSheetAssignMofaLevelDetailDto) {
            if (calculationSheetAssignMofaLevelDetailDto.getInputMofaLevel() == AppConstant.LEVEL_ONE) {
                return "Special Team.";
            }
            return "MOFA Team ".concat("( Level-" + calculationSheetAssignMofaLevelDetailDto.getInputMofaLevel() + ").");
        }
        return AppConstant.STRING_EMPTY;
    }

    protected CalculationSheetAssignMofaLevelDetailDto saveCalculationSheetAssignMofaLevel(Connection connection, Integer calSheetId, Integer claimNo, UserDto user, String assignUserId, int mofaLevel) throws Exception {
        CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto = new CalculationSheetAssignMofaLevelDetailDto();
        try {
            int assignMofaLevel;
            if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_CLAIM_HANDLER || user.getAccessUserType() == AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER || user.getAccessUserType() == AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER
                    || user.getAccessUserType() == AppConstant.ACCESS_LEVEL_RTE || user.getAccessUserType() == 23 || user.getAccessUserType() == 24) {
                assignMofaLevel = AppConstant.ZERO_INT;
            } else if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SPECIAL_TEAM || user.getAccessUserType() == AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM) {
                assignMofaLevel = AppConstant.ONE_INT;
            } else {
                assignMofaLevel = userAuthorityLimitDao.getMofaLevel(connection, BigDecimal.valueOf(user.getPaymentAuthLimit()), AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            }
            calculationSheetAssignMofaLevelDetailDto.setCalsheetId(calSheetId);
            calculationSheetAssignMofaLevelDetailDto.setClaimNo(claimNo);
            calculationSheetAssignMofaLevelDetailDto.setInputUserId(user.getUserId());
            calculationSheetAssignMofaLevelDetailDto.setInputMofaLevel(assignMofaLevel);
            calculationSheetAssignMofaLevelDetailDto.setAssignUserId(assignUserId);
            calculationSheetAssignMofaLevelDetailDto.setAssignMofaLevel(mofaLevel);
            calculationSheetAssignMofaLevelDetailDto.setAssignDatetime(Utility.sysDateTime());

            calculationSheetAssignMofaLevelDetailDao.insertMaster(connection, calculationSheetAssignMofaLevelDetailDto);
            calculationSheetAssignMofaLevelDetailDao.insertHistory(connection, calculationSheetAssignMofaLevelDetailDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return calculationSheetAssignMofaLevelDetailDto;
    }

    private CalculationSheetAssignMofaLevelDetailDto getNextAssignMofaDetails(Connection connection, Integer claimNo, Integer calSheetId, Integer calSheetType, UserDto user) throws Exception {
        String mofaUser = null;
        UserAuthorityLimitDto userAuthorityLimitDto = new UserAuthorityLimitDto();
        CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto;
        BigDecimal totalPaidAmount;
        BigDecimal totalApprovedAcr;
        int levelCode;
        try {
            boolean isAvailableFullAndFinal = !claimCalculationSheetMainDao.isAvailableCalSheetByType(connection, claimNo, AppConstant.CAL_SHEET_TYPE_FULL_FINAL).equals(AppConstant.ZERO_INT) && !claimCalculationSheetMainDao.isAvailableCalSheetByType(connection, claimNo, AppConstant.CAL_SHEET_TYPE_FULL_FINAL).equals(calSheetId);
            totalApprovedAcr = claimHandlerDao.getTotalAcrAmount(connection, claimNo);
            totalPaidAmount = claimCalculationSheetMainDao.getTotalPayableAmount(connection, claimNo);
            String calSheetTypeDesc = claimCalculationSheetTypeDao.searchMaster(connection, calSheetType).getVCalSheetTypeDesc();

            if (calSheetType.equals(AppConstant.CAL_SHEET_TYPE_ADVANCED) || (calSheetType.equals(AppConstant.CAL_SHEET_TYPE_DO) && !isAvailableFullAndFinal)) {
                levelCode = userAuthorityLimitDao.getMofaLevel(connection, totalApprovedAcr, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            } else {
                levelCode = userAuthorityLimitDao.getMofaLevel(connection, totalPaidAmount, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            }

            int assignUserLevelCode = userAuthorityLimitDao.getMofaLevel(connection, BigDecimal.valueOf(user.getPaymentAuthLimit()), AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            int assignedLevel = 0;
            List<Integer> mandatoryMofaLevelList = mofaLevelHierarchyDao.getAllMandatoryMofaLevel(connection);
            boolean isAssignedLevel = false;
            if (null != mandatoryMofaLevelList) {
                for (Integer mofaLevel : mandatoryMofaLevelList) {
                    if (mofaLevel > assignUserLevelCode && mofaLevel <= levelCode) {
                        assignedLevel = mofaLevel;
                        userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, mofaLevel, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
                        mofaUser = getMofaUser(connection, userAuthorityLimitDto.getFromLimit().add(BigDecimal.ONE), claimNo, calSheetId, user, userAuthorityLimitDto, mofaLevel, false);
                        if (null == mofaUser) {
                            saveClaimsLogs(connection, claimNo, user, calSheetTypeDesc + " Calculation Sheet [" + calSheetId + "] Forwarded", "Level " + assignedLevel + " on Leave and Forward to next Level");
                        }
                        isAssignedLevel = true;
                        break;
                    }
                }
            }
            if (!isAssignedLevel) {
                assignedLevel = levelCode;
                userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, levelCode, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
                mofaUser = getMofaUser(connection, userAuthorityLimitDto.getFromLimit().add(BigDecimal.ONE), claimNo, calSheetId, user, userAuthorityLimitDto, levelCode, false);
                if (null == mofaUser) {
                    saveClaimsLogs(connection, claimNo, user, calSheetTypeDesc + " Calculation Sheet [" + calSheetId + "] Forwarded", "Level " + assignedLevel + " on Leave and Forward to next Level");
                }
                isAssignedLevel = true;
            }

            if (null == mofaUser) {
                Integer mofaMaxLevel = mofaLevelHierarchyDao.getMofaMaxLevel(connection);
                for (; assignedLevel <= mofaMaxLevel; assignedLevel++) {
                    userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, assignedLevel, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
                    mofaUser = getMofaUser(connection, userAuthorityLimitDto.getFromLimit().add(BigDecimal.ONE), claimNo, calSheetId, user, userAuthorityLimitDto, assignedLevel, false);
                    if (null != mofaUser) {
                        break;
                    } else {
                        if (!isAssignedLevel) {
                            saveClaimsLogs(connection, claimNo, user, calSheetTypeDesc + " Calculation Sheet [" + calSheetId + "] Forwarded", "Level " + assignedLevel + " on Leave and Forward to next Level");
                        } else {
                            isAssignedLevel = false;
                        }
                        if (assignedLevel == mofaMaxLevel) {
                            mofaUser = getMofaUser(connection, userAuthorityLimitDto.getFromLimit().add(BigDecimal.ONE), claimNo, calSheetId, user, userAuthorityLimitDto, assignedLevel, true);
                            if (null == mofaUser) {
                                throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User Not Found To Assign Claim");
                            }
                        }
                    }
                }
            }

            calculationSheetAssignMofaLevelDetailDto = saveCalculationSheetAssignMofaLevel(connection, calSheetId, claimNo, user, mofaUser, userAuthorityLimitDto.getLevelCode());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return calculationSheetAssignMofaLevelDetailDto;
    }

    private String getMofaUser(Connection connection, BigDecimal payableAmount, Integer claimNo, Integer calSheetId, UserDto user, UserAuthorityLimitDto userAuthorityLimitDto, int mofaLevel, boolean isMandatory) {
        try {
            int accessUserType;
            accessUserType = getAccessUserType(connection, claimNo);
            String mofaUser = calculationSheetAssignMofaLevelDetailDao.getAssignMofaUserByAssignMofaLevelAndCalsheetId(connection, calSheetId, mofaLevel, accessUserType);
            if (null == mofaUser || mofaUser.isEmpty()) {
                mofaUser = claimUserAllocationService.getNextAuthLimitAssignUser(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo, true, payableAmount, userAuthorityLimitDto.getToLimit(), 0, isMandatory);
            } else {
                boolean isLeave = claimUserAllocationDao.checkIfLeave(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), mofaUser);
                if (isLeave) {
                    mofaUser = claimUserAllocationService.getNextAuthLimitAssignUser(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo, true, payableAmount, userAuthorityLimitDto.getToLimit(), 0, isMandatory);
                }
            }
            return mofaUser;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void callNoClaimBonus(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            ClaimCalculationSheetMainDto searchMaster = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
            ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, searchMaster.getCalSheetType());
            claimCalculationSheetMainDao.updateNoClaimBonusStatus(connection, calSheetId, "C");
            saveClaimsLogs(connection, claimNo, user, claimCalculationSheetTypeDto.getVCalSheetTypeDesc() + " Calculation Sheet No : ".concat(String.valueOf(calSheetId)), "Call NCB Confirmation");
            this.saveCalculationProcessFlow(connection, claimNo, calSheetId, searchMaster.getStatus(), "\"NCB Confirmation\"has been called", user.getUserId(), user.getUserId());
            createNcbEmail(connection, email, claimNo, calSheetId, searchMaster);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    public void saveConfirmationLogs(Connection connection, Integer claimNo, UserDto user, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception {
        boolean hasOutstandingPremium = outstandingPremium.compareTo(BigDecimal.ZERO) > 0;
        try {
            if (isCancelledPolicy && hasOutstandingPremium) {
                saveClaimsLogs(connection, claimNo, user, "Cancelled Policy and Premium Outstanding Confirmation Alert", "Marked As Ok to \"This is a cancelled policy and has Rs." + outstandingPremium + " of outstanding premium amount\" alert");
            } else if (isCancelledPolicy) {
                saveClaimsLogs(connection, claimNo, user, "Cancelled Policy Confirmation Alert", "Marked As Ok to \"This is a cancelled policy\" alert");
            } else if (hasOutstandingPremium) {
                saveClaimsLogs(connection, claimNo, user, "Premium Outstanding Confirmation Alert", "Marked As Ok to \"This policy has Rs." + outstandingPremium + " of outstanding premium amount\" alert");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private void sendLargeClaimEmail(Connection connection, Integer claimNo, BigDecimal value, BigDecimal payableAmount, String transactionType, UserDto user, Integer calSheetType) throws Exception {
        boolean isCoInsOrFac = false;
        String emailSubject1 = AppConstant.STRING_EMPTY;
        String emailSubject2 = AppConstant.STRING_EMPTY;
        StringBuilder stringBuilder = new StringBuilder();
        Email email = new Email();
        boolean isSeperateMails = false;
        DecimalFormat decimalFormat = new DecimalFormat("#,###.00");
        String policyChannelType;
        try {
            List<LargeClaimEmailReceiversDto> automatedMailReceivers = largeClaimEmailDao.getAutomatedMailReceivers(connection);

            if (null != automatedMailReceivers && automatedMailReceivers.size() > 0) {
                BigDecimal authorityLimit = largeClaimEmailDao.getAuthorityLimits(connection, user.getAccessUserType());
                ClaimsDto claimsDto = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
                policyChannelType = null != claimsDto.getPolicyDto().getPolicyChannelType() && !claimsDto.getPolicyDto().getPolicyChannelType().isEmpty()
                        && claimsDto.getPolicyDto().getPolicyChannelType().equals(PolicyChannelType.TAKAFUL.name())
                        ? AppConstant.URL_TYPE_TAKAFUL : AppConstant.URL_TYPE_CONVENTIONAL;

                LargeClaimDto largeClaimDetailsDto = restPolicyDetailsService.getLargeClaimDetailsDtoList(claimsDto.getPolicyDto(), policyChannelType);
                CoInsOrFacDetailDto coInsOrFacDetail = restPolicyDetailsService.getCoInsOrFacDetail(claimsDto.getPolicyDto(), policyChannelType);
                if (null != coInsOrFacDetail && null != coInsOrFacDetail.getClaimNo()) {
                    isCoInsOrFac = true;
                }
                boolean isHistoryRecord = largeClaimEmailDao.isMailAlreadySent(connection, claimNo);

                BigDecimal mnDivident = new BigDecimal(1000000.0);
                BigDecimal limitInMillions = authorityLimit.divide(mnDivident, 2, RoundingMode.HALF_UP);
                String formattedVal = String.format("%.2fMN", limitInMillions);
                if (AppConstant.CAL_SHEET_TYPE_BALANCE.equals(calSheetType) && value.compareTo(authorityLimit) > 0 && isCoInsOrFac) {
                    isSeperateMails = true;
                    emailSubject1 = "LARGE CLAIM NOTIFICATION - CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto()
                            .getVehicleNumber() + "] – OVER " + formattedVal + " – FINAL APPROVAL [EXCEEDED]";
                    emailSubject2 = "TOTAL APPROVED CLAIM AMOUNT CO-INSURANCE FAC INSURANCE ARRANGEMENTS POLICY";
                } else if (AppConstant.CAL_SHEET_TYPE_BALANCE.equals(calSheetType) && value.compareTo(authorityLimit) > 0) {
                    emailSubject1 = "LARGE CLAIM NOTIFICATION - CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto()
                            .getVehicleNumber() + "] – OVER " + formattedVal + " – FINAL APPROVAL [EXCEEDED]";
                } else if (value.compareTo(authorityLimit) > 0 && isCoInsOrFac) {
                    isSeperateMails = true;
                    emailSubject1 = "LARGE CLAIM NOTIFICATION - CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto()
                            .getVehicleNumber() + "] – OVER " + formattedVal + " – FINAL APPROVAL";
                    emailSubject2 = "TOTAL APPROVED CLAIM AMOUNT CO-INSURANCE FAC INSURANCE ARRANGEMENTS POLICY";
                } else if (value.compareTo(authorityLimit) > 0) {
                    emailSubject1 = "LARGE CLAIM NOTIFICATION - CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto()
                            .getVehicleNumber() + "] – OVER " + formattedVal + " – FINAL APPROVAL";
                } else if (isCoInsOrFac) {
                    emailSubject1 = "TOTAL APPROVED CLAIM AMOUNT CO-INSURANCE FAC INSURANCE ARRANGEMENTS POLICY";
                } else {
                    return;
                }

                MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.LARGE_CLAIM_MAIL);
                email.setEmailMassege(messageContentDetails.getMessageBody());

                ArrayList<String> list = new ArrayList<>();

                if (isHistoryRecord) {
                    List<LargeClaimEmailHistoryDto> historyRecords = largeClaimEmailDao.getHistoryRecords(connection, claimNo);
                    for (LargeClaimEmailHistoryDto emailHistoryDto : historyRecords) {
                        stringBuilder.append("<tr><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(emailHistoryDto.getApprovedDate());
                        stringBuilder.append("</td><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(decimalFormat.format(emailHistoryDto.getAcr()));
                        stringBuilder.append("</td><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(emailHistoryDto.getCalsheetType());
                        stringBuilder.append("</td><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(decimalFormat.format(emailHistoryDto.getTotalPayable()));
                        stringBuilder.append("</td></tr>");
                    }
                } else {
                    stringBuilder.append("<tr><td style=\"border: 1px solid black; text-align: right;\">N/A</td>");
                    stringBuilder.append("<td style=\"border: 1px solid black; text-align: right;\">N/A</td>");
                    stringBuilder.append("<td style=\"border: 1px solid black; text-align: right;\">N/A</td>");
                    stringBuilder.append("<td style=\"border: 1px solid black; text-align: right;\">N/A</td></tr>");
                }

                String policyDate = policyDao.getOldestPolicyInspecDate(connection, claimsDto.getPolicyDto().getPolicyNumber());

                list.add(String.valueOf(stringBuilder));

                list.add(Utility.sysDate(AppConstant.DATE_FORMAT));

                list.add("MOTOR");
                list.add(claimsDto.getPolicyDto().getPolicyNumber());
                list.add("MOTOR");//Class
                list.add(claimsDto.getPolicyDto().getCustName());
                list.add(claimsDto.getPolicyDto().getVehicleNumber());
                list.add(formatDate(policyDate) + " - " + formatDate(claimsDto.getPolicyDto().getExpireDate()));//Original Policy Period
                list.add("Rs." + decimalFormat.format(claimsDto.getPolicyDto().getSumInsured()));//Sum Insured
                list.add(String.valueOf(claimNo));
                list.add(String.valueOf(claimsDto.getIsfClaimNo()));
                list.add(formatDate(claimsDto.getAccidDate()));//Date of Loss
                list.add(formatDate(claimsDto.getDateOfReport()));//Date of Intimation
                String causeOfLoss = commonUtilDao.findOne(connection, "claim_cause_of_loss_type", "V_CAUSE_OF_LOSS", "N_ID=" + claimsDto.getCauseOfLoss());
                list.add(causeOfLoss);//Cause of Loss
                list.add("Rs." + decimalFormat.format(value));//Gross Reserve
                list.add(transactionType);
                Integer maxInvestigationId = investigationDetailsDao.getMaxInvestigationTxnIdByClaimNo(connection, claimNo);
                InvestigationDetailsDto investigationDetailsDto = investigationDetailsDao.searchMaster(connection, maxInvestigationId);
                list.add(maxInvestigationId == AppConstant.ZERO_INT || null == investigationDetailsDto.getAssignInvestigatorName() ? "N/A" : investigationDetailsDto.getAssignInvestigatorName());//Investigation Appointment
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getFac() ? "N/A"
                        : largeClaimDetailsDto.getFac() + "%");// (FAC/COINS%)
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getRetention() ? "N/A"
                        : largeClaimDetailsDto.getRetention() + "%");// Retention%
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getQs() ? "N/A"
                        : largeClaimDetailsDto.getQs() + "%");//  QS%
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getFac() ? "N/A"
                        : largeClaimDetailsDto.getFac() + "%");// (FAC/COINS%)
                list.add("N/A");//Aportionment

                email.setParameterEmail(list);

                for (LargeClaimEmailReceiversDto receiversDto : automatedMailReceivers) {
                    email.setToAddresses(receiversDto.getEmail());
                    email.setSubject(emailSubject1);
                    emailService.sendEmail(connection, email);
                    if (isSeperateMails) {
                        email.setSubject(emailSubject2);
                        emailService.sendEmail(connection, email);
                    }
                }

                LargeClaimEmailHistoryDto emailHistoryDto = new LargeClaimEmailHistoryDto();
                emailHistoryDto.setClaimNo(claimNo);
                emailHistoryDto.setAcr(value);
                emailHistoryDto.setApprovedUser(user.getUserId());
                emailHistoryDto.setApprovedDate(Utility.sysDateTime());
                emailHistoryDto.setTotalPayable(payableAmount);
                emailHistoryDto.setCalsheetType(transactionType);
                largeClaimEmailDao.saveHistory(connection, emailHistoryDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void setOtherDetailsList(ClaimsDto claimsDto) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            PolicyDto policyDto = claimsDto.getPolicyDto();
            this.setOtherDetailsList(policyDto);

            String dateOfReport = claimsDto.getDateOfReport();
            if (AppConstant.DEFAULT_DATE.equalsIgnoreCase(dateOfReport)) {
                dateOfReport = Utility.sysDate(AppConstant.DATE_FORMAT);
            }
            List<ClaimsDto> policyClaimList = null;

            if (!policyDto.getVehicleNumber().isEmpty()) {
                policyClaimList = callCenterDao.getPolicyClaimList(connection, policyDto.getVehicleNumber(), claimsDto.getClaimNo(), dateOfReport);
            }
            claimsDto.setClaimHistory(policyClaimList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void setOtherDetailsList(PolicyDto policyDto) {
        String policyChannelType = null != policyDto.getPolicyChannelType() && !policyDto.getPolicyChannelType().isEmpty()
                && policyDto.getPolicyChannelType().equals(PolicyChannelType.TAKAFUL.name())
                ? AppConstant.URL_TYPE_TAKAFUL : AppConstant.URL_TYPE_CONVENTIONAL;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            policyDto.setProductDetailListDto(userManagementService.getProductDetailByName(policyDto.getProduct()));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private Email createClaimRejectionEmail(Connection connection, String emailVal, Integer claimNo) throws
            Exception {
        try {
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.PREMIUM_CONFIRMATION_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();
                if (null != search.getVehicleNo() && !search.getVehicleNo().isEmpty()) {
                    list.add(search.getVehicleNo());
                } else {
                    list.add(search.getCoverNoteNo());
                }

                list.add(search.getPolicyDto().getPolicyNumber());
                list.add(search.getAccidDate());
                list.add(claimHandlerDao.getAssignedClaimHandler(connection, search.getClaimNo()));
                list.add(getMailSendUser());
                email.setParameterEmail(list);

                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

    private Email documentRequestingEmail(Connection connection, String emailVal, Integer claimNo) throws Exception {
        try {
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.PREMIUM_CONFIRMATION_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();
                if (null != search.getVehicleNo() && !search.getVehicleNo().isEmpty()) {
                    list.add(search.getVehicleNo());
                } else {
                    list.add(search.getCoverNoteNo());
                }

                list.add(search.getPolicyDto().getPolicyNumber());
                list.add(getTotalPaidForClaim(connection, claimNo).toString());
                list.add(search.getAccidDate());
                list.add(null == search.getPolicyDto().getPolicyBranch() || search.getPolicyDto().getPolicyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getPolicyBranch());
                list.add(null == search.getPolicyDto().getFinanceCompany() || search.getPolicyDto().getFinanceCompany().isEmpty() ? "N/A" : search.getPolicyDto().getFinanceCompany());
                list.add(null == search.getPolicyDto().getFinCompanyBranch() || search.getPolicyDto().getFinCompanyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getFinCompanyBranch());
                list.add(search.getPolicyDto().getCustName());
                list.add(search.getPolicyDto().getCustNic());
                list.add(search.getIsfClaimNo());
                list.add(policyDao.getChannelCode(connection, search.getPolicyDto().getChannel()));
                list.add(search.getIsfClaimNo()); // Pending Create Date

                List<ClaimRemindersDto> reminders = policyDao.getReminders(connection, claimNo);
                int i = 0;
                if (!reminders.isEmpty() && reminders.get(0).getdGeneratedDateTime() != null) {
                    i++;
                    list.add(i + getOrdinalSuffix(i) + "Reminder Date " + reminders.get(0).getdGeneratedDateTime().toString());
                } else {
                    list.add("N/A");
                }


                list.add(getMailSendUser());
                email.setParameterEmail(list);

                String subject = messageContentDetails.getSubject().concat(" - Claim No :-").
                        concat(search.getClaimNo().toString());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

    private String getOrdinalSuffix(int number) {
        if (number >= 11 && number <= 13) {
            return "th";
        }
        return switch (number % 10) {
            case 1 -> "st";
            case 2 -> "nd";
            case 3 -> "rd";
            default -> "th";
        };
    }

    private Email claimDocRejection(Connection connection, String emailVal, Integer claimNo) throws Exception {
        try {
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.PREMIUM_CONFIRMATION_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();
                if (null != search.getVehicleNo() && !search.getVehicleNo().isEmpty()) {
                    list.add(search.getVehicleNo());
                } else {
                    list.add(search.getCoverNoteNo());
                }

                list.add(search.getPolicyDto().getPolicyNumber());
                list.add(getTotalPaidForClaim(connection, claimNo).toString());
                list.add(search.getAccidDate());
                list.add(null == search.getPolicyDto().getPolicyBranch() || search.getPolicyDto().getPolicyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getPolicyBranch());
                list.add(null == search.getPolicyDto().getFinanceCompany() || search.getPolicyDto().getFinanceCompany().isEmpty() ? "N/A" : search.getPolicyDto().getFinanceCompany());
                list.add(null == search.getPolicyDto().getFinCompanyBranch() || search.getPolicyDto().getFinCompanyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getFinCompanyBranch());
                list.add(search.getPolicyDto().getCustName());
                list.add(search.getPolicyDto().getCustNic());
                list.add(search.getIsfClaimNo());
                list.add(policyDao.getChannelCode(connection, search.getPolicyDto().getChannel()));
                list.add(getMailSendUser());
                email.setParameterEmail(list);

                String subject = messageContentDetails.getSubject().concat(search.getClaimNo().toString());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

    private Email appealLetterEmail(Connection connection, String emailVal, Integer claimNo) throws Exception {
        try {
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.PREMIUM_CONFIRMATION_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();
                if (null != search.getVehicleNo() && !search.getVehicleNo().isEmpty()) {
                    list.add(search.getVehicleNo());
                } else {
                    list.add(search.getCoverNoteNo());
                }

                list.add(search.getPolicyDto().getPolicyNumber());
                list.add(getTotalPaidForClaim(connection, claimNo).toString());
                list.add(search.getAccidDate());
                list.add(null == search.getPolicyDto().getPolicyBranch() || search.getPolicyDto().getPolicyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getPolicyBranch());
                list.add(null == search.getPolicyDto().getFinanceCompany() || search.getPolicyDto().getFinanceCompany().isEmpty() ? "N/A" : search.getPolicyDto().getFinanceCompany());
                list.add(null == search.getPolicyDto().getFinCompanyBranch() || search.getPolicyDto().getFinCompanyBranch().isEmpty() ? "N/A" : search.getPolicyDto().getFinCompanyBranch());
                list.add(search.getPolicyDto().getCustName());
                list.add(search.getPolicyDto().getCustNic());
                list.add(search.getIsfClaimNo());
                list.add(policyDao.getChannelCode(connection, search.getPolicyDto().getChannel())); // channel code
                list.add(getMailSendUser());
                email.setParameterEmail(list);

                String subject = messageContentDetails.getSubject().concat(search.getVehicleNo()).concat(" - Claim No :-").
                        concat(search.getClaimNo().toString());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

    private Email salvageEmail(Connection connection, String emailVal, Integer claimNo) throws Exception {
        try {
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.PREMIUM_CONFIRMATION_EMAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            if (null != search) {
                email.setToAddresses(emailVal);
                ArrayList<String> list = new ArrayList<>();
                if (null != search.getVehicleNo() && !search.getVehicleNo().isEmpty()) {
                    list.add(search.getVehicleNo());
                } else {
                    list.add(search.getCoverNoteNo());
                }

                list.add(search.getAccidDate());
                list.add(search.getDrivenLicenseDocumentList().get(0).getJobRefNo().toString());
                list.add(search.getPolicyDto().getPolicyNumber());
                list.add(search.getPolicyDto().getPolicyNumber()); // todo- Set "Operator Comment" attribute
                list.add(search.getPolicyDto().getPolicyNumber()); // todo- Set "Remarks by Engineering Department" attribute
                list.add(getMailSendUser());
                email.setParameterEmail(list);

                String subject = messageContentDetails.getSubject().concat(" / ")
                        .concat(search.getDrivenLicenseDocumentList().get(0).getJobRefNo().toString());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

        return null;
    }

//
public DocumentContentDetails claimRepudiatedDocument(Connection connection, Integer claimNo, ClaimReasons reason)
        throws Exception {
    try {

        DocumentContentDetails documentContentDetail =
                documentContentDao.searchDocumentContentDetail(connection, reason.toString());

        if (documentContentDetail == null) {
            throw new IllegalArgumentException("No document found for subject: " + reason.toString());
        }

        String bodyTemplate = documentContentDetail.getDocumentBody();
        String subjectTemplate = documentContentDetail.getDocumentSubject();

        ClaimsDto claim = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);

        if (claim != null) {

            Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
            Matcher matcher = pattern.matcher(bodyTemplate + subjectTemplate);

            Set<String> placeholders = new HashSet<>();
            while (matcher.find()) {
                placeholders.add(matcher.group(1));
            }

            for (String fieldName : placeholders) {
                String value = "";
                try {
                    // Try direct field from ClaimsDto
                    Field field = claim.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object objValue = field.get(claim);
                    value = objValue != null ? objValue.toString() : "";
                } catch (NoSuchFieldException nf) {
                    // Try nested PolicyDto
                    try {
                        Field nested = claim.getClass().getDeclaredField("policyDto");
                        nested.setAccessible(true);
                        Object nestedObj = nested.get(claim);
                        if (nestedObj != null) {
                            Field nestedField = nestedObj.getClass().getDeclaredField(fieldName);
                            nestedField.setAccessible(true);
                            Object nestedValue = nestedField.get(nestedObj);
                            value = nestedValue != null ? nestedValue.toString() : "";
                        }
                    } catch (Exception ignored) {
                        LOGGER.warn("No matching field found for placeholder: {}", fieldName);
                    }
                }

                // Replace in body and subject
                bodyTemplate = bodyTemplate.replace("${" + fieldName + "}", value);
                subjectTemplate = subjectTemplate.replace("${" + fieldName + "}", value);
            }
        }

        documentContentDetail.setDocumentBody(bodyTemplate);
        documentContentDetail.setDocumentSubject(subjectTemplate);

        return documentContentDetail;

    } catch (Exception e) {
        LOGGER.error("Error in claimRepudiatedDocument: {}", e.getMessage(), e);
        throw e;
    }
}

    @Override
    public List<BankLovDto> getBankLov() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimCalculationSheetPayeeDao.getBankLov(connection);
        } catch (Exception e) {
            LOGGER.error("Error fetching bank LOV data: {}", e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

}


