package com.misyn.mcms.claim.service.keycloak;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.misyn.mcms.utility.Parameters;
import jakarta.ws.rs.client.*;
import jakarta.ws.rs.core.Form;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KeycloakApiClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(KeycloakApiClient.class);
    private static final String KEYCLOAK_URL = Parameters.getKeycloakServerUrl();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final String REALM = Parameters.getKeycloakRealm();
    private static final String CLIENT_ID = Parameters.getKeycloakClientId();
    private static final String CLIENT_SECRET = Parameters.getKeycloakClientSecret();

    public static String getBearerToken() {
        String tokenUrl = KEYCLOAK_URL + "/realms/" + REALM + "/protocol/openid-connect/token";


        try (Client client = ClientBuilder.newClient()) {
            Form form = new Form()
                    .param("grant_type", "client_credentials")
                    .param("client_id", CLIENT_ID)
                    .param("client_secret", CLIENT_SECRET);

            WebTarget target = client.target(tokenUrl);

            try (Response response = target.request(MediaType.APPLICATION_JSON)
                    .post(Entity.form(form))) {
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    String jsonResponse = response.readEntity(String.class);
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode jsonNode = objectMapper.readTree(jsonResponse);
                    return jsonNode.get("access_token").asText();
                } else {
                    LOGGER.error("Failed to get token: {} - {}", response.getStatus(), response.readEntity(String.class));
                    return null;
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    public static <T> T sendGetRequest(String endpoint, String token, TypeReference<T> typeReference) {
        String url = KEYCLOAK_URL + endpoint;

        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(url);
            Invocation.Builder request = target.request(MediaType.APPLICATION_JSON)
                    .header("Authorization", "Bearer " + token);

            try (Response response = request.get()) {
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    String jsonResponse = response.readEntity(String.class);
                    return objectMapper.readValue(jsonResponse, typeReference);
                } else {
                    LOGGER.info("GET request failed: {} - {}", response.getStatus(), response.readEntity(String.class));
                }
                return null;
            }
        } catch (JsonMappingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException("JSON mapping error", e);
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException("JSON processing error", e);
        }
    }

    public static <T> T sendPostRequest(String endpoint, String token, Object payload, TypeReference<T> typeReference) {
        String url = KEYCLOAK_URL + endpoint;

        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(url);
            Invocation.Builder request = target.request(MediaType.APPLICATION_JSON)
                    .header("Authorization", "Bearer " + token);

            String jsonPayload = objectMapper.writeValueAsString(payload);

            try (Response response = request.post(Entity.entity(jsonPayload, MediaType.APPLICATION_JSON))) {
                if (response.getStatus() == Response.Status.OK.getStatusCode() ||
                        response.getStatus() == Response.Status.CREATED.getStatusCode()) {
                    String jsonResponse = response.readEntity(String.class);
                    return objectMapper.readValue(jsonResponse, typeReference);
                } else {
                    LOGGER.info("POST request failed: {} - {}", response.getStatus(), response.readEntity(String.class));
                }
                return null;
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON processing error", e);
        }
    }
}
