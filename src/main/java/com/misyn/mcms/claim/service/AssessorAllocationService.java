package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 4/19/18.
 */
public interface AssessorAllocationService extends BaseService<AssessorAllocationDto> {
    List<AssessorAllocationDto> getAssessorListByClaimNo(int claimId) throws Exception;

    boolean updateCompletedJob(int refId, UserDto user) throws Exception;

    ErrorMessageDto saveRemark(SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception;

    AssessorAllocationDto reSendMessages(AssessorAllocationDto assessorAllocationDto) throws Exception;

    List<UserDto> getRTEList();

    List<String> getInputUserList();

    List<UserDto> getTCList() throws Exception;

    List<UserDto> getInitLiablityUserList() throws Exception;

    List<UserDto> getClaimHandlerUserList() throws Exception;

    List<NotificationDto> getNotifiList() throws Exception;

    NotificationDto insertNotification(NotificationDto notificationDto) throws Exception;

    ApplicationAlertDto insertAlert(ApplicationAlertDto applicationAlertDto) throws Exception;

    ApplicationAlertDto searchAlert(Integer keyValue, Integer type) throws Exception;

    List<InspectionDto> getInspectionList(Integer claimNo) throws Exception;

    boolean checkValidInspectionType(Integer claimNo, Integer inspctionId) throws Exception;

    boolean updateCompletedJob(int refId, UserDto user, Connection connection) throws Exception;

    AssessorAllocationDto search(Connection connection, Object id) throws Exception;

    InspectionDetailsDto getLatestUpdateOnSite(InspectionDetailsDto inspectionDetailsDto) throws Exception;

    void rejectAssesorJob(AssessorAllocationDto assessorAllocationDto, UserDto user, String assignUser) throws Exception;

    List<UserDto> getReportingRteList() throws Exception;

    List<AssessorDto> getAssessorListByRteCode(String rteCode);

    List<InspectionDto> getInspectionListForReassignScreen(int refId) throws Exception;
}
