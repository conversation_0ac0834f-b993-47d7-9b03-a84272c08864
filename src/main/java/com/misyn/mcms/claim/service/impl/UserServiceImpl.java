package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimUserMasterDao;
import com.misyn.mcms.claim.dao.impl.ClaimUserMasterDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;

public class UserServiceImpl extends AbstractBaseService<UserServiceImpl> implements UserService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveServiceImpl.class);
    private final ClaimUserMasterDao claimUserMasterDao = new ClaimUserMasterDaoImpl();

    @Override
    public UserMasterDto saveClaimUser(UserMasterDto userDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            claimUserMasterDao.insertMaster(connection, userDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userDto;
    }

    @Override
    public UserMasterDto updateClaimUser(UserMasterDto userDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            claimUserMasterDao.updateMaster(connection, userDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userDto;
    }

    @Override
    public DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, Integer type) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimUserMasterDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<ClaimDepartmentDto> getAllDepartments() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimUserMasterDao.getAllDepartments(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<AssessorDto> getActiveAssessors() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimUserMasterDao.getActiveAssessors(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

}
