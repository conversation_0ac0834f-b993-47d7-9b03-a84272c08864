package com.misyn.mcms.claim.controller.callcenter.validator;

import com.misyn.mcms.claim.dto.ClaimLogTrailDto;
import com.misyn.mcms.claim.dto.FormFieldDto;
import com.misyn.mcms.claim.service.FormFieldService;
import com.misyn.mcms.claim.service.impl.FormFieldServiceImpl;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class LoggerTrail<T> {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoggerTrail.class);
    FormFieldService formFieldService = new FormFieldServiceImpl();

    public List<ClaimLogTrailDto> getLoggerTrailDetailsList(T t1, T t2, Integer formNameId) {

        List<ClaimLogTrailDto> logTrailList = new ArrayList<>();

        Field[] fields = t1.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                String name = field.getName();
                field.setAccessible(true);
                Object newValue = field.get(t1);
                Object oldValue = field.get(t2);

                if (oldValue != null && !newValue.equals(oldValue)) {
                    ClaimLogTrailDto dto = new ClaimLogTrailDto();
                    FormFieldDto fieldDetails = formFieldService.getDtoFieldRelatedField(formNameId, name);
                    if (null == fieldDetails || null == fieldDetails.getFormFieldName() || fieldDetails.getFormFieldName().isEmpty())
                        continue;
                    dto.setFormNameId(formNameId);
                    dto.setFieldName(fieldDetails.getFormFieldName());
                    dto.setFieldValue(getLoggerValue(name, newValue.toString()));
                    logTrailList.add(dto);
                }

            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return logTrailList;
    }

    private String getLoggerValue(String name, String value) {
        DbRecordCommonFunction dbFunction = new DbRecordCommonFunction();
        String returnedValue = "";
        switch (name) {
            case "preferredLanguage":
                switch (value) {
                    case "E":
                        returnedValue = "English";
                        break;
                    case "S":
                        returnedValue = "Sinhala";
                        break;
                    case "T":
                        returnedValue = "Tamil";
                        break;
                }
                break;
            case "isSameReportDriver":
            case "isCatEvent":
            case "isFirstStatementReq":
            case "ncbProm":
            case "lomoProm":
            case "isfsUpdateStatus":
            case "isNoDamage":
            case "isHugeDamage":
            case "printLetter":
            case "negPremOut":
            case "isDoubt":
            case "isFutherDamage":
                switch (value) {
                    case "Y":
                        returnedValue = "Yes";
                        break;
                    case "N":
                        returnedValue = "No";
                        break;
                    case "WD":
                        returnedValue = "Want to decide";
                        break;
                }
                break;
            case "reporterTitle":
            case "driverTitle":
            case "followCallContactPersonTitle":
                returnedValue = dbFunction.getValue("claim_salutation", "salutation_name", "salutation_id", value);
                break;
            case "districtCode":
                returnedValue = dbFunction.getValue("claim_district", "V_DISTRICT_NAME", "N_TXN_ID", value);
                break;
            case "ncbReason":
                returnedValue = dbFunction.getValue("claim_ncb_reason", "V_REASON", "N_ID", value);
                break;
            case "catEventCode":
                returnedValue = dbFunction.getValue("claim_cat_event", "V_CAT_EVENT_DESC", "N_ID", value);
                break;
            case "causeOfLoss":
                returnedValue = dbFunction.getValue("claim_cause_of_loss_type", "V_CAUSE_OF_LOSS", "N_ID", value);
                break;
            case "draftReason":
                returnedValue = dbFunction.getValue("claim_draft_reason", "V_REASION", "N_REF_ID", value);
                break;
            case "driverStatus":
                returnedValue = dbFunction.getValue("claim_driver_status", "V_DRIVER_STATUS_DESC", "N_ID", value);
                break;
            case "nearestCity":
                returnedValue = dbFunction.getValue("claim_gramas", "V_GRAMA_NAME", "N_GRAMA_CODE", value);
                break;
            case "inspectionTypeReason":
                returnedValue = dbFunction.getValue("claim_inspection_type_reason", "V_REASON", "N_ID", value);
                break;
            case "intimationType":
                returnedValue = dbFunction.getValue("claim_intimate_type", "V_INTIMATION_TYPE", "N_ID", value);
                break;
            case "lateIntimateReason":
                returnedValue = dbFunction.getValue("claim_late_intimation_reason", "V_INTIMATION_REASON", "N_ID", value);
                break;
            case "lomoReason":
                returnedValue = dbFunction.getValue("claim_lomo_reason", "V_REASON", "N_ID", value);
                break;
            case "nearPoliceStation":
                returnedValue = dbFunction.getValue("claim_police_station", "V_POLICE_NAME", "N_REF_NO", value);
                break;
            case "followCallAgnetServiceRate":
            case "followCallAssessorServiceRate":
                returnedValue = dbFunction.getValue("claim_rate_option_type", "V_RATE_OPTION", "N_ID", value);
                break;
            case "claimStatus":
                returnedValue = dbFunction.getValue("claim_status_para", "v_status_desc", "n_ref_id", value);
                break;
            case "driverReleshipInsurd":
            case "releshipInsurd":
                returnedValue = dbFunction.getValue("claim_insured_relationship", "v_desc", "n_id", value);
                break;
            case "firstStatementReqReason":
                returnedValue = dbFunction.getValue("claim_first_statement_reason", "V_REASON", "n_id", value);
                break;
            case "vehClsId":
                returnedValue = dbFunction.getValue("claim_vehicle_class", "V_VEH_CLS_DESC", "N_VEH_CLS_ID", value);
                break;
            case "driverLicenceType": switch (value) {
                case "1":
                    returnedValue = "Local";
                    break;
                case "2":
                    returnedValue = "International";
                    break;
            }
                break;

            default:
                returnedValue = value;
        }
        return returnedValue;
    }
}
