package com.misyn.mcms.claim.controller.admin;

import com.google.gson.Gson;
import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.misyn.mcms.admin.admin.dto.AccessUserTypeDto;
import com.misyn.mcms.admin.admin.dto.UserTypeDto;
import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.admin.admin.service.impl.UserManagementServiceImpl;
import com.misyn.mcms.claim.controller.ClaimUserLeaveController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.AssessorAllocationService;
import com.misyn.mcms.claim.service.UserService;
import com.misyn.mcms.claim.service.impl.AssessorAllocationServiceImpl;
import com.misyn.mcms.claim.service.impl.UserServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "ClaimUserLeaveController", urlPatterns = "/ClaimUserController/*")
public class UserController extends HttpServlet {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveController.class);
    private final UserManagementService userManagementService = new UserManagementServiceImpl();
    private final AssessorAllocationService assessorAllocationService = new AssessorAllocationServiceImpl();
    private int draw = 1;
    private UserService userService = null;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void addFieldParameter(String dbFieldName, String value, FieldParameterDto.SearchType searchType, List<FieldParameterDto> parameterList) {
        if (!AppConstant.STRING_EMPTY.equals(value)) {
            FieldParameterDto fieldParameterDTO = new FieldParameterDto();
            fieldParameterDTO.setDbFieldName(dbFieldName);
            fieldParameterDTO.setFieldValue(value);
            fieldParameterDTO.setStringType(!searchType.equals(FieldParameterDto.SearchType.NOT_IN) && !searchType.equals(FieldParameterDto.SearchType.IN));
            fieldParameterDTO.setSearchType(searchType);
            parameterList.add(fieldParameterDTO);
        }
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        userService = new UserServiceImpl();
        try {
            switch (pathInfo) {
                case "/save":
                    saveClaimUser(request, response);
                    break;
                case "/update":
                    updateClaimUser(request, response);
                    break;
                case "/getAll":
                    searchClaimUsers(request, response);
                    break;
                case "/load":
                    loadCreateUserScreen(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchClaimUsers(HttpServletRequest request, HttpServletResponse response) {
        Integer type = null == request.getParameter("type") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("type"));
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        String userRole = request.getParameter("userRole") == null ? AppConstant.STRING_EMPTY : request.getParameter("userRole");
        String userStatus = request.getParameter("userStatus") == null ? AppConstant.STRING_EMPTY : request.getParameter("userStatus");
        String userFirstName = request.getParameter("userFirstName") == null ? AppConstant.STRING_EMPTY : request.getParameter("userFirstName");
        String userMobile = request.getParameter("userMobile") == null ? AppConstant.STRING_EMPTY : request.getParameter("userMobile");
        String userID = request.getParameter(AppConstant.TXT_USER_ID) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_USER_ID);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("v_usrid", userID, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("n_accessusrtype", userRole.equals(AppConstant.ZERO) ? AppConstant.STRING_EMPTY : userRole, FieldParameterDto.SearchType.Equal, parameterList);
            this.addFieldParameter("v_firstname", userFirstName, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("v_mobile", userMobile, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("v_usrstatus", userStatus, FieldParameterDto.SearchType.Equal, parameterList);

            switch (orderColumnName) {
                case "userStatus":
                    orderColumnName = "v_usrstatus";
                    break;
                case "firstName":
                    orderColumnName = "v_firstname";
                    break;
                case "lastName":
                    orderColumnName = "v_lastname";
                    break;
                case "accessUserTypeDesc":
                    orderColumnName = "a.v_accessusrtype";
                    break;
                case "userId":
                    orderColumnName = "v_usrid";
                    break;
                case "userCode":
                    orderColumnName = "n_usrcode";
                    break;
            }

            DataGridDto data = userService.getUserDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, type);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    private UserMasterDto setData(BufferedReader reader) throws IOException {
        StringBuilder jsonBuffer = new StringBuilder();
        String line;
        UserMasterDto userMasterDto = new UserMasterDto();
        while ((line = reader.readLine()) != null) {
            jsonBuffer.append(line);
        }

        // Convert JSON string to JSONObject
        String jsonString = jsonBuffer.toString();
        JSONObject jsonObject = new JSONObject(jsonString);

        // Extract values from the JSON object
        userMasterDto.setUserCode(jsonObject.optInt("userCode", 0));
        userMasterDto.setCompanyId(jsonObject.optInt("companyId", 1));
        userMasterDto.setUserId(jsonObject.optString("userId"));
        userMasterDto.setCompanyCode(jsonObject.optString("company"));
        userMasterDto.setUserId(jsonObject.optString("loginName"));
        userMasterDto.setAccessUserType(jsonObject.optInt("department"));
        JSONArray userRolesArray = jsonObject.optJSONArray("userRole");
        if (userRolesArray != null) {
            List<String> roles = new ArrayList<>();
            for (int i = 0; i < userRolesArray.length(); i++) {
                roles.add(userRolesArray.getString(i));
            }
            String rolesCommaSeparated = String.join(",", roles);
            userMasterDto.setUserTypes(rolesCommaSeparated);
        }
        JSONArray userRoleDesArray = jsonObject.optJSONArray("userRoleDesc");
        if (userRoleDesArray != null) {
            List<String> roleDescriptions = new ArrayList<>();
            for (int i = 0; i < userRoleDesArray.length(); i++) {
                roleDescriptions.add(userRoleDesArray.getString(i));
            }
            String roleDescCommaSeparated = String.join(",", roleDescriptions);
            userMasterDto.setUserTypeDescription(roleDescCommaSeparated);
        }
        userMasterDto.setPassword(jsonObject.optString("password"));
        userMasterDto.setPasswordHash(jsonObject.optString("passwordHash"));
        userMasterDto.setUserStatus(jsonObject.optString("userStatus"));
        userMasterDto.setTitle(jsonObject.optString("title"));
        userMasterDto.setFirstName(jsonObject.optString("firstName"));
        userMasterDto.setLastName(jsonObject.optString("lastName"));
        userMasterDto.setEmployeeNumber(jsonObject.optString("empCode"));
        userMasterDto.setEmail(jsonObject.optString("email"));
        userMasterDto.setAddress1(jsonObject.optString("address1"));
        userMasterDto.setAddress2(jsonObject.optString("address2"));
        userMasterDto.setLandPhone(jsonObject.optString("landPhone"));
        userMasterDto.setMobile(jsonObject.optString("mobilePhone"));
        userMasterDto.setNic(jsonObject.optString("nic"));
        userMasterDto.setTeamId(jsonObject.optInt("teamId", 0));
        userMasterDto.setLiabilityLimit(jsonObject.optDouble("liabilityLimit", 0));
        userMasterDto.setPaymentLimit(jsonObject.optDouble("paymentLimit", 0));
        userMasterDto.setPaymentAuthLimit(jsonObject.optDouble("paymentAuthLimit", 0));
        userMasterDto.setReserveLimit(jsonObject.optDouble("reserveLimit", 0));
        // Handle reporting fields - all map to single V_REPORT_TO column
        String reportingTo = "";
        if (!jsonObject.optString("reportingTo", "").isEmpty()) {
            reportingTo = jsonObject.optString("reportingTo", "");
        } else if (!jsonObject.optString("assessorReportingTo", "").isEmpty()) {
            reportingTo = jsonObject.optString("assessorReportingTo", "");
        } else if (!jsonObject.optString("reportingToRte", "").isEmpty()) {
            reportingTo = jsonObject.optString("reportingToRte", "");
        }
        userMasterDto.setReportingTo(reportingTo);

        String assessorCode = jsonObject.optString("assessorCode", "");
        userMasterDto.setAssessorName(assessorCode);
        userMasterDto.setAssessorType(jsonObject.optString("assessorType", ""));

        String districtCode = jsonObject.optString("assignDistrict", "");
        userMasterDto.setDistrictCode(districtCode);

        // Set reserve limit level for regional roles
        if (jsonObject.has("reserveLimitOnly") && !jsonObject.isNull("reserveLimitOnly")) {
            userMasterDto.setReserveLimitLevel(jsonObject.optInt("reserveLimitOnly", 0));
        }

        // Set default values for new users
        //userMasterDto.setActiveDate(Utility.sysDate("yyyy-MM-dd"));
        //userMasterDto.setExpiryDate("2099-12-31");
        userMasterDto.setFirstLogin("Y");
        userMasterDto.setAnyModify("N");
        userMasterDto.setAttemptNo(0);
        userMasterDto.setPasswordChangeDate(Utility.sysDate("yyyy-MM-dd"));
        userMasterDto.setPasswordPrintDate(Utility.sysDate("yyyy-MM-dd"));
        //userMasterDto.setLastLoginDate("1900-01-01");
        userMasterDto.setLastLoginTime("00:00:00");
        userMasterDto.setUserIdLockDate("1900-01-01");
        userMasterDto.setUserIdLockTime("00:00:00");
        userMasterDto.setOldUserStatus("");
        userMasterDto.setGroupIds("");
        userMasterDto.setGroupIdsDescription("");
        //userMasterDto.setPasswordHash("");
        userMasterDto.setBranchCode("");
        userMasterDto.setNeedToSendEmail("N");
        userMasterDto.setAuth1Status("P");
        //userMasterDto.setAuth1User("SYSTEM");
        userMasterDto.setAuth1Time(Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
        userMasterDto.setAuth2Status("P");
        //userMasterDto.setAuth2User("SYSTEM");
        userMasterDto.setAuth2Time(Utility.sysDate("yyyy-MM-dd hh:mm:ss"));

        return userMasterDto;
    }

    private void loadCreateUserScreen(HttpServletRequest request, HttpServletResponse response) {
        Integer  teamId = null == request.getParameter("teamId") || request.getParameter("teamId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("teamId"));
        try {
            List<AccessUserTypeDto> accessUserTypeList = userManagementService.getAccessUserTypeList();
            List<UserTypeDto> userTypeDtos = userManagementService.getUserTypeList();
            List<DistrictDto> distictList = userManagementService.getDistictList();
            List<UserDto> rteList = assessorAllocationService.getRTEList();
            List<ClaimDepartmentDto> departmentList = userService.getAllDepartments();
            List<AssessorDto> assessorDtoList = userService.getActiveAssessors();
            request.setAttribute("accessUserTypeListMain", accessUserTypeList);
            request.setAttribute("userTypeListMain", userTypeDtos);
            request.setAttribute("departmentListMain", departmentList);
            request.setAttribute("districtListMain", distictList);
            request.setAttribute("rteListMain", rteList);
            request.setAttribute("assessorListMain", assessorDtoList);
            requestDispatcher(request, response, "/admin/user_claim/userAll.jsp");

        } catch (Exception e) {
            LOGGER.error("Error loading user screen: " + e.getMessage(), e);
        }
    }

    private void saveClaimUser(HttpServletRequest request, HttpServletResponse response) {
        try {
            BufferedReader reader = request.getReader();
            UserMasterDto userMasterDto = setData(reader);
            UserMasterDto savedUser = userService.saveClaimUser(userMasterDto);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();

            if (savedUser != null) {
                out.write("{\"status\":\"success\", \"message\":\"User created successfully\", \"userCode\":" + savedUser.getUserCode() + "}");
            } else {
                out.write("{\"status\":\"error\", \"message\":\"Failed to create user\"}");
            }
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error saving user: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (IOException ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }

    private void updateClaimUser(HttpServletRequest request, HttpServletResponse response) {
        try {
            BufferedReader reader = request.getReader();
            UserMasterDto userMasterDto = setData(reader);
            if (userMasterDto.getUserCode() == 0) {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"User code is required for update operation\"}");
                out.flush();
                return;
            }
            UserMasterDto updatedUser = userService.updateClaimUser(userMasterDto);

            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();

            if (updatedUser != null) {
                out.write("{\"status\":\"success\", \"message\":\"User updated successfully\", \"userCode\":" + updatedUser.getUserCode() + "}");
            } else {
                out.write("{\"status\":\"error\", \"message\":\"Failed to update user\"}");
            }
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error updating user: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (IOException ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }

    protected void requestDispatcher(HttpServletRequest request, HttpServletResponse response, String url) {
        try {
            RequestDispatcher rd = request.getRequestDispatcher(url);
            rd.forward(request, response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
