package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.YesNoWantDecideEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

public interface ClaimHandlerDao extends BaseDao<ClaimHandlerDto> {

    String SQL_INSERT_INTO_CLAIM_ASSIGN_ASSESSOR = "INSERT INTO claim_assign_claim_handler\n" + "VALUES\n" + "(0,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?,?,?,?,?,?,?,?,?" + ",?,?)";

    String SEARCH_CLAIMS_BY_N_TXN_NO = "SELECT * FROM claim_assign_claim_handler WHERE N_TXN_NO =?";

    String SEARCH_CLAIMS_BY_CLAIM_NO = "SELECT * FROM claim_assign_claim_handler WHERE N_CLAIM_NO =?";

    String UPDATE_FINANCIAL_INTEREST = "UPDATE claim_assign_claim_handler SET V_FINANCIAL_INTEREST =? ,N_LEASING_REF_NO =? WHERE N_CLAIM_NO =?";

    String UPDATE_LIABILITY_CHECK_LIST = "UPDATE claim_assign_claim_handler SET V_IS_LC_CHK1 =? ,V_IS_LC_CHK2 =? ,V_IS_LC_CHK3 =? ,V_IS_LC_CHK4 =? ,V_IS_LC_CHK5 =? ,V_IS_LC_CHK6 =?,V_IS_LC_CHK7 =?,V_IS_LC_CHK8 =?,V_LC_CHK_USER=?,D_LC_CHK_DATE_TIME=? WHERE N_CLAIM_NO =?";

    String UPDATE_INITIAL_LIABILITY_USER = "UPDATE claim_assign_claim_handler SET V_INIT_LIABILITY_ASSIGN_DATE_TIME =? ,V_INIT_LIABILITY_ASSIGN_USER_ID =? ,V_INIT_LIABILITY_APRV_STATUS =? WHERE N_CLAIM_NO =?";

    String UPDATE_INITIAL_LIABILITY_USER_LIABILITY_PENDING = "UPDATE claim_assign_claim_handler\n" + "SET V_INIT_LIABILITY_ASSIGN_DATE_TIME =?,\n" + " V_INIT_LIABILITY_ASSIGN_USER_ID =?,\n" + " V_INIT_LIABILITY_APRV_STATUS =?,\n" + "V_ASSIGN_USER_ID =NULL,\n" + "V_LIABILITY_APRV_ASSIGN_USER = NULL\n" + "WHERE\n" + "	N_CLAIM_NO =?";

    String UPDATE_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET D_ASSIGN_DATE_TIME =? ,V_ASSIGN_USER_ID =? ,V_ASSIGN_STATUS =?,V_LIABILITY_APRV_ASSIGN_USER=?,D_LIABILITY_APRV_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO =?";

    String UPDATE_CLAIM_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET D_ASSIGN_DATE_TIME =? ,V_ASSIGN_USER_ID =?,V_LIABILITY_APRV_ASSIGN_USER=? WHERE N_CLAIM_NO =?";

    String UPDATE_LIABILITY_APRV_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET D_LIABILITY_APRV_ASSIGN_DATE_TIME =? ,V_LIABILITY_APRV_ASSIGN_USER =? WHERE N_CLAIM_NO = ?";

    String UPDATE_DECISION_MAKING_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET D_DECISION_MAKING_ASSIGN_DATE_TIME = ? ,V_DECISION_MAKING_ASSIGN_USER_ID = ? WHERE N_CLAIM_NO =?";

    String UPDATE_REPUDIATED_TYPE = "UPDATE claim_assign_claim_handler SET N_REPUDIATED_TYPE = ? WHERE N_CLAIM_NO =?";

    String UPDATE_INITIAL_LIABILITY_APRV_STATUS = "UPDATE claim_assign_claim_handler SET D_INIT_LIABILITY_APRV_DATE_TIME =?,V_INIT_LIABILITY_APRV_USER_ID =? ,V_INIT_LIABILITY_APRV_STATUS =? WHERE N_CLAIM_NO =?";

    String UPDATE_LIABILITY_APRV_STATUS = "UPDATE claim_assign_claim_handler SET D_LIABILITY_APRV_DATE_TIME =?,V_LIABILITY_APRV_USER =? ,V_LIABILITY_APRV_STATUS =? WHERE N_CLAIM_NO =?";

    String UPDATE_CLAIM_STATUS = "UPDATE claim_assign_claim_handler SET N_CLAIM_STATUS = ? WHERE N_CLAIM_NO = ?";

    String UPDATE_ASSIGN_STATUS = "UPDATE claim_assign_claim_handler SET V_ASSIGN_STATUS = ? WHERE N_CLAIM_NO = ?";

    String UPDATE_LOSS_TYPE = "UPDATE claim_assign_claim_handler SET N_LOSS_TYPE = ? WHERE N_CLAIM_NO = ?";

    String UPDATE_STORE_STATUS = "UPDATE claim_assign_claim_handler SET V_IS_FILE_STORE = ? WHERE N_CLAIM_NO = ?";

    String UPDATE_STORE_USER = "UPDATE claim_assign_claim_handler SET V_IS_FILE_STORE = ?, V_FILE_USER_STORE_USER_ID = ?, D_FILE_STORE_DATE_TIME = ? WHERE N_CLAIM_NO = ?";


    String UPDATE_INITIAL_LIABILITY_APRV_DETAILS = "UPDATE claim_assign_claim_handler SET D_INIT_LIABILITY_APRV_DATE_TIME =? ,V_INIT_LIABILITY_APRV_USER_ID=?, WHERE V_INIT_LIABILITY_APRV_USER_ID =?";

    String UPDATE_IS_ALL_DOC_UPLOAD_DOCS = "UPDATE claim_assign_claim_handler SET " + "V_IS_ALL_DOC_UPLOAD = ?WHERE N_CLAIM_NO = ?";
    String UPDATE_IS_CHECK_ALL_MND_DOCS = "UPDATE claim_assign_claim_handler SET " + "V_IS_CHECK_ALL_MND_DOCS=? WHERE N_CLAIM_NO = ?";

    String SQL_SELECT_ALL_TO_GRID = "SELECT\n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.D_ACCID_DATE,\n" + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t1.V_PRIORITY,\n" + "t2.V_ASSIGN_USER_ID,\n" + "t2.D_ASSIGN_DATE_TIME,\n" + "t2.V_IS_ALL_DOC_UPLOAD,\n" + "t2.V_IS_CHECK_ALL_MND_DOCS,\n" + "t2.V_LIABILITY_APRV_STATUS,\n" + "t2.N_CLAIM_STATUS,\n" + "t3.v_status_desc,\n" + "t2.N_TXN_NO,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.N_RESERVE_AMOUNT,\n" + "t2.V_IS_FILE_STORE,\n" + "t2.N_LOSS_TYPE,\n" + "t2.V_ASSIGN_USER_ID,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.V_INVESTIGATION_STATUS,\n" + "t2.V_IS_DOUBT,\n" + "t2.V_IS_ON_SITE_OFFER,\n" + "t2.N_APRV_ADVANCE_AMOUNT,\n" + "t2.V_REOPEN_TYPE,\n" + "t2.V_CLOSE_USER,\n" + "t2.D_CLOSE_DATE_TIME,\n" + "t2.V_CLOSE_STATUS\n" + "FROM\n" + "claim_claim_info_main AS t1\n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id ";

    String SQL_SELECT_ALL_TO_L_PANEL = "SELECT\n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_ISF_CLAIM_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t1.D_ACCID_DATE,\n" + "t2.N_REPUDIATED_TYPE,\n" + "t2.V_DECISION_MAKING_ASSIGN_USER_ID,\n" + "t2.D_ASSIGN_DATE_TIME,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.V_IS_REJECTION_ATTACHED,\n" + "t2.N_REPUDIATED_LETTER_TYPE,\n" + "t3.V_REPUDIATE_LETTER_TYPE_DESC\n" + "FROM\n" + "claim_claim_info_main AS t1\n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" + "INNER JOIN claim_repudiated_letter_type AS t3 ON t2.N_REPUDIATED_LETTER_TYPE = t3.N_REPUDIATE_LETTER_TYPE ";

    String SQL_COUNT_LETTER_PANEL = "SELECT\n" + "count(*) as cnt\n" + "FROM \n" + "claim_claim_info_main AS t1\n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO";


    String SQL_SELECT_ALL_TO_SUPPLIER_GRID = "SELECT\n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.D_ACCID_DATE,\n" + "t1.V_PRIORITY,\n" + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t2.D_ASSIGN_DATE_TIME,\n" + "t2.V_IS_ALL_DOC_UPLOAD,\n" + "t2.V_IS_CHECK_ALL_MND_DOCS,\n" + "t2.V_LIABILITY_APRV_STATUS,\n" + "t2.N_CLAIM_STATUS,\n" + "t3.v_status_desc,\n" + "t2.N_TXN_NO,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.N_RESERVE_AMOUNT,\n" + "t2.V_IS_FILE_STORE,\n" + "t2.N_LOSS_TYPE,\n" + "t2.V_ASSIGN_USER_ID,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.V_INVESTIGATION_STATUS,\n" + "t2.V_IS_DOUBT,\n" + "t2.V_IS_ON_SITE_OFFER,\n" + "t2.N_APRV_ADVANCE_AMOUNT,\n" + "t2.V_REOPEN_TYPE,\n" + "t2.V_CLOSE_STATUS,\n" + "t2.V_CLOSE_USER,\n" + "t2.D_CLOSE_DATE_TIME,\n" + "t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME,\n" + "t4.v_supply_order_status\n" + "FROM  \n" + "claim_claim_info_main AS t1  \n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id \n" + "LEFT JOIN claim_supply_order_summary AS t4 ON t2.N_CLAIM_NO = t4.n_claim_no  ";

    String SELECT_ALL_CLAIM_AND_CALSHEET_TO_SUPPLIER_GRID = "SELECT  \n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.D_ACCID_DATE,\n" + "t1.V_PRIORITY,\n" + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t2.D_ASSIGN_DATE_TIME,\n" + "t2.V_IS_ALL_DOC_UPLOAD,\n" + "t2.V_IS_CHECK_ALL_MND_DOCS,\n" + "t2.V_LIABILITY_APRV_STATUS,\n" + "t2.N_CLAIM_STATUS,\n" + "t3.v_status_desc,\n" + "t2.N_TXN_NO,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.N_RESERVE_AMOUNT,\n" + "t2.V_IS_FILE_STORE,\n" + "t2.N_LOSS_TYPE,\n" + "t2.V_ASSIGN_USER_ID,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.V_INVESTIGATION_STATUS,\n" + "t2.V_IS_DOUBT,\n" + "t2.V_IS_ON_SITE_OFFER,\n" + "t2.N_APRV_ADVANCE_AMOUNT,\n" + "t2.V_REOPEN_TYPE,\n" + "t2.V_CLOSE_STATUS,\n" + "t2.V_CLOSE_USER,\n" + "t2.D_CLOSE_DATE_TIME,\n" + "t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME,\n" + "t5.v_supply_order_status\n" + "FROM  \n" + "claim_claim_info_main AS t1  \n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id \n" + "LEFT JOIN claim_calculation_sheet_main AS t4 ON t4.N_CLAIM_NO = t2.N_CLAIM_NO \n" + "LEFT JOIN claim_supply_order_summary AS t5 ON t2.N_CLAIM_NO = t5.n_claim_no";

    String SQL_COUNT_SUPPLIER = "SELECT  \n" + "count(*) as cnt\n" + " FROM  \n" + " claim_claim_info_main AS t1  \n" + " INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + " INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id \n" + " LEFT JOIN claim_supply_order_summary AS t4 ON t2.N_CLAIM_NO = t4.n_claim_no   ";

    String SQL_COUNT_CLAIM_HANDLER = "SELECT\n" + "count(*) as cnt\n" + "FROM\n" + "claim_claim_info_main AS t1\n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO \n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id ";

    String SQL_SELECT_SCRUTINIZING_LIST = "SELECT\n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.D_ACCID_DATE,\n" + "t1.V_PRIORITY," + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t2.V_IS_ALL_DOC_UPLOAD,\n" + "t2.V_IS_CHECK_ALL_MND_DOCS,\n" + "t2.V_LIABILITY_APRV_STATUS,\n" + "t2.N_CLAIM_STATUS,\n" + "t3.v_status_desc,\n" + "t2.N_TXN_NO,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.N_RESERVE_AMOUNT,\n" + "t2.V_IS_FILE_STORE,\n" + "t2.N_LOSS_TYPE,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.V_INVESTIGATION_STATUS,\n" + "t2.V_CLOSE_STATUS\n" + "FROM   \n" + "claim_claim_info_main AS t1   \n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO   \n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + "LEFT JOIN claim_supply_order_summary AS t4 ON t1.N_CLIM_NO = t4.n_claim_no";

    String SQL_SELECT_SCRUTINIZING_CLAIM_AND_CALSHEET_LIST = "SELECT\n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.D_ACCID_DATE,\n" + "t1.V_PRIORITY,\n" + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t2.V_IS_ALL_DOC_UPLOAD,\n" + "t2.V_IS_CHECK_ALL_MND_DOCS,\n" + "t2.V_LIABILITY_APRV_STATUS,\n" + "t2.N_CLAIM_STATUS,\n" + "t3.v_status_desc,\n" + "t2.N_TXN_NO,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.N_RESERVE_AMOUNT,\n" + "t2.V_IS_FILE_STORE,\n" + "t2.N_LOSS_TYPE,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.V_INVESTIGATION_STATUS,\n" + "t2.V_CLOSE_STATUS\n" + "FROM\n" + "claim_claim_info_main AS t1   \n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO   \n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + "LEFT JOIN claim_calculation_sheet_main AS t4 ON t2.N_CLAIM_NO = t4.N_CLAIM_NO \n" + "LEFT JOIN claim_supply_order_summary AS t5 ON t1.N_CLIM_NO = t5.n_claim_no";

    String SQL_SELECT_SCRUTINIZING_COUNT = "SELECT\n" + "\n" + "  count(*) as cnt  \n" + "  FROM   \n" + "  claim_claim_info_main AS t1   \n" + "  INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO   \n" + "  INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + "  LEFT JOIN claim_supply_order_summary AS t4 ON t1.N_CLIM_NO = t4.n_claim_no";


    String SQL_SELECT_LEASING_COMPANY_DETAILS = "SELECT * FROM claim_leasing_companies";

    String SQL_SELECT_PANEL_USER_IDS = "SELECT V_USER_ID FROM claim_claim_panel_user WHERE V_USER_STATUS = 'A' AND N_PANEL_ID = ?";

    String SQL_UPDATED_CLAIM_HANDLER_AMOUNT_BY_CLAIM_NO = "UPDATE claim_assign_claim_handler SET N_RESERVE_AMOUNT=N_RESERVE_AMOUNT-? AND N_APRV_TOT_ACR_AMOUNT=N_APRV_TOT_ACR_AMOUNT-? WHERE N_CLAIM_NO=?";
    String SQL_UPDATED_RESEVE_AMOUNT_FROM_RTE_BY_CLAIM_NO = "UPDATE claim_assign_claim_handler SET N_RESERVE_AMOUNT=? , N_RESERVE_AMOUNT_AFTER_APRV=? , N_APRV_TOT_ACR_AMOUNT=?, V_ACR_APRV_USER=? WHERE N_CLAIM_NO=?";
    String SQL_UPDATED_RESEVE_AMOUNT_CLAIM_NO = "UPDATE claim_assign_claim_handler SET N_RESERVE_AMOUNT=? , N_RESERVE_AMOUNT_AFTER_APRV=? WHERE N_CLAIM_NO=?";
    String SQL_UPDATE_REFERENCE_TWO_RESERVE_AMOUNT_CLAIM_NO = "UPDATE claim_reference_two_reserve_handler SET N_RESERVE_AMOUNT=? , N_RESERVE_AMOUNT_AFTER_APRV=? WHERE N_CLAIM_NO=? AND N_CAL_SHEET_TYPE = ?";
    String SQL_INSERT_REFERENCE_TWO_RESERVE_AMOUNT_CLAIM_NO = "INSERT INTO `claim_reference_two_reserve_handler` VALUES (0,?,?,?,?,?,?);";
    String SQL_APPROVED_UPDATED_RESEVE_AMOUNT_CLAIM_NO = "UPDATE claim_assign_claim_handler SET N_RESERVE_AMOUNT_AFTER_APRV=N_RESERVE_AMOUNT_AFTER_APRV-? WHERE N_CLAIM_NO=?";
    String SQL_APPROVED_UPDATED_REFERENCE_TWO_RESEVE_AMOUNT_CLAIM_NO = "UPDATE claim_reference_two_reserve_handler SET N_RESERVE_AMOUNT_AFTER_APRV=N_RESERVE_AMOUNT_AFTER_APRV-? WHERE N_CLAIM_NO=?";

    String SQL_SELECT_TO_PANEL_LIST = "SELECT\n" + "\n" + " t1.*,t2.*,t3.*\n" + " FROM\n" + " claim_claim_info_main AS t1  \n" + " INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + " INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + " INNER JOIN claim_panel_assign_user AS t4 ON t2.N_CLAIM_NO = t4.N_CLAIM_NO";


    String SQL_SELECT_COUNT_TO_PANEL_LIST = "SELECT\n" + "COUNT(*) as cnt \n" + " FROM\n" + " claim_claim_info_main AS t1  \n" + " INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + " INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + " INNER JOIN claim_panel_assign_user AS t4 ON t2.N_CLAIM_NO = t4.N_CLAIM_NO";

    String UPDATE_ACR_VALUE = "UPDATE claim_assign_claim_handler SET N_APRV_TOT_ACR_AMOUNT = ? WHERE N_CLAIM_NO = ?";

    String UPDATE_PENALTITY_VALUE = "UPDATE claim_assign_claim_handler SET N_PENALTY_UNDER_INSURCE = ? ,N_PENALTY_UNDER_INSURCE_RATE=? WHERE N_CLAIM_NO = ?";

    String UPDATE_UNDER_PENALTITY_VALUE = "UPDATE claim_assign_claim_handler SET N_PENALTY_BALD_TYRE = ? ,N_PENALTY_BALD_TYRE_RATE=? WHERE N_CLAIM_NO = ?";
    String UPDATE_APRV_ADVANCE_AMOUNT = "UPDATE claim_assign_claim_handler SET N_APRV_ADVANCE_AMOUNT = ? WHERE N_CLAIM_NO = ?";
    String UPDATE_IS_DOUBT = "UPDATE claim_assign_claim_handler SET V_IS_DOUBT = ? WHERE N_CLAIM_NO = ?";

    String SQL_SELECT_USER_LIST = "SELECT v_usrid FROM usr_mst \n" + "WHERE n_accessusrtype IN (22,23,24,25,26,27,28,40,41,42,43,44,45,46,60,61,62,63,103)\n" + "AND v_usrstatus NOT IN ('D','C')\n" + "ORDER BY v_usrid ASC";
    String SQL_SELECT_USER_LIST_BY_IN_ACCESS_TYPE = "SELECT v_usrid FROM usr_mst WHERE n_accessusrtype ";

    String UPDATE_SPECIAL_COMMENT_BY_CLAIM_NO = "UPDATE claim_assign_claim_handler SET V_SPECIAL_APPROVAL_USER_ID = ?," + "V_SPECIAL_APPROVAL_DATE_TIME=?,V_SPECIAL_APPROVAL_INPUT_USER_ID=?,V_SPECIAL_APPROVAL_INPUT_DATE_TIME=?," + "N_OLD_CLAIM_STATUS=N_CLAIM_STATUS,N_CLAIM_STATUS = 57 " + "WHERE N_CLAIM_NO = ?";

    String UPDATE_SWAP_CLAIM_STATUS_BY_CLAIM_NO = "UPDATE claim_assign_claim_handler SET N_CLAIM_STATUS=N_OLD_CLAIM_STATUS,N_OLD_CLAIM_STATUS = 0 " + " WHERE N_CLAIM_NO = ?";

    String UPDATE_FORWARD_SUPPLIER_ORDER_WITH_USER = "UPDATE claim_assign_claim_handler SET V_SUPPLY_ORDER_ASSIGN_USER = ?,V_SUPPLY_ORDER_ASSIGN_STATUS=?,D_SUPPLY_ORDER_ASSIGN_DATE_TIME=?,V_IS_GENARATE_SUPPLY_ORDER='N'  WHERE N_CLAIM_NO = ?";

    String UPDATE_FORWARD_SUPPLIER_ORDER = "UPDATE claim_assign_claim_handler SET V_SUPPLY_ORDER_ASSIGN_STATUS=?,D_SUPPLY_ORDER_ASSIGN_DATE_TIME=?,V_IS_GENARATE_SUPPLY_ORDER='N'  WHERE N_CLAIM_NO = ?";

    String UPDATE_FORWARD_SUPPLIER_ORDER_STATUS = "UPDATE claim_assign_claim_handler SET V_SUPPLY_ORDER_ASSIGN_STATUS=?,V_IS_GENARATE_SUPPLY_ORDER='Y'  WHERE N_CLAIM_NO = ?";

    String UPDATE_LABOUR_COST_AND_PART_COST_VALUE = "UPDATE claim_assign_claim_handler SET N_LABOUR_COST = ?,N_PART_COST = ? WHERE N_CLAIM_NO = ?";

    String SQL_SELECT_IS_LIABLITY_CHECKED = "SELECT V_LC_CHK_USER FROM claim_assign_claim_handler WHERE  N_CLAIM_NO=?";

    String UPDATE_CLAIM_TO_CLOSE = "UPDATE claim_assign_claim_handler SET V_CLOSE_STATUS = ?,V_CLOSE_USER=?,D_CLOSE_DATE_TIME=?  WHERE N_CLAIM_NO = ?";

    String UPDATE_CLAIM_TO_REOPEN = "UPDATE claim_assign_claim_handler SET N_REOPEN_NO_OF_TIME=N_REOPEN_NO_OF_TIME+1, V_CLOSE_STATUS = ?,V_REOPEN_TYPE=?,V_REOPEN_USER_ID=?,D_REOPEN_DATE_TIME=? WHERE N_CLAIM_NO = ?";

    String UPDATE_ONSITE_OFFER_TYPE = "UPDATE claim_assign_claim_handler SET V_IS_ON_SITE_OFFER=? WHERE N_CLAIM_NO =?";

    String SELECT_USERS_BY_EMP_NO = "SELECT * FROM usr_mst WHERE v_emp_no=?";

    String UPDATE_CLAIM_STATUS_FILE_STATUS = "UPDATE claim_assign_claim_handler SET V_IS_FILE_STORE =?, N_CLAIM_STATUS=? WHERE N_CLAIM_NO=?";

    String SELECT_UPLOADED_DOCUMENTS = "SELECT\n" + "DISTINCT(t1.N_DOC_TYPE_ID) AS N_DOC_TYPE_ID,\n" + "t1.N_CLAIM_NO\n" + "FROM\n" + "claim_documents_claim_wise AS t1\n" + "INNER JOIN claim_upload_documents AS t2 ON t1.N_CLAIM_NO = t2.N_CLIM_NO  AND t1.N_DOC_TYPE_ID=t2.N_DOC_TYPE_ID\n" + "WHERE\n" + "t1.N_CLAIM_NO = ? AND\n" + "t1.V_IS_MANDATORY = 'Y' AND\n" + "t2.V_DOC_STATUS IN ('A', 'P')";

    String SELECT_IS_AVAILABLE_ONSITE_OFFSITE = "SELECT claim_no FROM claim_assign_assesor WHERE insepction_id IN(1,2) AND claim_no =? AND record_status NOT IN (4, 23)";

    String SELECT_IS_AVAILABLE_PENDING_ONSITE_OR_OFFSITE = "SELECT claim_no FROM claim_assign_assesor WHERE insepction_id IN(1,2) AND claim_no =? AND record_status NOT IN(4, 9, 23)";

    String SELECET_IS_INITIAL_LIABILITY = "select N_CLAIM_NO FROM claim_assign_claim_handler where V_INIT_LIABILITY_APRV_STATUS IN('R','H','F','P','A') and N_CLAIM_NO =?";

    String UPDATE_CLAIM_STATUS_FILE_REOPEN = "UPDATE claim_assign_claim_handler SET V_ASSIGN_USER_ID =?,V_ASSIGN_STATUS=?,D_ASSIGN_DATE_TIME=?, " + "V_LIABILITY_APRV_ASSIGN_USER=?,V_LIABILITY_APRV_STATUS=?,D_LIABILITY_APRV_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO=?";

    String UPDATE_CLAIM_ASSIGN_USER_BY_INSPECTION_ID = "UPDATE claim_inspection_info_main SET v_assign_rte_user=?, d_assign_rte_datetime = ? WHERE n_ref_no=?";
    String SELECT_ASSIGNED_USER_BY_INSPECTION_ID = "SELECT v_assign_rte_user FROM claim_inspection_info_main WHERE n_ref_no = ?";
    String UPDATE_CLAIM_APPROVE_ASSIGN_USER_BY_INSPECTION_ID = "UPDATE claim_inspection_info_main SET v_assign_rte_user=?, d_assign_rte_datetime = ?  WHERE n_ref_no=?";

    String UPDATE_PENDING_CLAIM_APPROVE_ASSIGN_USER_BY_INSPECTION_ID = "UPDATE claim_inspection_info_main SET v_approve_assign_rte_user=?, d_approve_assign_datetime = ?, n_record_status = ?  WHERE n_ref_no=?";

    String SELECT_THEFT_CLAIMS = "SELECT\n" + "\tt2.N_POL_REF_NO,\n" + "\tt2.N_CLIM_NO,\n" + "\tt2.V_VEHICLE_NO,\n" + "\tt2.V_COVER_NOTE_NO,\n" + "\tt2.D_ACCID_DATE,\n" + "\tt3.V_PRODUCT,\n" + "\tt3.V_CATEGORY_DESC \n" + "FROM\n" + "  claim_claim_info_main AS t2\n" + "\tINNER JOIN claim_vehicle_info_main AS t3 ON t2.N_POL_REF_NO = t3.N_POL_REF_NO \n" + "WHERE\n" + "\tDATEDIFF(?,D_DATE_OF_REPORT) = ? AND N_CAUSE_OF_LOSS=4";
    String SELECT_APPROVE_TOTAL_ACR = "SELECT N_RESERVE_AMOUNT,N_RESERVE_AMOUNT_AFTER_APRV,N_APRV_TOT_ACR_AMOUNT FROM claim_assign_claim_handler WHERE N_CLAIM_NO=?";

    String SELECT_MOBILE_NO_BY_ASSIGN_USER_ID = "SELECT v_mobile FROM usr_mst WHERE v_usrid=?";

    String UPDATE_EXCESS_INCLUDE_BY_CLAIM_NO = "UPDATE claim_assign_claim_handler SET V_IS_EXCESS_INCLUDE=? WHERE N_CLAIM_NO=? ";

    String UPDATE_ASSIGN_USER_AND_ASSIGN_DATE = "UPDATE claim_assign_claim_handler\n" + "SET V_ASSIGN_USER_ID =?, V_ASSIGN_STATUS ='A', D_ASSIGN_DATE_TIME =?\n" + "WHERE\n" + "	N_CLAIM_NO =?";

    String SQL_IS_INIT_LIABILITY_REQUEST = "SELECT\n" + "	1 \n" + "FROM\n" + "	claim_assign_claim_handler AS t1\n" + "	INNER JOIN usr_mst AS t2 ON t1.V_INIT_LIABILITY_ASSIGN_USER_ID = t2.v_usrid \n" + "WHERE\n" + "	N_CLAIM_NO = ? \n" + "	AND t1.V_INIT_LIABILITY_APRV_USER_ID = ?\n" + "	AND t2.n_accessusrtype = '40' \n" + "	AND t1.V_INIT_LIABILITY_APRV_STATUS = 'A'";

    String UPDATE_ASSIGN_USER_ALREADY_LIABILITY_APPROVED = "UPDATE claim_assign_claim_handler \n" + "SET D_ASSIGN_DATE_TIME =?,\n" + "V_ASSIGN_USER_ID =?,\n" + "V_ASSIGN_STATUS =?\n" + "WHERE\n" + "	N_CLAIM_NO =?";

    String UPDATE_RESERVE_AMOUNT_AFTER_APPROVED_BY_CLAIM_NO = "UPDATE claim_assign_claim_handler \n" + "SET N_RESERVE_AMOUNT_AFTER_APRV = N_RESERVE_AMOUNT_AFTER_APRV +? \n" + "WHERE\n" + "	N_CLAIM_NO =?";
    String UPDATE_REFERENCE_TWO_RESERVE_AMOUNT_AFTER_APPROVED_BY_CLAIM_NO = "UPDATE claim_reference_two_reserve_handler \n" + "SET N_RESERVE_AMOUNT_AFTER_APRV = N_RESERVE_AMOUNT_AFTER_APRV +? \n" + "WHERE\n" + "	N_CLAIM_NO =?";

    String SELECT_RESERVE_AMOUNT_AFTER_APPROVED_BY_CLAIM_NO = "SELECT N_RESERVE_AMOUNT_AFTER_APRV FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String SELECT_RESERVE_AMOUNT_BY_CLAIM_NO = "SELECT N_RESERVE_AMOUNT FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";
    String SELECT_REFERENCE_TWO_RESERVE_AMOUNT_BY_CLAIM_NO = "SELECT N_RESERVE_AMOUNT FROM claim_reference_two_reserve_handler WHERE N_CLAIM_NO = ?";


    String UPDATE_RESERVE_AMOUNT_AND_AFTER_APPROVED = "UPDATE claim_assign_claim_handler \n" + "SET N_RESERVE_AMOUNT = ?,\n" + "N_RESERVE_AMOUNT_AFTER_APRV =? \n" + "WHERE\n" + "	N_CLAIM_NO =?";

    String UPDATE_REFERENCE_TWO_RESERVE_AMOUNT_AND_AFTER_APPROVED = "UPDATE claim_reference_two_reserve_handler \n" + "SET N_RESERVE_AMOUNT = ?,\n" + "N_RESERVE_AMOUNT_AFTER_APRV =? \n" + "WHERE\n" + "	N_CLAIM_NO =?";

    String SELECT_APRV_ADVANCE_AMOUNT_BY_CLAIM_NO = "SELECT N_APRV_ADVANCE_AMOUNT FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String UPDATE_V_IS_PROVIDE_OFFER_BY_N_CLAIM_NO = "UPDATE claim_assign_claim_handler SET V_IS_PROVIDE_OFFER = ? WHERE N_CLAIM_NO = ?";

    String UPDATE_INIT_LIABILITY_AND_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET D_ASSIGN_DATE_TIME =? ,V_ASSIGN_USER_ID =?,V_INIT_LIABILITY_ASSIGN_DATE_TIME = ?,V_INIT_LIABILITY_ASSIGN_USER_ID =?,V_INIT_LIABILITY_APRV_STATUS =?,V_LIABILITY_APRV_ASSIGN_USER=? WHERE N_CLAIM_NO =?";

    String SELECT_IS_PROVIDE_OFFER_BY_CLAIM_NO = "SELECT V_IS_PROVIDE_OFFER FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String GET_ENGINEER_APPROVED_AMOUNT_BY_CLAIM_NO = "SELECT N_ENGINEER_APRV_AMOUNT FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String UPDATE_ENGINEER_APPROVED_AMOUNT = "UPDATE claim_assign_claim_handler SET N_ENGINEER_APRV_AMOUNT = ? WHERE N_CLAIM_NO = ?";

    String UPDATE_LETTER_PANEL_USER = "UPDATE claim_assign_claim_handler SET V_LETTER_PANEL_USER_ID = ?,D_LETTER_PANEL_DATETIME = ? WHERE N_CLAIM_NO = ?";


    String SQL_SELECT_ADVANCE_FORWARD_LIST = "SELECT  \n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.D_ACCID_DATE,\n" + "t1.V_PRIORITY,\n" + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t2.D_ASSIGN_DATE_TIME,\n" + "t2.V_IS_ALL_DOC_UPLOAD,\n" + "t2.V_IS_CHECK_ALL_MND_DOCS,\n" + "t2.V_LIABILITY_APRV_STATUS,\n" + "t2.N_CLAIM_STATUS,\n" + "t3.v_status_desc,\n" + "t2.N_TXN_NO,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.N_RESERVE_AMOUNT,\n" + "t2.V_IS_FILE_STORE,\n" + "t2.N_LOSS_TYPE,\n" + "t2.V_ASSIGN_USER_ID,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.V_INVESTIGATION_STATUS,\n" + "t2.V_IS_DOUBT,\n" + "t2.V_IS_ON_SITE_OFFER,\n" + "t2.N_APRV_ADVANCE_AMOUNT,\n" + "t2.V_REOPEN_TYPE,\n" + "t2.V_CLOSE_STATUS,\n" + "t2.V_CLOSE_USER,\n" + "t2.D_CLOSE_DATE_TIME\n" + "FROM  \n" + "claim_claim_info_main AS t1  \n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id";

    String SQL_COUNT_ADVANCE_FORWARD_LIST = "SELECT  \n" + "COUNT(t1.N_CLIM_NO) AS cnt\n" + "FROM  \n" + "claim_claim_info_main AS t1  \n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n";

    String UPDATE_ADVANCE_REQUEST = "UPDATE claim_assign_claim_handler SET V_ADVANCE_APPROVAL_ASSIGN_USER = ?, D_ADVANCE_APPROVAL_ASSIGN_DATE_TIME = ?, V_ADVANCE_STATUS = ?, V_ADVANCE_FORWARDED_USER = ?, N_APPROVAL_PENDING_ADVANCE_AMOUNT = ?, V_ADVANCE_APPROVED_USER = ?, D_ADVANCE_APPROVED_DATE_TIME = ?, N_CLAIM_STATUS = ? WHERE N_CLAIM_NO = ?";

    String GET_ASSIGNED_USER = "SELECT V_ASSIGN_USER_ID FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String APPROVE_ADVANCE = "UPDATE claim_assign_claim_handler SET N_APRV_ADVANCE_AMOUNT = ?, V_ADVANCE_APPROVED_USER = ?, D_ADVANCE_APPROVED_DATE_TIME = ?, V_ADVANCE_STATUS = ?, N_CLAIM_STATUS = ? WHERE N_CLAIM_NO = ?";

    String SELECT_ADVANCE_ASSIGN_USER = "SELECT V_ADVANCE_APPROVAL_ASSIGN_USER FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String SELECT_ADVANCE_FORWARDED_USER = "SELECT cach.V_ADVANCE_FORWARDED_USER, um.n_accessusrtype\n" + "FROM claim_assign_claim_handler cach\n" + "INNER JOIN usr_mst as um ON cach.V_ADVANCE_FORWARDED_USER = um.v_usrid\n" + "WHERE cach.N_CLAIM_NO = ?";

    String RECALL_ADVANCE = "UPDATE claim_assign_claim_handler SET V_ADVANCE_APPROVAL_ASSIGN_USER = ?, D_ADVANCE_APPROVAL_ASSIGN_DATE_TIME = ?, V_ADVANCE_STATUS = ?, N_CLAIM_STATUS = ? WHERE N_CLAIM_NO = ?";

    String SELECT_ADVANCE_APPROVED_USER = "SELECT cach.V_ADVANCE_APPROVED_USER, um.n_accessusrtype\n" + "FROM claim_assign_claim_handler cach\n" + "INNER JOIN usr_mst as um ON cach.V_ADVANCE_APPROVED_USER = um.v_usrid\n" + "WHERE cach.N_CLAIM_NO = ?";

    String GET_N_CLAIM_STATUS_BY_CLAIM = "SELECT N_CLAIM_STATUS FROM claim_assign_claim_handler WHERE N_CLAIM_NO= ?;";

    String UPDATE_OLD_CLAIM_STATUS = "UPDATE claim_assign_claim_handler SET N_OLD_CLAIM_STATUS = ? WHERE N_CLAIM_NO = ?";

    String FORWARD_TO_ENGINEER = "UPDATE claim_assign_claim_handler SET N_OLD_ENGINEER_CLAIM_STATUS = N_CLAIM_STATUS, N_CLAIM_STATUS = ?, V_FORWARDED_ENGINEER = ? WHERE N_CLAIM_NO = ?";

    String GET_CLAIM_FORWARDED_ENGINEER = "SELECT V_FORWARDED_ENGINEER FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String RECALL_CLAIM_FROM_FORWARDED_ENGINEER = "UPDATE claim_assign_claim_handler SET N_CLAIM_STATUS = N_OLD_CLAIM_STATUS, V_FORWARDED_ENGINEER = ? WHERE N_CLAIM_NO = ?";

    String RECALL_CLAIM_FROM_FORWARDED_ENGINEER_WHEN_SPECIAL_COMMENT = "UPDATE claim_assign_claim_handler SET N_CLAIM_STATUS = N_OLD_ENGINEER_CLAIM_STATUS, N_OLD_ENGINEER_CLAIM_STATUS = 0, V_FORWARDED_ENGINEER = ? WHERE N_CLAIM_NO = ?";

    String SELECT_CLAIMS_FOR_ENGINEER = "SELECT\n" + "t1.N_CLIM_NO,\n" + "t1.V_VEHICLE_NO,\n" + "t1.V_POL_NUMBER,\n" + "t1.D_ACCID_DATE,\n" + "t1.V_POLICY_CHANNEL_TYPE,\n" + "t1.V_PRIORITY,\n" + "t2.V_ASSIGN_USER_ID,\n" + "t2.D_ASSIGN_DATE_TIME,\n" + "t2.V_IS_ALL_DOC_UPLOAD,\n" + "t2.V_IS_CHECK_ALL_MND_DOCS,\n" + "t2.V_LIABILITY_APRV_STATUS,\n" + "t2.N_CLAIM_STATUS,\n" + "t3.v_status_desc,\n" + "t2.N_TXN_NO,\n" + "t2.N_APRV_TOT_ACR_AMOUNT,\n" + "t2.N_RESERVE_AMOUNT,\n" + "t2.V_IS_FILE_STORE,\n" + "t2.N_LOSS_TYPE,\n" + "t2.V_ASSIGN_USER_ID,\n" + "t2.V_LIABILITY_APRV_ASSIGN_USER,\n" + "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" + "t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" + "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" + "t2.V_INVESTIGATION_STATUS,\n" + "t2.V_IS_DOUBT,\n" + "t2.V_IS_ON_SITE_OFFER,\n" + "t2.N_APRV_ADVANCE_AMOUNT,\n" + "t2.V_REOPEN_TYPE,\n" + "t2.V_CLOSE_USER,\n" + "t2.D_CLOSE_DATE_TIME,\n" + "t2.V_CLOSE_STATUS\n" + "FROM\n" + "claim_claim_info_main AS t1\n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + "INNER JOIN rte_pending_claim_detail t4 ON t2.N_CLAIM_NO = t4.claim_no";

    String SQL_COUNT_CLAIMS_FOR_ENGINEER = "SELECT\n" + "count(*) as cnt\n" + "FROM\n" + "claim_claim_info_main AS t1\n" + "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" + "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + "INNER JOIN rte_pending_claim_detail t4 ON t2.N_CLAIM_NO = t4.claim_no";

    String IS_CLAIM_FORWARDED_TO_ENGINEER = "SELECT 1 FROM claim_assign_claim_handler WHERE N_CLAIM_STATUS = ? and N_CLAIM_NO = ?";

    String IS_CLAIM_FORWARDED_TO_SELECTED_ENGINEER = "SELECT 1 FROM claim_assign_claim_handler WHERE N_CLAIM_STATUS = ? and N_CLAIM_NO = ? and V_FORWARDED_ENGINEER = ?";

    String SQL_SELECT_TO_MAIN_PANEL_LIST = "SELECT\n" + "\n" + " t1.*,t2.*,t3.*\n" + " FROM\n" + " claim_claim_info_main AS t1  \n" + " INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + " INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + " INNER JOIN claim_panel_assign_user AS t4 ON t2.N_CLAIM_NO = t4.N_CLAIM_NO\n" + " INNER JOIN claim_claim_panel_user AS t5 ON t4.V_USER_ID = t5.V_USER_ID";


    String SQL_SELECT_COUNT_TO_MAIN_PANEL_LIST = "SELECT\n" + "COUNT(*) as cnt \n" + " FROM\n" + " claim_claim_info_main AS t1  \n" + " INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO  \n" + " INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" + " INNER JOIN claim_panel_assign_user AS t4 ON t2.N_CLAIM_NO = t4.N_CLAIM_NO\n" + " INNER JOIN claim_claim_panel_user AS t5 ON t4.V_USER_ID = t5.V_USER_ID";

    String GET_ASSIGNED_DECISION_MAKER = "SELECT V_DECISION_MAKING_ASSIGN_USER_ID FROM claim_assign_claim_handler WHERE N_CLAIM_NO = ?";

    String IS_ARI_AND_STORED = "SELECT 1\n" + "FROM claim_assign_claim_handler t1\n" + "INNER JOIN request_ari_details t2 ON t1.N_CLAIM_NO = t2.claim_no\n" + "WHERE t1.V_IS_FILE_STORE = 'Y'\n" + "AND t1.N_CLAIM_NO = ?";

    String GET_PANEL_STATUS = "SELECT\n" + "t1.N_CLAIM_NO,\n" + "t2.V_DECISION_MAKING_ASSIGN_USER_ID,\n" + "t1.V_DM_REMARK,\n" + "CASE\t\n" + "WHEN t3.N_REF_NO = 0 THEN 'N/A'\n" + "ELSE t3.V_REPUDIATED_REASON_DESC END AS REPUDIATED_REASON,\n" + "CASE \n" + "WHEN sum(CASE WHEN V_STATUS = 'D' THEN 2 ELSE 0 END) > 1 THEN 'D'\n" + "WHEN sum(CASE WHEN V_STATUS = 'P' THEN 1 ELSE 0 END) > 0 THEN 'P'\n" + "ELSE max(V_STATUS)\n" + "END AS V_STATUS\n" + "FROM claim_panel_assign_user t1\n" + "INNER JOIN claim_assign_claim_handler t2 on t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" + "INNER JOIN claim_repudiated_reason t3 on t2.N_REPUDIATED_TYPE = t3.N_REF_NO\n" + "WHERE t1.N_CLAIM_NO = ?\n" + "GROUP BY\n" + "t1.N_CLAIM_NO\n" + "ORDER BY\n" + "t1.N_ID DESC";

    String GET_ARI_CLAIM_ASSIGNED_USER = "";

    String UPDATE_PANEL_DETAILS = "UPDATE claim_assign_claim_handler SET N_DECISION_APPRV_CLAIM_PANEL = ?, V_CLAIM_PANEL_DECISION = ?, D_CLAIM_PANEL_DECISION_DATE_TIME = ? WHERE N_CLAIM_NO =?";

    String ATTACH_FILE_ON_OTHER_SELECT = "UPDATE claim_assign_claim_handler SET V_IS_REJECTION_ATTACHED = 'Y' WHERE N_CLAIM_NO = ?";

    String CLAIM_REPUDIATED_LETTER_TYPE_LIST = "SELECT\n" + "N_REPUDIATE_LETTER_TYPE,\n" + "V_REPUDIATE_LETTER_TYPE_DESC\n " + "FROM\n " + "claim_repudiated_letter_type\n " + "WHERE\n " + "V_STATUS = ? AND N_REPUDIATE_LETTER_TYPE > 0";

    String GET_REJECTION_REF_NO = "SELECT N_REF_NO\n" + "FROM `claim_upload_documents` WHERE N_CLIM_NO = ? AND N_DOC_TYPE_ID = ? ORDER BY N_REF_NO DESC LIMIT 1;";

    String SELECT_RESERVE_AMOUNT_DETAIL = "SELECT * FROM claim_reference_two_reserve_handler WHERE N_CLAIM_NO = ? and V_CODE = ?";

    String GET_INVESTIGATOR_LIST = "SELECT N_REF_NO, V_CODE, V_NAME, V_EMAIL, N_MOBILE FROM claim_assessor WHERE V_PARA_TYPE = 'INV'";

    String INSERT_CALL_CENTER_CLAIM_CLOSE = "INSERT INTO `call_center_claim_close_detail` (`N_TXN_ID`, `N_CLAIM_NO`, `V_USER`, `V_REASON`, `N_PREVIOUS_CLAIM_STATUS`, `V_PREVIOUS_CLOSE_STATUS`, `V_REMARK`, `D_INPUT_DATE_TIME`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    String SELECT_CALL_CENTER_CLAIM_CLOSE = "SELECT * FROM `call_center_claim_close_detail` WHERE `N_CLAIM_NO` = ?";

    boolean updateFinancialInterest(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    boolean updateLiabilityCheckList(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto updateInitialLiabilityUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto updateAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto updateLiabilityAprvAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto updateInitialLiabilityAprvStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto updateLiabilityAprvStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto updateInitialLiabilityAprvDetails(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    DataGridDto getClaimHandlerDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer priority) throws Exception;

    DataGridDto getLetterPanelList(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception;

    DataGridDto getClaimHandlerDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String calsheetStatus) throws Exception;

    DataGridDto getClaimHandlerSupplierDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) throws Exception;

    List<LeasingCompanyDto> getLeasingCompanyDetails(Connection connection) throws Exception;

    void updateClaimStatus(Connection connection, int claimNo, int status) throws Exception;

    void updateStoreStatus(Connection connection, int claimNo, String status) throws Exception;

    void updateStoreStatusAndUserID(Connection connection, int claimNo, String status, UserDto user) throws Exception;

    void updateDecisionMakingUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateRepudiatedReason(Connection connection, Integer claimNo, Integer repudiatedReason) throws Exception;

    void updateUploadAllDocumentStatus(Connection connection, int claimNo, YesNoWantDecideEnum isAllDocUpload) throws MisynJDBCException;

    void updateCheckedMandatoryDocumentStatus(Connection connection, int claimNo, YesNoWantDecideEnum isCheckAllMndDocs) throws MisynJDBCException;

    List<String> getPanelUserIdList(Connection connection, int type) throws Exception;

    List<ClaimClaimPanelUserDto> searchByUserId(Connection connection, String userId) throws Exception;

    ClaimHandlerDto updateInvestigationCompletedUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto updateInvestigationArrangedUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateInvestigationAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateInvestigationAssignUserAndDecisionMakeUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateInvestigationStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateRejectionUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    ClaimHandlerDto searchMasterByClaimNo(Connection connection, Integer claimNo) throws Exception;

    List<ClaimRepudiatedReasonDto> getRepudiatedList(Connection connection) throws Exception;

    boolean updateReserveAmount(BigDecimal reseveAcr, BigDecimal totAcr, Connection connection, Integer claimNo) throws Exception;

    void updateAssignStatus(Connection connection, int claimNo, String status) throws Exception;

    DataGridDto getClaimHandlerPanelDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) throws Exception;

    void updateReserveAmountFromRTE(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, BigDecimal totAmt, String aprvUser, Connection connection, Integer claimNo) throws Exception;

    void updateReserveAcrAmount(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo) throws Exception;

    void updateReserveAcrAmountAfetrApproved(BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo) throws Exception;

    void updateLossType(Connection connection, int claimNo, String type) throws Exception;

    boolean updateTotAcrAmount(Connection connection, Integer claimNo, BigDecimal amount) throws Exception;

    boolean updatePenaltyAmount(Connection connection, Integer claimNo, BigDecimal amount, BigDecimal penaltyAmount, boolean isBold) throws Exception;

    List<String> getUserListByAccessUserType(Connection connection) throws Exception;

    List<String> searchAllUserByInAccessUserType(Connection connection, String accessTypeList) throws Exception;
    List<String> searchAllUserByInAccessUserType(Connection connection, String accessTypeList,UserDto sessionUser) throws Exception;

    boolean updateSpecialComment(Connection connection, Integer claimNo, String userId, String inputUserId) throws Exception;

    boolean updateSupplierOrderAssignDetails(Connection connection, Integer claimNo, String userId, String status, String assignDateTime) throws Exception;

    boolean updateSupplierOrderAssignStatus(Connection connection, Integer claimNo, String status) throws Exception;

    DataGridDto getClaimHandlerScrutinizingDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) throws Exception;

    boolean updateLabourCostAndPartCostAmount(Connection connection, Integer claimNo, BigDecimal labourCost, BigDecimal partCost) throws Exception;

    boolean updateSwapClaimStatus(Connection connection, Integer claimNo) throws Exception;

    boolean isLiablityChecked(Connection connection, Integer claimNo) throws Exception;

    void updateAdvanceAmount(Connection connection, Integer claimNo, BigDecimal amount) throws Exception;

    void updateDoubtStatus(Connection connection, Integer claimNo, String doubtStatus) throws Exception;

    void updateClaimClose(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateClaimReopen(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateOnsiteOffer(Connection connection, int claimNo, int offerType) throws Exception;

    List<UserDto> getBranchUserList(Connection connection, String empNo);

    void updateClaimStatusFileStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    Map<Integer, Integer> getUploadedDocumentsType(Connection connection, Integer claimNo);

    boolean isAvailableOnsiteOrOffsite(Connection connection, int claimNo);

    void updateClaimHandlerReopen(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    boolean updateClaimAssignUserByrefNo(Connection connection, Integer InspectionId, String assignUser) throws Exception;

    boolean isUserAlreadyAssigned(Connection connection, Integer inspectionId, String assignUser) throws Exception;

    List<ClaimsDto> getTheftClaimsList(Connection connection, String sysDate, Integer days) throws Exception;

    BigDecimal getTotalAcrAmount(Connection connection, int claimNo);

    BigDecimal getReserveAmountByClaimNo(Connection connection, Integer claimNo) throws Exception;

    String getMobileNoByAssignId(Connection connection, String assignId) throws Exception;

    boolean updateExcessIncludeStatus(Connection connection, String status, Integer claimNo) throws Exception;

    void changeAssignUser(Connection connection, String assignUser, int claimNo);

    boolean updateInitialLiabilityUserLiabilityPending(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    boolean isCheckInitLiabilityAssignUserChangeRequest(Connection connection, String user, int claimNo) throws Exception;

    ClaimHandlerDto updateAssignUserIfLiabilityApproved(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception;

    BigDecimal getReserveAmountAfterApproved(Connection connection, Integer claimNo) throws Exception;

    BigDecimal getReserveAmount(Connection connection, Integer claimNo) throws Exception;

    void updateReserveAmountAndReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmount, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception;

    BigDecimal getAdvanceAmount(Connection connection, int claimNo);

    void updateIsProvideOffer(Connection connection, int claimNo, String isProvideOffer) throws Exception;

    void updateClaimAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    void updateInitialLiabilityUserAndAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception;

    boolean isProvideOffer(Connection connection, Integer claimNo);

    boolean isOnsiteOrOffSitePending(Connection connection, int claimNo);

    BigDecimal getEngineerApprovedAmount(Connection connection, Integer claimNo) throws Exception;

    void updateEngineerApprovedAmount(Connection connection, Integer claimNo, BigDecimal payableAmount) throws Exception;

    void updateLetterPanelUser(Connection connection, int claimNo, String user) throws Exception;

    void updateAdvanceRequest(Connection connection, Integer claimNo, UserDto assignUser, UserDto user, BigDecimal advanceAmount) throws Exception;

    String getAssignedClaimHandler(Connection connection, Integer claimNo) throws Exception;

    void approveAdvance(Connection connection, Integer claimNo, BigDecimal advance, UserDto sessionUser) throws Exception;

    void recallAdvance(Connection connection, Integer claimNo, UserDto user) throws Exception;

    DataGridDto getAdvanceForwardList(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String user) throws Exception;

    String getAdvanceApprovalAssignUser(Connection connection, Integer claimNo) throws Exception;

    UserDto getAdvanceForwardedUser(Connection connection, Integer claimNo) throws Exception;

    UserDto getAdvanceApprovedUser(Connection connection, Integer claimNo) throws Exception;

    boolean updateClaimApproveAssignUserByrefNo(Connection connection, Integer refNo, String assignUser, boolean rteApproved) throws Exception;

    boolean checkAndUpdateAdvanceAmount(Connection connection, Integer claimNo, BigDecimal amount) throws Exception;

    Boolean checkClaimStatusByClaimNO(Connection connection, Integer claimNo) throws Exception;

    boolean forwardToEngineer(Connection connection, Integer claimNo, String rteUser) throws Exception;

    String getClaimForwardedEngineer(Connection connection, Integer claimNo) throws Exception;

    boolean recallFromForwardedEngineer(Connection connection, Integer claimNo, Integer oldEngineerClaimStatus) throws Exception;

    DataGridDto getClaimsForEngineerDataGrid(Connection connection, List<FieldParameterDto> parameterList, int drawRendomId, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, boolean equals);

    boolean isClaimForwardedToEngineer(Connection connection, Integer claimNo, String userId) throws Exception;

    String getAssignedDecisionMaker(Connection connection, Integer claimNo) throws Exception;

    boolean isAutoStored(Connection connection, Integer claimNo) throws Exception;

    ClaimPanelDto populatePanelStatus(Connection connection, Integer claimNo) throws Exception;

    String getARIRequestedFileAssignUser(Connection connection, int claimNo) throws Exception;

    void updateClaimPanel(Connection connection, Integer claimNo, String panelId, String status) throws Exception;

    List<ClaimRepudiatedLetterTypeDto> getActiveRejectionReasons(Connection connection, String activeStatus);

    void attachRejectionFileOnOtherSelect(Connection connection, Integer claimNo) throws Exception;

    int getRejectionRefNo(Connection connection, Integer claimNo, Integer rejectoinDocTypeId) throws Exception;

    Integer getClaimStatusByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void updateOldClaimStatus(Connection connection, int claimNo, int status) throws Exception;

    void updateReferenceTwoReserveAcrAmount(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo, int type) throws Exception;

    void insertReferenceTwoReserveAcrAmount(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo, Integer calSheet, String code) throws Exception;

    BigDecimal getReferenceTwoReserveAmount(Connection connection, Integer claimNo) throws Exception;

    void updateReferenceTwoReserveAcrAmountAfetrApproved(BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo) throws Exception;

    void updateReferenceTwoReserveAmountAndReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmount, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception;

    void updateReferenceTwoReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception;

    CalSheetTypeDto getReferenceTwoReserve(Connection connection, Integer claimNo, Integer calSheetType) throws Exception;

    List<InvestigatorDetailsDto> getInvestigatorList(Connection connection) throws Exception;

    void updateCallCenterClaimClose(Connection connection, ClaimHandlerDto claimHandlerDto,String remark,UserDto user) throws Exception;

    String getCallCenterClaimClose(Connection connection, Integer claimno) throws Exception;
}
