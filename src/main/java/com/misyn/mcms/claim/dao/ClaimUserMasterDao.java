package com.misyn.mcms.claim.dao;

import com.misyn.mcms.admin.admin.dto.AccessUserTypeDto;
import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.util.List;

public interface ClaimUserMasterDao extends BaseDao<UserMasterDto> {
    String CLAIM_USER_INSERT = "INSERT INTO usr_mst (" +
            "n_usrcode, n_comid, v_usrid, v_password, v_oldpassword, v_usrtypes, n_accessusrtype, " +
            "v_title, v_firstname, v_lastname, v_address1, v_address2, v_email, " +
            "v_land_phone, v_mobile, v_fax, v_nic, v_emp_no, n_brid, " +
            "d_activedate, d_expirydate, v_usrstatus, v_oldusrstatus, d_lastlogindate, " +
            "d_lastlogintime, n_atmptno, d_pwchgdate, d_pwprtdate, v_firstlogin, " +
            "d_uidlockdate, d_uidlocktime, v_anymodify, v_group_ids, v_group_ids_desc, v_usrtype_desc, " +
            "v_password_hash, V_REPORT_TO, N_TEAM_ID, N_LIABLITY_LIMIT, N_PAYMENT_LIMIT, " +
            "N_RESERVE_LIMIT, N_PAYMENT_AUTH_LIMIT, v_inpstat, v_inpuser, d_inptime, " +
            "v_auth1user, v_auth1stat, d_auth1time, v_auth2stat, v_auth2user, d_auth2time, " +
            "assessor_type, branch_code, need_to_send_email, n_auth_level, v_district_code" +
            ") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String CLAIM_USER_UPDATE = "UPDATE usr_mst SET " +
            "n_comid=?, v_usrid=?, v_password=?, v_oldpassword=?, v_usrtypes=?, n_accessusrtype=?, " +
            "v_title=?, v_firstname=?, v_lastname=?, v_address1=?, v_address2=?, v_email=?, " +
            "v_land_phone=?, v_mobile=?, v_fax=?, v_nic=?, v_emp_no=?, n_brid=?, " +
            "d_activedate=?, d_expirydate=?, v_usrstatus=?, v_oldusrstatus=?, d_lastlogindate=?, " +
            "d_lastlogintime=?, n_atmptno=?, d_pwchgdate=?, d_pwprtdate=?, v_firstlogin=?, " +
            "d_uidlockdate=?, d_uidlocktime=?, v_anymodify=?, v_group_ids=?, v_group_ids_desc=?, v_usrtype_desc=?, " +
            "v_password_hash=?, V_REPORT_TO=?, N_TEAM_ID=?, N_LIABLITY_LIMIT=?, N_PAYMENT_LIMIT=?, " +
            "N_RESERVE_LIMIT=?, N_PAYMENT_AUTH_LIMIT=?, v_inpstat=?, v_inpuser=?, d_inptime=?, " +
            "v_auth1user=?, v_auth1stat=?, d_auth1time=?, v_auth2stat=?, v_auth2user=?, d_auth2time=?, " +
            "assessor_type=?, branch_code=?, need_to_send_email=?, n_auth_level=?, v_district_code=? " +
            "WHERE n_usrcode=?";

    String GET_NEXT_USER_CODE = "SELECT COALESCE(MAX(n_usrcode), 0) + 1 AS next_code FROM usr_mst";

    String GET_ACTIVE_ASSESSORS = "SELECT a.N_REF_NO, a.V_CODE, a.V_NAME, a.V_DISTRICT_CODE, d.V_DISTRICT_NAME " +
            "FROM claim_assessor a " +
            "LEFT JOIN claim_district d ON a.V_DISTRICT_CODE = d.V_DISTRICT_CODE " +
            "WHERE a.V_PARA_TYPE = 'ASSESSOR'";

    // Department and User Role SQL Constants
    String SELECT_CLAIM_DEPARTMENTS = "SELECT * FROM claim_department ORDER BY department_name";
    String SELECT_CLAIM_USER_ROLES = "SELECT * FROM accessusrtype_mst";

    // User Leave Management SQL Constants
    String CLAIM_USER_LEAVE_UPDATE = "UPDATE claim_user_leave SET \n" + "D_FROM_DATE_TIME =?,\n" + "D_TO_DATE_TIME =?,\n" + "V_LEAVE_TYPE =?,\n" + "V_INPUT_USER =?,\n" + "D_INPUT_DATE_TIME =? WHERE V_USER_ID =?\n";
    String CLAIM_USER_LEAVE_SEARCH = "SELECT\n" + "t2.D_FROM_DATE_TIME,\n" + "t2.D_TO_DATE_TIME,\n" + "t2.V_LEAVE_TYPE,\n" + "t2.V_INPUT_USER,\n" + "t2.D_INPUT_DATE_TIME,\n" + "t1.v_usrid,\n" + "t1.v_firstname,\n" + "t1.v_lastname\n" + "FROM\n" + "usr_mst AS t1\n" + "LEFT JOIN claim_user_leave AS t2 ON t1.v_usrid = t2.V_USER_ID\n" + "WHERE t1.v_usrid =? ";
    String CLAIM_USER_LEAVE_SEARCH_ALL = "SELECT * FROM claim_user_leave";
    String SQL_TODAY_LEAVE = "SELECT 1 FROM claim_user_leave WHERE V_USER_ID = ? AND V_LEAVE_TYPE= 'LEAVE' AND D_FROM_DATE_TIME <= ? AND D_TO_DATE_TIME >= ?";

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, Integer type) throws Exception;

    List<ClaimDepartmentDto> getAllDepartments(Connection connection) throws Exception;

    List<AssessorDto> getActiveAssessors(Connection connection) throws Exception;

}
