package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.BankLovDto;
import com.misyn.mcms.claim.dto.CalculationSheetPayeeHistoryDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimCalculationSheetPayeeDao extends BaseDao<ClaimCalculationSheetPayeeDto> {

    String CLAIM_CALCULATION_SHEET_PAYEE_INSERT = "INSERT INTO claim_calculation_sheet_payee VALUES(0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String CLAIM_CALCULATION_SHEET_PAYEE_UPDATE = "UPDATE claim_calculation_sheet_payee SET \n"
            + "N_CAL_SHEET_ID = ?,\n"
            + "N_PAYEE_ID = ?,\n"
            + "V_PAYEE_DESC = ?,\n"
            + "N_AMOUNT = ?,\n"
            + "V_IS_EFT_PAYMENT = ?,\n"
            + "V_ACCOUNT_NO = ?,\n"
            + "V_BANK_NAME = ?,\n"
            + "V_BANK_CODE = ?,\n"
            + "V_BRANCH = ?,\n"
            + "V_CONTACT_NO = ?,\n"
            + "V_EMAIL_ADDRESS = ? WHERE N_CAL_SHEET_PAYEE_ID =?";
    String CLAIM_CALCULATION_SHEET_PAYEE_SEARCH = "SELECT * FROM claim_calculation_sheet_payee WHERE N_CAL_SHEET_PAYEE_ID =?";
    String CLAIM_CALCULATION_SHEET_PAYEE_SEARCH_ALL = "SELECT * FROM claim_calculation_sheet_payee";
    String CLAIM_CALCULATION_SHEET_PAYEE_DELETE = "DELETE FROM claim_calculation_sheet_payee WHERE N_CAL_SHEET_PAYEE_ID =?";
    String CLAIM_CALCULATION_SHEET_PAYEE_DELETE_BY_CAL_SHEET_ID = "DELETE FROM claim_calculation_sheet_payee WHERE N_CAL_SHEET_ID =?";
    String CLAIM_CALCULATION_SHEET_PAYEE_SEARCH_ALL_BY_CAL_SHEET_ID = "SELECT * FROM claim_calculation_sheet_payee WHERE N_CAL_SHEET_ID = ?";
    String SELECT_PENDING_EMAILS_LIST_CHECK = "SELECT\n" +
            "t2.N_AMOUNT,\n" +
            "t2.V_PAYEE_DESC,\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_INPUT_USER,\n" +
            "t1.V_APR_USER,\n" +
            "t2.V_VOUCHER_NO,\n" +
            "t2.N_CAL_SHEET_PAYEE_ID,\n" +
            "t2.N_PAYEE_ID\n" +
            "FROM\n" +
            "claim_calculation_sheet_main AS t1\n" +
            "INNER JOIN claim_calculation_sheet_payee AS t2 ON t2.N_CAL_SHEET_ID = t1.N_CAL_SHEET_ID\n" +
            "WHERE t2.V_EMAIL_STATUS='N' AND t2.V_VOUCHER_NO IS NOT NULL AND t2.V_VOUCHER_NO <> ''  AND t1.N_CAL_SHEET_ID=?\n";

    String SELECT_PENDING_EMAILS_LIST = "SELECT\n" +
            "t2.N_AMOUNT,\n" +
            "t2.V_PAYEE_DESC,\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_INPUT_USER,\n" +
            "t1.V_APR_USER,\n" +
            "t2.V_VOUCHER_NO,\n" +
            "t2.N_CAL_SHEET_PAYEE_ID,\n" +
            "t2.N_PAYEE_ID\n" +
            "FROM\n" +
            "claim_calculation_sheet_main AS t1\n" +
            "INNER JOIN claim_calculation_sheet_payee AS t2 ON t2.N_CAL_SHEET_ID = t1.N_CAL_SHEET_ID\n" +
            "WHERE  t1.N_CAL_SHEET_ID=?\n";
    String UPDATE_EMAIL_STATUS = "UPDATE claim_calculation_sheet_payee SET V_EMAIL_STATUS='Y' WHERE  N_CAL_SHEET_PAYEE_ID =? ";

    String SELECT_VOUCHER_GENERATED_CAL_SHEETS = "SELECT\n" +
            "	t2.N_CAL_SHEET_ID\n" +
            "FROM\n" +
            "	claim_calculation_sheet_main AS t1\n" +
            "LEFT JOIN claim_calculation_sheet_payee AS t2 ON t2.N_CAL_SHEET_ID = t1.N_CAL_SHEET_ID\n" +
            "WHERE\n" +
            "	t1.V_STATUS = '67'\n" +
            "AND t2.V_EMAIL_STATUS = 'N'";

    String SELECT_PAYEE_DETAILS_BY_VOUCHER_NO = "SELECT * FROM claim_calculation_sheet_payee WHERE V_VOUCHER_NO = ?";


    String SELECT_PAYEE_HISTORY_BY_CALSHEET_ID = "SELECT\n" +
            "	t1.N_CAL_SHEET_PAYEE_ID,\n" +
            "	t1.N_PAYEE_ID,\n" +
            "	t1.V_PAYEE_DESC,\n" +
            "	t1.N_AMOUNT,\n" +
            "	t2.V_PAYEE_NAME \n" +
            "FROM\n" +
            "	claim_calculation_sheet_payee AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_payee_name AS t2 ON t1.N_PAYEE_ID = t2.N_CAL_SHEET_PAYEE_NAME_ID \n" +
            "WHERE\n" +
            "	N_CAL_SHEET_ID = ?";

    String SELECT_PAYEE_LIST_BY_PAYEE_ID = "SELECT\n" +
            "	t1.N_CAL_SHEET_PAYEE_ID,\n" +
            "	t1.N_PAYEE_ID,\n" +
            "	t1.V_PAYEE_DESC,\n" +
            "	t1.N_AMOUNT,\n" +
            "	t2.V_PAYEE_NAME \n" +
            "FROM\n" +
            "	claim_calculation_sheet_payee AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_payee_name AS t2 ON t1.N_PAYEE_ID = t2.N_CAL_SHEET_PAYEE_NAME_ID \n" +
            "WHERE\n" +
            "	t1.N_PAYEE_ID = ?";

    String SELECT_BANK_LOV = "SELECT N_ID, V_BANK_NAME, V_BANK_CODE FROM bank_lov ORDER BY V_BANK_NAME";

    List<ClaimCalculationSheetPayeeDto> searchByCalSheetId(Connection connection, Integer calSheetId) throws Exception;

    void deleteByCalSheetNo(Connection connection, Integer calSheetId) throws Exception;

    List<ClaimCalculationSheetPayeeDto> getPendingEmailList(Connection connection, Integer calSheetId, boolean isCheckStatus) throws Exception;

    void updateEmailStatus(Connection connection, Integer payeeId) throws Exception;

    List<Integer> getVoucherGeneratedList(Connection connection);

    ClaimCalculationSheetPayeeDto findPayeeDetailsByVoucherNo(Connection connection, String voucherNo) throws Exception;

    List<CalculationSheetPayeeHistoryDto> getPayeeListByCalsheetId(Connection connection, Integer calsheetId);
    List<CalculationSheetPayeeHistoryDto> getPayeeListByPayeeId(Connection connection, Integer payeeId);

    List<BankLovDto> getBankLov(Connection connection) throws Exception;
}
