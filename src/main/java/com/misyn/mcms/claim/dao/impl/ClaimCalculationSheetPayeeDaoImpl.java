package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.admin.admin.dao.BranchMstDao;
import com.misyn.mcms.admin.admin.dao.impl.BranchMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.claim.dao.ClaimCalculationSheetPayeeDao;
import com.misyn.mcms.claim.dto.BankLovDto;
import com.misyn.mcms.claim.dto.CalculationSheetPayeeHistoryDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
public class ClaimCalculationSheetPayeeDaoImpl implements ClaimCalculationSheetPayeeDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimCalculationSheetPayeeDaoImpl.class);
    private static BranchMstDao branchMstDao = new BranchMstDaoImpl();

    @Override
    public ClaimCalculationSheetPayeeDto insertMaster(Connection connection, ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_INSERT, Statement.RETURN_GENERATED_KEYS);
            ps.setInt(++index, claimCalculationSheetPayeeDto.getCalSheetId());
            ps.setInt(++index, claimCalculationSheetPayeeDto.getPayeeId());
            ps.setString(++index, claimCalculationSheetPayeeDto.getPayeeDesc());
            ps.setBigDecimal(++index, claimCalculationSheetPayeeDto.getAmount());
            ps.setString(++index, claimCalculationSheetPayeeDto.getIsEftPayment());
            ps.setString(++index, claimCalculationSheetPayeeDto.getAccountNo());
            ps.setString(++index, claimCalculationSheetPayeeDto.getBankName());
            ps.setString(++index, claimCalculationSheetPayeeDto.getBankCode());
            ps.setString(++index, claimCalculationSheetPayeeDto.getBranch());
            ps.setString(++index, claimCalculationSheetPayeeDto.getContactNo());
            ps.setString(++index, claimCalculationSheetPayeeDto.getEmailAddress());
            ps.setString(++index, claimCalculationSheetPayeeDto.getVoucherNo());
            ps.setString(++index, claimCalculationSheetPayeeDto.getEmailStatus());
            ps.setString(++index, claimCalculationSheetPayeeDto.getResponseDateTime());
            ps.setString(++index, claimCalculationSheetPayeeDto.getBranchDetailDto().getBranchCode());
            ps.setString(++index, claimCalculationSheetPayeeDto.getChequeNo());
            ps.setString(++index, claimCalculationSheetPayeeDto.getChequeStatus());
            ps.setString(++index, AppConstant.NO);

            if (ps.executeUpdate() > 0) {
                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    claimCalculationSheetPayeeDto.setCalSheetPayeeId(autoGeneratedId);
                    return claimCalculationSheetPayeeDto;
                }
                rsKeys.close();
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeDto updateMaster(Connection connection, ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_UPDATE);
            ps.setInt(++index, claimCalculationSheetPayeeDto.getCalSheetId());
            ps.setInt(++index, claimCalculationSheetPayeeDto.getPayeeId());
            ps.setString(++index, claimCalculationSheetPayeeDto.getPayeeDesc());
            ps.setBigDecimal(++index, claimCalculationSheetPayeeDto.getAmount());
            ps.setString(++index, claimCalculationSheetPayeeDto.getIsEftPayment());
            ps.setString(++index, claimCalculationSheetPayeeDto.getAccountNo());
            ps.setString(++index, claimCalculationSheetPayeeDto.getBankName());
            ps.setString(++index, claimCalculationSheetPayeeDto.getBankCode());
            ps.setString(++index, claimCalculationSheetPayeeDto.getBranch());
            ps.setString(++index, claimCalculationSheetPayeeDto.getContactNo());
            ps.setString(++index, claimCalculationSheetPayeeDto.getEmailAddress());
            ps.setInt(++index, claimCalculationSheetPayeeDto.getCalSheetPayeeId());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetPayeeDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeDto insertTemporary(Connection connection, ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeDto updateTemporary(Connection connection, ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeDto insertHistory(Connection connection, ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_DELETE);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void deleteByCalSheetNo(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_DELETE_BY_CAL_SHEET_ID);
            ps.setInt(1, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public List<ClaimCalculationSheetPayeeDto> getPendingEmailList(Connection connection, Integer calSheetId, boolean isCheckStatus) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(isCheckStatus ? SELECT_PENDING_EMAILS_LIST_CHECK : SELECT_PENDING_EMAILS_LIST);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetPayeeDto calculationSheetPayeeDto = getPayeeDto(rs);
                claimCalculationSheetPayeeDtoList.add(calculationSheetPayeeDto);
            }
            return claimCalculationSheetPayeeDtoList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void updateEmailStatus(Connection connection, Integer payeeId) throws Exception {
        try {
            PreparedStatement ps;
            ps = connection.prepareStatement(UPDATE_EMAIL_STATUS);
            ps.setInt(1, payeeId);
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public List<Integer> getVoucherGeneratedList(Connection connection) {
        PreparedStatement ps;
        ResultSet rs;
        List<Integer> list = new ArrayList();

        try {
            ps = connection.prepareStatement(SELECT_VOUCHER_GENERATED_CAL_SHEETS);
            rs = ps.executeQuery();

            while (rs.next()) {
                list.add(rs.getInt("N_CAL_SHEET_ID"));
            }
            return list;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());

        }
        return list;
    }

    @Override
    public ClaimCalculationSheetPayeeDto findPayeeDetailsByVoucherNo(Connection connection, String voucherNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetPayeeDto payeeDto = new ClaimCalculationSheetPayeeDto();
        try {
            ps = connection.prepareStatement(SELECT_PAYEE_DETAILS_BY_VOUCHER_NO);
            ps.setString(1, voucherNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                payeeDto.setCalSheetPayeeId(rs.getInt("N_CAL_SHEET_PAYEE_ID"));
                payeeDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                payeeDto.setPayeeId(rs.getInt("N_PAYEE_ID"));
                payeeDto.setPayeeDesc(null == rs.getString("V_PAYEE_DESC") ? AppConstant.STRING_EMPTY : rs.getString("V_PAYEE_DESC"));
                payeeDto.setAmount(rs.getBigDecimal("N_AMOUNT"));
                payeeDto.setIsEftPayment(null == rs.getString("V_IS_EFT_PAYMENT") ? AppConstant.STRING_EMPTY : rs.getString("V_IS_EFT_PAYMENT"));
                payeeDto.setAccountNo(null == rs.getString("V_ACCOUNT_NO") ? AppConstant.STRING_EMPTY : rs.getString("V_ACCOUNT_NO"));
                payeeDto.setBankName(null == rs.getString("V_BANK_NAME") ? AppConstant.STRING_EMPTY : rs.getString("V_BANK_NAME"));
                payeeDto.setBankCode(null == rs.getString("V_BANK_CODE") ? AppConstant.STRING_EMPTY : rs.getString("V_BANK_CODE"));
                payeeDto.setBranch(null == rs.getString("V_BRANCH") ? AppConstant.STRING_EMPTY : rs.getString("V_BRANCH"));
                payeeDto.setContactNo(null == rs.getString("V_CONTACT_NO") ? AppConstant.STRING_EMPTY : rs.getString("V_CONTACT_NO"));
                payeeDto.setEmailAddress(null == rs.getString("V_EMAIL_ADDRESS") ? AppConstant.STRING_EMPTY : rs.getString("V_EMAIL_ADDRESS"));
                payeeDto.setVoucherNo(null == rs.getString("V_VOUCHER_NO") ? AppConstant.STRING_EMPTY : rs.getString("V_VOUCHER_NO"));
                payeeDto.setEmailStatus(null == rs.getString("V_EMAIL_STATUS") ? AppConstant.STRING_EMPTY : rs.getString("V_EMAIL_STATUS"));
                payeeDto.setResponseDateTime(null == rs.getString("D_RESPONSE_DATETIME") ? AppConstant.STRING_EMPTY : rs.getString("D_RESPONSE_DATETIME"));
                payeeDto.setChequeNo(null == rs.getString("V_CHEQUE_NO") ? AppConstant.STRING_EMPTY : rs.getString("V_CHEQUE_NO"));
                String branch = null == rs.getString("V_CHECK_DISPATCH_LOCATION") ? AppConstant.STRING_EMPTY : rs.getString("V_CHECK_DISPATCH_LOCATION");
                if (!branch.isEmpty()) {
                    BranchDetailDto branchDetailByBranchCode = branchMstDao.getBranchDetailByBranchCode(connection, branch);
                    payeeDto.setBranchDetailDto(branchDetailByBranchCode);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return payeeDto;
    }

    @Override
    public List<CalculationSheetPayeeHistoryDto> getPayeeListByCalsheetId(Connection connection, Integer calsheetId) {
        PreparedStatement ps;
        ResultSet rs;
        List<CalculationSheetPayeeHistoryDto> list = new ArrayList();

        try {
            ps = connection.prepareStatement(SELECT_PAYEE_HISTORY_BY_CALSHEET_ID);
            ps.setObject(1, calsheetId);
            rs = ps.executeQuery();

            while (rs.next()) {
                CalculationSheetPayeeHistoryDto dto = new CalculationSheetPayeeHistoryDto();

                dto.setCalsheetPayeeId(rs.getInt("t1.N_CAL_SHEET_PAYEE_ID"));
                dto.setPayeeId(rs.getInt("t1.N_PAYEE_ID"));
                dto.setPayeeDesc(rs.getString("t1.V_PAYEE_DESC"));
                dto.setAmount(null == rs.getBigDecimal("t1.N_AMOUNT") ? new BigDecimal(0.00) : rs.getBigDecimal("t1.N_AMOUNT").setScale(2, RoundingMode.HALF_UP));
                dto.setPayeeType(rs.getString("t2.V_PAYEE_NAME"));

                list.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<CalculationSheetPayeeHistoryDto> getPayeeListByPayeeId(Connection connection, Integer payeeId) {
        PreparedStatement ps;
        ResultSet rs;
        List<CalculationSheetPayeeHistoryDto> list = new ArrayList();

        try {
            ps = connection.prepareStatement(SELECT_PAYEE_LIST_BY_PAYEE_ID);
            ps.setObject(1, payeeId);
            rs = ps.executeQuery();

            while (rs.next()) {
                CalculationSheetPayeeHistoryDto dto = new CalculationSheetPayeeHistoryDto();

                dto.setCalsheetPayeeId(rs.getInt("t1.N_CAL_SHEET_PAYEE_ID"));
                dto.setPayeeId(rs.getInt("t1.N_PAYEE_ID"));
                dto.setPayeeDesc(rs.getString("t1.V_PAYEE_DESC"));
                dto.setAmount(null == rs.getBigDecimal("t1.N_AMOUNT") ? new BigDecimal(0.00) : rs.getBigDecimal("t1.N_AMOUNT").setScale(2, RoundingMode.HALF_UP));
                dto.setPayeeType(rs.getString("t2.V_PAYEE_NAME"));

                list.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimCalculationSheetPayeeDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetPayeeDto calculationSheetPayeeDto = new ClaimCalculationSheetPayeeDto();
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                calculationSheetPayeeDto.setCalSheetPayeeId(rs.getInt("N_CAL_SHEET_PAYEE_ID"));
                calculationSheetPayeeDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                calculationSheetPayeeDto.setPayeeId(rs.getInt("N_PAYEE_ID"));
                calculationSheetPayeeDto.setPayeeDesc(rs.getString("V_PAYEE_DESC"));
                calculationSheetPayeeDto.setAmount(rs.getBigDecimal("N_AMOUNT"));
                calculationSheetPayeeDto.setIsEftPayment(rs.getString("V_IS_EFT_PAYMENT"));
                calculationSheetPayeeDto.setAccountNo(rs.getString("V_ACCOUNT_NO"));
                calculationSheetPayeeDto.setBankName(rs.getString("V_BANK_NAME"));
                calculationSheetPayeeDto.setBankCode(rs.getString("V_BANK_CODE"));
                calculationSheetPayeeDto.setBranch(rs.getString("V_BRANCH"));
                calculationSheetPayeeDto.setContactNo(rs.getString("V_CONTACT_NO"));
                calculationSheetPayeeDto.setEmailAddress(rs.getString("V_EMAIL_ADDRESS"));

                return calculationSheetPayeeDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return calculationSheetPayeeDto;
    }

    @Override
    public ClaimCalculationSheetPayeeDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetPayeeDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_SEARCH_ALL);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetPayeeDto calculationSheetPayeeDto = new ClaimCalculationSheetPayeeDto();

                calculationSheetPayeeDto.setCalSheetPayeeId(rs.getInt("N_CAL_SHEET_PAYEE_ID"));
                calculationSheetPayeeDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                calculationSheetPayeeDto.setPayeeId(rs.getInt("N_PAYEE_ID"));
                calculationSheetPayeeDto.setPayeeDesc(rs.getString("V_PAYEE_DESC"));
                calculationSheetPayeeDto.setAmount(rs.getBigDecimal("N_AMOUNT"));
                calculationSheetPayeeDto.setIsEftPayment(rs.getString("V_IS_EFT_PAYMENT"));
                calculationSheetPayeeDto.setAccountNo(rs.getString("V_ACCOUNT_NO"));
                calculationSheetPayeeDto.setBankName(rs.getString("V_BANK_NAME"));
                calculationSheetPayeeDto.setBankCode(rs.getString("V_BANK_CODE"));
                calculationSheetPayeeDto.setBranch(rs.getString("V_BRANCH"));
                calculationSheetPayeeDto.setContactNo(rs.getString("V_CONTACT_NO"));
                calculationSheetPayeeDto.setEmailAddress(rs.getString("V_EMAIL_ADDRESS"));

                claimCalculationSheetPayeeDtoList.add(calculationSheetPayeeDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimCalculationSheetPayeeDtoList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetPayeeDto> searchByCalSheetId(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_SEARCH_ALL_BY_CAL_SHEET_ID);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetPayeeDto calculationSheetPayeeDto = new ClaimCalculationSheetPayeeDto();
                calculationSheetPayeeDto.setCalSheetPayeeId(rs.getInt("N_CAL_SHEET_PAYEE_ID"));
                calculationSheetPayeeDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                calculationSheetPayeeDto.setPayeeId(rs.getInt("N_PAYEE_ID"));
                calculationSheetPayeeDto.setPayeeDesc(rs.getString("V_PAYEE_DESC"));
                calculationSheetPayeeDto.setAmount(rs.getBigDecimal("N_AMOUNT"));
                calculationSheetPayeeDto.setIsEftPayment(rs.getString("V_IS_EFT_PAYMENT"));
                calculationSheetPayeeDto.setAccountNo(rs.getString("V_ACCOUNT_NO"));
                calculationSheetPayeeDto.setBankName(rs.getString("V_BANK_NAME"));
                calculationSheetPayeeDto.setBankCode(rs.getString("V_BANK_CODE"));
                calculationSheetPayeeDto.setBranch(rs.getString("V_BRANCH"));
                calculationSheetPayeeDto.setContactNo(rs.getString("V_CONTACT_NO"));
                calculationSheetPayeeDto.setEmailAddress(rs.getString("V_EMAIL_ADDRESS"));
                calculationSheetPayeeDto.setVoucherNo(null == rs.getString("V_VOUCHER_NO") ? AppConstant.STRING_EMPTY : rs.getString("V_VOUCHER_NO"));
                String branch = null == rs.getString("V_CHECK_DISPATCH_LOCATION") ? AppConstant.STRING_EMPTY : rs.getString("V_CHECK_DISPATCH_LOCATION");
                if (!branch.isEmpty()) {
                    BranchDetailDto branchDetailByBranchCode = branchMstDao.getBranchDetailByBranchCode(connection, branch);
                    calculationSheetPayeeDto.setBranchDetailDto(branchDetailByBranchCode);
                }
                claimCalculationSheetPayeeDtoList.add(calculationSheetPayeeDto);
            }
            return claimCalculationSheetPayeeDtoList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private ClaimCalculationSheetPayeeDto getPayeeDto(ResultSet rs) {
        ClaimCalculationSheetPayeeDto calculationSheetPayeeDto = new ClaimCalculationSheetPayeeDto();
        try {

            calculationSheetPayeeDto.setPayeeDesc(rs.getString("t2.V_PAYEE_DESC"));
            calculationSheetPayeeDto.setAmount(rs.getBigDecimal("t2.N_AMOUNT"));
            calculationSheetPayeeDto.setCalSheetPayeeId(rs.getInt("t2.N_CAL_SHEET_PAYEE_ID"));
            calculationSheetPayeeDto.setVoucherNo(null == rs.getString("t2.V_VOUCHER_NO") ? AppConstant.STRING_EMPTY : rs.getString("t2.V_VOUCHER_NO"));
            calculationSheetPayeeDto.setPayeeId(rs.getInt("t2.N_PAYEE_ID"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return calculationSheetPayeeDto;
    }

    @Override
    public List<BankLovDto> getBankLov(Connection connection) {
        PreparedStatement ps;
        ResultSet rs;
        List<BankLovDto> BankLovDtolist = new ArrayList();

        try {
            ps = connection.prepareStatement(SELECT_BANK_LOV);
            rs = ps.executeQuery();

            while (rs.next()) {
                BankLovDto dto = new BankLovDto();
                dto.setId(rs.getInt("N_ID"));
                dto.setBankCode(rs.getString("V_BANK_CODE"));
                dto.setBankName(rs.getString("V_BANK_NAME"));
                BankLovDtolist.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return BankLovDtolist;
    }
}
