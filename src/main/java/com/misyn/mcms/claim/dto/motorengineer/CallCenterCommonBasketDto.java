package com.misyn.mcms.claim.dto.motorengineer;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;

public class CallCenterCommonBasketDto implements Serializable {

    private int id = AppConstant.ZERO_INT;

    private int inspectionId = AppConstant.ZERO_INT;
    private int inspectionType = AppConstant.ZERO_INT;
    private int claimNo = AppConstant.ZERO_INT;

    private String rteUserId = AppConstant.STRING_EMPTY;
    private String callCenterUserId = AppConstant.STRING_EMPTY;
    private String assessorId = AppConstant.STRING_EMPTY;

    private String isOnsiteReviewApply = AppConstant.NO; // 'Y' or 'N'

    private Status status = Status.PENDING;

    private String submittedDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String submittedUser = AppConstant.STRING_EMPTY;

    private String lastUpdatedDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String lastUpdatedUser = AppConstant.STRING_EMPTY;

    private String rejectedReason = AppConstant.STRING_EMPTY;
    private int rejectCount = AppConstant.ZERO_INT;

    public int getId() {
        return id;
    }


    // Getters and Setters

    public void setId(int id) {
        this.id = id;
    }

    public int getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(int inspectionId) {
        this.inspectionId = inspectionId;
    }

    public int getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(int inspectionType) {
        this.inspectionType = inspectionType;
    }

    public int getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(int claimNo) {
        this.claimNo = claimNo;
    }

    public String getRteUserId() {
        return rteUserId;
    }

    public void setRteUserId(String rteUserId) {
        this.rteUserId = rteUserId;
    }

    public String getCallCenterUserId() {
        return callCenterUserId;
    }

    public void setCallCenterUserId(String callCenterUserId) {
        this.callCenterUserId = callCenterUserId;
    }

    public String getAssessorId() {
        return assessorId;
    }

    public void setAssessorId(String assessorId) {
        this.assessorId = assessorId;
    }

    public String getIsOnsiteReviewApply() {
        return isOnsiteReviewApply;
    }

    public void setIsOnsiteReviewApply(String isOnsiteReviewApply) {
        this.isOnsiteReviewApply = isOnsiteReviewApply;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getSubmittedDatetime() {
        return submittedDatetime;
    }

    public void setSubmittedDatetime(String submittedDatetime) {
        this.submittedDatetime = submittedDatetime;
    }

    public String getSubmittedUser() {
        return submittedUser;
    }

    public void setSubmittedUser(String submittedUser) {
        this.submittedUser = submittedUser;
    }

    public String getLastUpdatedDatetime() {
        return lastUpdatedDatetime;
    }

    public void setLastUpdatedDatetime(String lastUpdatedDatetime) {
        this.lastUpdatedDatetime = lastUpdatedDatetime;
    }

    public String getLastUpdatedUser() {
        return lastUpdatedUser;
    }

    public void setLastUpdatedUser(String lastUpdatedUser) {
        this.lastUpdatedUser = lastUpdatedUser;
    }

    public String getRejectedReason() {
        return rejectedReason;
    }

    public void setRejectedReason(String rejectedReason) {
        this.rejectedReason = rejectedReason;
    }

    public int getRejectCount() {
        return rejectCount;
    }

    public void setRejectCount(int rejectCount) {
        this.rejectCount = rejectCount;
    }

    public enum Status {
        PENDING("P"), ASSIGNED("A"), COMPLETED("C");

        private final String code;

        Status(String code) {
            this.code = code;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equalsIgnoreCase(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Invalid status code: " + code);
        }

        public String getCode() {
            return code;
        }
    }
}
