
#generic error messages
GEN.DB.JDBC.ERROR=Error in jdbc
GEN.DB.CONN.ERROR=Error in database connection
GEN.LDAP.URI.ERROR=Error in ldap connection
GEN.DB.INVALID.OPERATION.ADD=Could not add a new record
GEN.DB.INVALID.OPERATION.DEL=Could not remove record
DATE_CONVERSION_ERROR=String to date conversion error
REF.UI.LEAVE.CERTIFICATE.ERROR=Medical certificate is not given
REF.UI.LEAVE.HOLIDAY.DATES.ERROR=Sorry, You cannot add leave on holidays
MESSAGE.SIZE=Message size is too large
#reference module specific error messages
REF.SYS.USER.NAME=Sorry this user is already registered in the system
REF.SYS.USER.USERNAME=Sorry this username already exists. Please select another
REF.SYS.USER=System user created successfully
REF.SYS.USER.EDITED=System user edited successfully
REF.UI.SYSTEM.USER.MAIL=System user successfully saved to the DB, but unable to send mail
REF.UI.SYSTEM.USER.EDITED.MAIL=System user updated successfully,but unable to send mail
REF.UI.TERMINATE.USER.MAIL=Student successfully terminated, but unable to send mail
#school specific error messages
SCH.DETAILS=Sorry details already exists
UI.PUBLICATION.DATE.ERROR=The date must be a future date
REF.UI.EXAM.DELETE=Sorry this Exam cannot be deleted
EDIT.EXAM.MESSAGE=Sorry this Exam cannot be edited
#emil specific error messages of MessageBoardController
REF.EMAIL.REPORT.PATH=com.sms.path.emailtemplates
PAGINATION.NEXT=next
REF.UI.PUBLICATION.EXIST=This News/Events already exists. Please add another
#staff specific error messages
PUB.UI.IMAGE.EXTENSION.ERROR=Image file extension should be in .jpg,.png,.jpeg,.gif,.bitmap
PUB.UI.IMAGE.SIZE.ERROR=Image size should not be more than 1MB
STA.SEARCH.NO.RESULT=No results exists for the given staff criteria
STA.UI.NIC.FIELD.TYPE=National Identity Card Number format is invalid
STA.UI.PARISH.FIELD.TYPE=Invalid data format for Parish
typeMismatch.staff.basicSalary=Invalid data format for Basic Salary
STA.UI.MANDATORY.FIELD.REQUIRED=Please fill all the mandatory fields marked in asterisk
STA.UI.MANDATORY.FIELD.PHONE.NO=Please fill Residence or Mobile Phone Number
STA.UI.MOBILE.NO.FIELD.TYPE=Mobile Phone Number is invalid format
STA.UI.RESIDENCE.NO.FIELD.TYPE=Residence Phone Number is invalid format
STA.UI.CR.NO=Contact Details Residence number
STA.UI.CM.NO=Contact details Mobile Number
STA.UI.ER.NO=Emergency Contact Residence Number
STA.UI.EM.NO=Emergency Contact Mobile Number
STA.UI.CR.NO.INVALID=Contact Details Residence number is not recognized
STA.UI.CM.NO.INVALID=Contact details Mobile Number is not recognized
STA.UI.ER.NO.INVALID=Emergency Contact Residence Number is not recognized
STA.UI.EM.NO.INVALID=Emergency Contact Mobile Number is not recognized
STA.UI.All.PHONE.NUMBERS.NO.FIELD.TYPE=All phone numbers are not recognized
STA.UI.CR.NO.STA.UI.CM.NO=Contact Details Residence number and Contact details Mobile Number are not recognized
STA.UI.CR.NO.STA.UI.ER.NO=Contact Details Residence number and Emergency Contact Residence Number are not recognized
STA.UI.CR.NO.STA.UI.EM.NO=Contact Details Residence number and Emergency Contact Mobile Number are not recognized
STA.UI.CM.NO.STA.UI.ER.NO=Contact details Mobile number and Emergency Contact Residence Number are not recognized
STA.UI.CM.NO.STA.UI.EM.NO=Contact details Mobile number and Emergency Contact Mobile Number are not recognized
STA.UI.ER.NO.STA.UI.EM.NO=Emergency Contact Residence Number and Emergency Contact Mobile Number are not recognized
STA.UI.CR.NO.STA.UI.CM.NO.STA.UI.ER.NO=Contact Details Residence, Contact details Mobile number and Emergency Contact Residence Number are not recognized
STA.UI.CR.NO.STA.UI.CM.NO.STA.UI.EM.NO=Contact Details Residence, Contact details Mobile number and Emergency Contact Mobile Number are not recognized
STA.UI.CR.NO.STA.UI.ER.NO.STA.UI.EM.NO=Contact Details Residence, Emergency Contact Residence Number and Emergency Contact Mobile Number are not recognized
STA.UI.CM.NO.STA.UI.ER.NO.STA.UI.EM.NO=Contact details Mobile number, Emergency Contact Residence Number and Emergency Contact Mobile Number are not recognized
STA.UI.CM.NO.STA.UI.ER.NO.STA.UI.ER.NO=Contact details Mobile number, Emergency Contact Residence Number and Emergency Contact Residence Number are not recognized
STA.UI.OFFICE.NO.FIELD.TYPE=Office Phone Number is invalid format
STA.UI.FULLNAME.FIELD.TYPE=Full Name has invalid character(s) entered
STA.UI.LASTNAME.FIELD.TYPE=Last Name has invalid character(s) entered
STA.UI.NAME.INITIALS.FIELD.TYPE=Name with Initials has invalid character(s) entered
STA.UI.DUPLICATE.DESCRIPTION=Sorry this record already exists. Please add another
STA.UI.RECORD.ADDED=Staff member successfully added
STA.UI.RECORD.MODIFIED=Staff member successfully updated
STA.UI.EMAIL.ERROR=Please enter valid Email Address
STA.UI.EMAIL.EXIST.ERROR=This Email Address is already assigned to a staff member
STA.UI.RECORD.DELETE.ERROR=Staff Member cannot be deleted
IMG.UI.FILE.EMPTY.ERROR=Empty file selected
IMG.UI.IMAGE.FIELD.TYPE=Please enter Image File Extensions .jpg,.png,.jpeg,.gif,.bitmap
IMG.UI.IMAGE.FILE.SIZE=File size is more than 1MB
PUB.UI.IMAGE.SIZE.MIN.ERROR=Database Server size is less than 1MB
IMAGE.DATABASE.SIZE=Please increase the database server max packet size
typeMismatch.staff.dateOfBirth=Correct date format is yyyy-mm-dd
typeMismatch.staff.firstAppointmetDate=Correct date format is yyyy-mm-dd
typeMismatch.staff.dateOfDeparture=Correct date format is yyyy-mm-dd
#staff qualification specific error messages
STQ.UI.MANDATORY.FIELD.REQUIRED=Please fill all the mandatory fields marked in star
STQ.UI.DUPLICATE.DESCRIPTION=Sorry this record already exists. Please add another
typeMismatch.staffEducation.year=Correct date format is yyyy-mm-dd
typeMismatch.staffProfessional.fromYear=Correct date format is yyyy-mm-dd
typeMismatch.staffProfessional.toYear=Correct date format is yyyy-mm-dd
#student specific error messages
STU.SEARCH.NO.RESULT=No results found for the given student criteria
typeMismatch.student.dateOfBirth=Correct date format is yyyy-mm-dd
typeMismatch.student.admissionDate=Correct date format is yyyy-mm-dd
typeMismatch.student.previousSchoolFromDate=Correct date format is yyyy-mm-dd
typeMismatch.student.previousSchoolToDate=Correct date format is yyyy-mm-dd
#student service specific error messages
REF.SERVICE.RATING.VERYGOOD=Very Good
REF.SERVICE.RATING.GOOD=Good
#parent specific error messages
PAR.SEARCH.NO.RESULT=No results found for the given parent criteria
PAR.UI.MAND.REQUIRED=Please enter father, mother or guardian details
PAR.UI.MANDATORY.REQUIRED=Please fill all the mandatory fields marked in asterisk
PER.UI.FULLNAME.FIELD.TYPE=Full Name has invalid character(s) entered
PER.UI.NAMEWITNINITIALS.FIELD.TYPE=Name with Initials has invalid character(s) entered
PER.UI.LASTNAME.FIELD.TYPE=Last Name has invalid character(s) entered
PAR.UI.NIC.FIELD.TYPE=National Identity Card Number is invalid format
PAR.UI.REL.FIELD.TYPE=Please select a Religion
PAR.UI.WORK.SEG.FIELD.TYPE=Please select a Working Segment
PAR.UI.PERM.CITY.FIELD.TYPE=Please select a Permanent City
PAR.UI.TEMP.CITY.FIELD.TYPE=Please select a Temporary City
PAR.UI.RES.PHONE.NO.FIELD.TYPE=Residence Phone Number invalid format
PAR.UI.MOB.PHONE.NO.FIELD.TYPE=Mobile Phone Number invalid format
PAR.UI.EMAIL.FIELD.TYPE=Email Address invalid format
PAR.UI.OFF.PHONE.NO.FIELD.TYPE=Office Phone Number invalid format
PAR.UI.OFF.FAX.NO.FIELD.TYPE=Office Fax Number invalid format
PAR.UI.OFF.EMAIL.FIELD.TYPE=Office Email Address invalid format
PAR.UI.OFF.CITY.FIELD.TYPE=Please select a City
PAR.UI.ALUMNI.FIELD.TYPE=Please enter Alumni Number
typeMismatch.parent.alumniId=Alumni Number invalid format
PAR.UI.TEACHER.FIELD.TYPE=Teacher Registration Number invalid format
PAR.UI.DONATION.MAND.PURPOSE.REQUIRED=Please enter Purpose for the Donation
PAR.UI.DONATION.MAND.AMOUNT.REQUIRED=Please enter Amount for the Donation
PAR.UI.DONATION.MAND.DONATIONTYPE.REQUIRED=Please select the Donation Type
typeMismatch.donation.amount=Amount of Donation is invalid format
PAR.UI.DONATION.DELETE=Sorry this Donation cannot be deleted
REF.UI.PARENT.STAFFID.NOTEXIST=Sorry invalid Teacher RegNo. Please enter correct Teacher RegNo
REF.UI.PARENT.DONATION.DESCRIPTION=Sorry parent information is not available. Please add parent information
REF.UI.PARENT.DONATION.EXISTS=Sorry donation information is already exists. Please add another donation information
PAR.UI.DUP.NIC.FIELD.TYPE=Duplicated NIC Number. Please enter correct NIC Number
PAR.UI.NIC.NO.EXIST=The same parent details exist as shown... Please validate...
PAR.UI.PARENT.EMAIL.REQUIRED=Parents Email Required
PAR.UI.USER.EMAIL.REQUIRED=Senders Email Required
PAR.UI.USER.FILE.REQUIRED=Please select Progress Report
PAR.UI.GENERATE.REPORT.INCOMPLETE=Generate the Report first
PAR.UI.PARENT_UPDATED_SUCESS=Parent details updated successfully
PAR.UI.PARENT_ALREADEY_USED=The same parent identification used for the same student
PAR.UI.PARENT_DETAIL_EXIST=The same parent details exist as shown... Please validate...
PAR.UI.DEFFERENT_PARENT_ENTITY=The same parent identification used within a different parent entity in student admission no :
PAR.UI.NEW_RECORD_ADD_SUCCESS=New parent details added successfully
PAR.UI.NO_USER_EMAIL_ADRESS=No user email address found
PAR.UI.ERROR_SEND_TO_USER=Error sending email to user
PAR.UI.ERROR_SEND_TO_ADMIN=Error sending email to Administrator. Please try again later
PAR.UI.SUCCESS_SEND_TO_ADMIN=Successfully sent your request to Administrator
PAR.UI.SUCCESS_SEND_TO_USER=Notification email was successfully sent to the user
#studentProgress report specific messages
TITLE=Title
REF.REPO.NO.DATA=No data to be previewed
STU.PRO.SUMMARY.TITLE=Student Progress Summary
#subject specific error messages
SUB.SEARCH.NO.RESULT=No results found for the given subject criteria
#REF.UI.SUBJECT.DESCRIPTION.REQUIRED = Description of the subject is required.
#stream specific error messages
STR.SEARCH.NO.RESULT=No results found for the given stream criteria
REF.UI.STREAM.DESCRIPTION.REQUIRED=Description of the stream is required
#province specific error messages
PRO.SEARCH.NO.RESULT=No results found for the given province criteria
REF.UI.PROVINCE.DESCRIPTION.REQUIRED=Description of the province is required
#district specific error messages
DIS.SEARCH.NO.RESULT=No results found for the given district criteria
REF.UI.DISTRICT.DESCRIPTION.REQUIRED=Description of the district is required
#city specific error messages
CIT.SEARCH.NO.RESULT=No results found for the given city criteria
REF.UI.CITY.DESCRIPTION.REQUIRED=Description of the city is required
#country specific error messages
COUN.SEARCH.NO.RESULT=No results found for the given country criteria
#bloodGroup specific error messages
REF.UI.BLOODGROUP.DESCRIPTION.REQUIRED=Description of the Blood Group is required
#PrefectType specific error messages
REF.UI.PREFECTTYPE.DESCRIPTION.REQUIRED=Description of the Prefect Type is required
#WarningLevel specific error messages
REF.UI.WARNINGLEVEL.DESCRIPTION.REQUIRED=Description of the warning Level is required
#SchoolClass specific error messages
REF.UI.SCHOOLCLASS.DESCRIPTION.REQUIRED=Description of the class is required
#ManageAssignment specific error messages
REF.UI.ASSIGNMENT.DESCRIPTION.REQUIRED=Assignment Name is required
#nationality specific error messages
REF.UI.NATIONALITY.DESCRIPTION.REQUIRED=Please fill all the mandatory fields marked in asterisk.
#race specific error messages
REF.UI.RACE.DESCRIPTION.REQUIRED=Description of the race is required.
#saffCategory specific error messages
REF.UI.STAFFCATEGORY.DESCRIPTION.REQUIRED=Description of the Staff Category is required
REF.UI.STAFFCATEGORY.DESCRIPTION=Sorry this Staff Category already exists. Please add another
REF.UI.STAFFCATEGORY.ACADEMIC.REQUIRED=Academic Details of the Staff Category is required
REF.UI.STAFFCATEGORY.ACADEMIC=Academic
REF.UI.STAFFCATEGORY.NONACADEMIC=Non Academic
#sectionHead specific error messages
SEC.UI.SECTIONHEAD.DUPLICATESTARTDATE=Sorry this is an invalid record
SEC.SEARCH.NO.RESULT=No results found for the given Section Head criteria
SEC.ASSIGNED.SUCCESSFULLY=Section Head is assigned successfully
SEC.UPDATED.SUCCESSFULLY=Section Head is updated successfully
SEC.DELETE.ERROR=You cannot delete Section Head who has already started working
# Reference module - Manage Section specific error messages
REF.UI.SECTION.DESCRIPTION.REQUIRED=Section is required field.
REF.UI.SECTION.DESCRIPTION.EXIST=Sorry this record already already exists. Please add another.
REF.UI.SECTION.DELETE=Sorry this Section cannot be deleted.
#school specific error messages
REF.UI.SCHOOL.NAME.REQUIRED=Name of the School is required
REF.UI.STAFF.PRINCIPALID.REQUIRED=Principal of the School is required
REF.UI.STAFF.VICEPRINCIPALID.REQUIRED=Vice Principal of the School is required
REF.UI.STAFF.NO.REQUIRED=Number of Staff is required
REF.UI.STAFF.NO.ERROR=Number of Staff cannot be less than zero
REF.UI.STUDENT.NO.ERROR=Number of Students cannot be less than zero
typeMismatch.school.noOfStudents=Number of students must be a valid number
typeMismatch.school.noOfStaff=Number of staff must be a valid number
typeMismatch.school.startedDate=Started Date is required
REF.UI.STARTEDDATE.ERROR=The Date should not be the current date
REF.UI.EMAIL.TYPE=Enter a valid E-mail Address
REF.UI.WEBSITE.TYPE=Enter a valid hyperlink. Eg.www.XXXX.com
REF.UI.SCHOOL.STARTEDDATE.REQUIRED=Started Date is required
REF.UI.SCHOOL.NAME=School Name :
REF.UI.STAFF.DATA.LONG=Data length is too long
REF.UI.NAME.TYPE=School name may contain ' . special characters
REF.UI.ADDRESS.TYPE=Address may contain ' . , - : special characters
REF.UI.CONTACTNO.TYPE=Contact Number must be a valid number
REF.UI.FAXNO.TYPE=Fax Number must be a valid number
REF.UI.SCHOOL.CONTACTNO.SHORT=Contact Number is wrong
REF.UI.SCHOOL.FAXNO.SHORT=Fax Number is invalid
REF.UI.SCHOOL.SUCCESSFULLY_EDITED=Successfully Edited
REF.UI.SCHOOL.SUCCESSFULLY_ADDED=Successfully Added
REF.UI.SCHOOL.VICE_PRICIPAL.NOT_ALLOWED=The same person can't be assigned as the Principal and the Vice Principal
#reference module UI specific error messages
REF.UI.PROVINCE.DESCRIPTION.ERROR=Sorry this province already exists. Please add another
REF.UI.BLOODGROUP.DESCRIPTION=Sorry this Blood Group already exists. Please add another
REF.UI.PREFECTTYPE.DESCRIPTION=Sorry this Prefect Type already exists. Please add another
REF.UI.WARNINGLEVEL.DESCRIPTION=Sorry this Waning Level is already exists. Please add another
REF.UI.SCHOOLCLASS.DESCRIPTION=Sorry this Class already exists. Please add another
REF.UI.EXAM.DESCRIPTION=Sorry this Exam already exists. Please add another
REF.UI.NATIONALITY.DESCRIPTION=Sorry this Nationality already exists. Please add another
REF.UI.RACE.DESCRIPTION=Sorry this race is already exists. Please add another.
REF.UI.ASSIGNMENT.DESCRIPTION=Sorry this Assignment already exists. Please add another
REF.UI.CITY.DESCRIPTION=Sorry this City already exists. Please add another
REF.UI.DISTRICT.DESCRIPTION=Sorry this District already exists. Please add another
REF.UI.COUNTRY.DESCRIPTION=Sorry this country is already exists. Please add another.
REF.UI.SUBJECT.DESCRIPTION=Sorry this Subject Name , Short Name or Code already exists. Please add another
REF.UI.SUBJECT.CODE=Maximum length of Subject Code is 5
REF.UI.SUBJECT.DELETE=Sorry this Subject cannot be deleted
REF.UI.STAFFCATEGORY.DELETE=Sorry this Staff Category cannot be deleted
REF.UI.STREAM.DELETE=Sorry this Stream cannot be deleted
REF.UI.WARNINGLEVEL.DELETE=Sorry this Warning Level cannot be deleted
REF.UI.PROVINCE.DELETE=Sorry this Province cannot be deleted
REF.UI.DISTRICT.DELETE=Sorry this District cannot be deleted
REF.UI.FIELD.TYPE=Invalid data type
REF.UI.FIELD.COUNTRY.CODE.TYPE=Invalid country code
REF.UI.STREAM.DESCRIPTION=Sorry this Stream already exists. Please add another
REF.UI.NATIONALITY.DELETE=Sorry this Nationality cannot be deleted
REF.UI.RACE.DELETE=Sorry this Race cannot be deleted.
REF.UI.BLOODGROUP.DELETE=Sorry this Blood Group cannot be deleted
REF.UI.PREFECTTYPE.DELETE=Sorry this Prefect Type cannot be deleted
REF.UI.SCHOOLCLASS.DELETE=Sorry this Class cannot be deleted
REF.UI.CITY.DELETE=Sorry this City cannot be deleted
REF.UI.ASSIGNMENT.DELETE=Sorry this Assignment cannot be deleted
REF.UI.ASSIGNMENT.CANNOT_UPDATED=Sorry this Assignment cannot be updated
REF.UI.COUNTRY.DELETE=Sorry this Country cannot be deleted.
REF.UI.STAFFLEAVETYPEFIELD.TYPE=Mismatch the field type for Staff Leave Type .
# Reference module - Manage Religion specific error messages
REF.UI.RELIGION.DESCRIPTION.REQUIRED=Religion is required field
REF.UI.RELIGION.DESCRIPTION.EXIST=Religion already exists. Please add another
REF.UI.RELIGION.DELETE=Sorry this Religion cannot be deleted
# Reference module - Appointment nature specific error messages
REF.UI.APPOINTMENTNATURE.DESCRIPTION.REQUIRED=Please fill all the mandatory fields marked in asterisk
REF.UI.APPOINTMENTNATURE.DESCRIPTION.EXIST=This Record is already exists. Please add another
REF.UI.APPOINTMENTNATURE.DELETE=Sorry this record cannot be deleted
# Reference module - Manage Appointment Classification specific error messages
REF.UI.APPOINTMENTCLASSIFICATION.DESCRIPTION.REQUIRED=Appointment Classification is required field.
REF.UI.APPOINTMENTCLASSIFICATION.DESCRIPTION.EXIST=Appointment Classification already exists. Please add another.
REF.UI.APPOINTMENTCLASSIFICATION.DELETE=Sorry this Appointment Classification cannot be deleted.
#classTeacher specific error messages
CLASSTEACHER.SEARCH.NO.RESULT=No results found for the given Class Teacher criteria
REF.UI.CLASSTEACHER.ALREADY.ASSIGN=Sorry this Record is already assigned. Please add another
REF.UI.CLASSTEACHER.EXIST=Sorry this Class Teacher already exist. Please add another
REF.UI.CLASSTEACHER.DELETE=Sorry this Class Teacher cannot be deleted
REF.UI.CLASSTEACHER.ADDED=Class Teacher is added successfully
REF.UI.CLASSTEACHER.UPDATED=Class Teacher is updated successfully
# Reference module - Manage House specific error messages
REF.UI.HOUSE.DESCRIPTION.REQUIRED=House is required field
REF.UI.HOUSE.DESCRIPTION.EXIST=House already exists. Please add another
REF.UI.HOUSE.COLOUR.EXIST=This Color for house already exists. Please add another
REF.UI.HOUSE.DELETE=Sorry this House cannot be deleted
#gradeSubject specific error messages
REF.UI.GRADESUBJECT.EXIST=Sorry this Grade Subject already exists. Please add another
REF.UI.GRADESUBJECT.SUBJECTS.ASSIGNED=Subjects are already assigned to the selected Grade. Please update the record.
REF.UI.GRADESUBJECT.DELETE=Sorry this Grade Subject cannot be deleted
REF.UI.GRADESUBJECT.DELETE.SUBJECTS=Sorry cannot edit this record, because marks are assigned to some subject.
# Reference module - Manage Position specific error messages
REF.UI.POSITION.DESCRIPTION.REQUIRED=Position is required field
REF.UI.POSITION.DESCRIPTION.EXIST=Position already exists. Please add another
REF.UI.POSITION.DELETE=Sorry this Position cannot be deleted
# Reference module - Manage Educational Qualification specific error messages
REF.UI.EDUCATIONALQUALIFICATION.DESCRIPTION.REQUIRED=Educational Qualification is required field
REF.UI.EDUCATIONALQUALIFICATION.DESCRIPTION.EXIST=Educational Qualification already exists. Please add another
REF.UI.EDUCATIONALQUALIFICATION.DELETE=Sorry this Educational Qualification cannot be deleted
# Reference module - Manage Professional Qualification specific error messages
REF.UI.PROFESSIONALQUALIFICATION.DESCRIPTION.REQUIRED=Professional Qualification is required field
REF.UI.PROFESSIONALQUALIFICATION.DESCRIPTION.EXIST=Professional Qualification already exists. Please add another
REF.UI.PROFESSIONALQUALIFICATION.DELETE=Sorry this Professional Qualification cannot be deleted
# Reference module - Manage Scholarship specific error messages
REF.UI.METHODOFTRAVEL.DESCRIPTION.REQUIRED=Travel Method is required field
REF.UI.METHODOFTRAVEL.DESCRIPTION.EXIST=Travel Method already exists. Please add another
REF.UI.METHODOFTRAVEL.DELETE=Sorry this Travel Method cannot be deleted
# Reference module - Manage Scholarship specific error messages
REF.UI.SCHOLARSHIP.DESCRIPTION.REQUIRED=Scholarship is required field
REF.UI.SCHOLARSHIP.DESCRIPTION.EXIST=Scholarship already exists. Please add another
REF.UI.SCHOLARSHIP.DELETE=Sorry this Scholarship cannot be deleted
# Reference module - Manage Working Segment specific error messages
REF.UI.WORKINGSEGMENT.DESCRIPTION.REQUIRED=Working Segment is required field
REF.UI.WORKINGSEGMENT.DESCRIPTION.EXIST=Working Segment already exists. Please add another
REF.UI.WORKINGSEGMENT.DELETE=Sorry this Working Segment cannot be deleted
REF.UI.GRADE.DESCRIPTION.REQUIRED=Grade is required field
REF.UI.GRADE.CLASSES.REQUIRED=Class is required field
REF.UI.GRADE.DESCRIPTION.EXIST=Grade already exists. Please add another
REF.UI.GRADE.CLASSES.MAXIMUM_MARK=The maximum mark values should be a number between 0 and the 100.
REF.UI.GRADE.DELETE=Sorry this Grade cannot be deleted
REF.UI.GRADE.EDIT=Sorry this Grade cannot be edited
# Reference module - Manage Term specific error messages
REF.UI.TERM.DESCRIPTION.REQUIRED=Term is required field
REF.UI.TERM.DESCRIPTION.EXIST=Term already exists. Please add another
typeMismatch.fromMonth=
typeMismatch.toMonth=
REF.UI.TERM.PERIOD=Start Date and End Date cannot be same. Please select another
REF.UI.TERM.DELETE=Sorry this Term cannot be deleted
# Reference module - Manage Sub Term specific error messages
REF.UI.SUBTERM.TERM.REQUIRED=Term is required field
REF.UI.SUBTERM.DESCRIPTION.REQUIRED=Sub Term is required field
REF.UI.SUBTERM.DESCRIPTION.EXIST=Sub Term already exists for the selected Term. Please add another
typeMismatch.termId=Term is required field
REF.UI.SUBTERM.DELETE=Sorry this Sub Term cannot be deleted
# Reference module - Manage Grading specific error messages
REF.UI.GRADING.ACRONYM.REQUIRED=Grade acronym is required field
REF.UI.GRADING.DESCRIPTION.REQUIRED=Description for the entered acronym is required field
REF.UI.GRADING.DESCRIPTION.EXIST=Grading information already exists. Please add another
REF.UI.GRADING.DELETE=Sorry this Grading cannot be deleted
REF.UI.GRADING.TYPE.MISMATCH=Grade acronym can have only capital letters
# Reference module - Manage Prefect Type specific error messages
STU.UI.DESCRIPTION.FIELD.TYPE=Please enter a valid Prefect Name
REF.UI.STUDENT.ADMISSIONNO.VIOLATEERROR=Please select all the mandatory fields marked in asterisk
#civil status specific error messages
REF.UI.CIVILSTATUS.DESCRIPTION.REQUIRED=Description of the civil status is required.
REF.UI.CIVILSTATUS.DELETE=Sorry this Civil status cannot be deleted.
REF.UI.CIVILSTATUS.DESCRIPTION=Sorry this civil status is already exists. Please add another.
REF.UI.STAFFLEAVETYPE.DESCRIPTION.EXIST=Staff Leave Type already exists. Please add another.
REF.UI.STAFFLEAVETYPE.DELETE=This Staff Leave Type is already applied and cannot be deleted
REF.UI.STAFFLEAVETYPE.EDIT=Sorry this Staff Leave Type is already applied and cannot be edited
REF.UI.STAFFLEAVETYPE.MAXSTAFFLEAVES=Maximum number of Staff Leaves cannot be zero.
# Student module - Manage Student specific error messages
REF.UI.STUDENT.EMAIL.ERROR=Please enter valid Email Address
REF.UI.STUDENT.ADMISSIONNO.DUPLCATEERROR=Admission No already exists. Please enter new Admission No
REF.UI.STUDENT.ADMISSIONNO.VIOLATEERROR=Please select all the mandatory fields marked in asterisk
REF.UI.STUDENT.SIBLING.ADMISSIONNO.VIOLATEERROR=Given Sibling Admission Number does not exist
REF.UI.STUDENT.RESIDENCENO.ERROR=Please enter a valid Residence No
REF.UI.STUDENT.MOBILENO.ERROR=Please enter a valid Mobile No
REF.UI.STUDENT.EMERGENCYRESIDENCENO.ERROR=Please enter valid Phone No
REF.UI.STUDENT.EMERGENCYOFFICENO.ERROR=Please enter valid Phone No
REF.UI.STUDENT.EMERGENCYMOBILENO.ERROR=Please enter valid Phone No
REF.UI.STUDENT.EMERGENCYEMAIL.ERROR=Please enter valid Email Address
REF.UI.STUDENT.NATIONALIDCARDNUMBER.ERROR=Please enter a valid NIC Number
REF.UI.STUDENT.DELETE.ERROR=Sorry, the selected student cannot be deleted
REF.UI.STUDENT.IMAGEFORMAT.ERROR=Please select an image file for the profile image (e.g.: .jpeg,.png,.jpg,.gif,.bmp)
REF.UI.STUDENT.IMAGESIZE.ERROR=Image size is exceeding the preferred size
STU.UI.FILE.COPY=Error in copying file
STU.UI.FILE.NOT.FOUND=File not Found
STU.UI.FILE.IO=Error occurred during the file writing to webapp folder
STU.UI.FULLNAME.FIELD.TYPE=Please enter a valid Full Name
STU.UI.NAME.INITIALS.FIELD.TYPE=Initials should start with a capital letter and separate initials with dot or space
STU.UI.LASTNAME.FIELD.TYPE=Last Name has invalid character(s) entered.
STU.UI.ADDMISSIONNO.FIELD.TYPE=A valid Admission No can have only numbers and letters
STU.UI.SIBLING.ADDMISSIONNO.FIELD.TYPE=A valid Sibling Admission Number can have only numbers and letters
STU.UI.STUDENT.DISCIPLINE.EXIST=Sorry this Student Discipline Record is already exists. Please add another.
STU.UI.STUDENT.CLUBANDSOCIETY.ACHIEVEMENT.DELETE.ERROR=Sorry this club/society has achievements.
#Admin Module - UserLogin related error messages.
REF.UI.USERLOGIN.LOGIN.ERROR=Couldn't find your login id
REF.UI.USERLOGIN.LOGIN.PASSWORD.ERROR=Password You Entered is Incorrect
REF.UI.USERLOGIN.LDAP.LOGIN.PASSWORD.ERROR=Password is incorrect or user profile expired
REF.UI.USERLOGIN.NEXT.TIME.USER.LOCK=Next Time Your User ID Is Lock
REF.UI.USERLOGIN.USER.LOCK.DORMENT=User ID Lock.(System has marked you as dorment user)
REF.UI.USERLOGIN.USER.LOCK=User ID is Locked.
REF.UI.USERLOGIN.USER.DISABLED=User ID Disabled
REF.UI.USERLOGIN.USER.HAVE.BEEN.TERMINATED=You have been terminated
REF.UI.USERLOGIN.USER.EXPIRED=Your Profile has expired
REF.UI.USERLOGIN.USER.TERMINATED=The User ID is terminated
REF.UI.USERLOGIN.USER.PASSWORD_EXPIRED=User password expired
REF.UI.USERLOGIN.USER.PENDING_LOGIN=This user is pending login
REF.UI.USERDETAILS.SYSTEM.ERROR=System error
REF.UI.FILE.SYSTEM.VIRUS="Virus"
USER.CREATE.USER.PASSWORD.SAME.AS.UNAME.OR.EMAIL.ERROR=Password cannot be Username or email address.
USER.CREATE.USER.PASSWORD.TOO.SHORT.ERROR=Password must be at least 8 characters.
# Login Failure Error
REF.UI.USERLOGIN.ERROR=Please enter valid Username and Password.
# Student module - Discipline tab.
REF.UI.STUDENTDISCIPLINE.COMMENT.REQUIRED=Comment is required field
typeMismatch.date=Date is required field
REF.UI.STUDENTDISCIPLINE.WARNINGLEVEL.REQUIRED=Warning level is required field
REF.UI.STUDENTDISCIPLINE.DELETE=Sorry this discipline cannot be deleted
REF.UI.MANDATORY.FIELD.REQUIRED=Please fill all the mandatory fields marked in asterisk
REF.UI.MANDATORY.FIELD.SELECTED.REQUIRED=Please select all the mandatory fields marked in asterisk
#Reporting Module Error Messages
REF.UI.DATE.FIELD.MANDATORY=Please select a date field
REF.UI.TIME.FIELD.MANDATORY=Please select a valid time value
REF.UI.TEACHER.FIELD.MANDATORY=Please enter Teacher Id
REF.UI.TEACHER.FIELD.INVALID=Invalid format Staff Registration Number
REF.UI.STUDENTID.FIELD.INVALID=Invalid format Student Id
REF.UI.STDUENTID.FIELD.MANDATORY=Student ID is required field
REF.UI.DATE.FIELD.MANDATORY=Date is required field
REF.UI.DATE.FIELD.INVALID=Invalid format of Date
REF.UI.DATE.FIELD.INCORRECT=To Date should be greater than From Date
REF.UI.YEAR.FIELD.MANDATORY=Year is required field
REF.UI.MONTH.FIELD.MANDATORY=Month is required field
REF.UI.MANDATORY.EARLY.FIELD.REQUIRED=Please fill all the mandatory fields marked in asterisk
REF.UI.MANDATORY.LATE.FIELD.REQUIRED=Please fill all the mandatory fields marked in asterisk
REF.UI.CLASS.FIELD.MANDATORY=Class is required field
REF.UI.GRADE.FIELD.MANDATORY=Grade is required field
REF.UI.SUBJECT.FIELD.MANDATORY=Subject is required field
REF.UI.STUDENT.FIELD.INVALID=Invalid format of Student Name
REF.UI.ADMISSION.FIELD.INVALID=Invalid format of Admission Number
REF.UI.STUDENT.FIELD.MANDATORY=Student Name is required field
REF.UI.ADMISSION.FIELD.MANDATORY=Admission Number is required field
REF.UI.TERM.FIELD.MANDATORY=Term is required field
REF.UI.WARNING.FIELD.MANDATORY=Warning Level is required field
REF.UI.NUMOFDAYS.FIELD.MANDATORY=Number of Days is required field
REPORTING.IO.EXCEPTION=Error occurred while writing report to pdf
REF.UI.NO.DATA=No matching data to generate report
REF.UI.NO.DATA.STUDENT=No data found for Admission No or given date period
REF.UI.NO.DATA.TEACHER=No data found for Registration No or given date period
REF.UI.NO.DATA.FOUND=No data found for given date and/or time period
REF.UI.NO.DATA.FOUND.STUDENT=No student data found for given date period
REPORTING.UI.HOLIDAY=Selected date is a holiday
REF.UI.FORMAT.ERROR.LESSTHAN=Less than value should be greater than zero
REF.UI.FORMAT.ERROR.GRATERTHAN=Greater than value cannot be less than zero
REF.UI.FORMAT.ERROR.INBETWEEN.LESS.GRATERTHAN=Both fields cannot be zero
REF.UI.FORMAT.ERROR.INBETWEEN="Marks In:" value should be less than "And:" value
REF.UI.FORMAT.ERROR.INBETWEEN.LESS.GRATERTHAN.NEGETIVE=Values cannot be less than zero
ERROR_MSG_FORMAT_ERROR_THANHUNDARD=Marks cannot be greater than 100
REF.UI.FORMAT.ERROR.INBETWEEN.LESS.GRATERTHAN.MAXIMUM.MARKS=Marks should be between 0 and 100
REF.UI.DATE.FIELD.FUTURE.TO.INVALID=To Date cannot be future date
REF.UI.DATE.FIELD.FUTURE.FROM.INVALID=From Date cannot be future date
REF.UI.NO.SUBJECT.STUDENT=Currently there is not any subjects allocated for this student.
#student subject assignment related error messages
REF.UI.STUDENT.SUBJECTASSIGN.ERROR=Please assign at least one subject to student before save
REF.UI.STUDENT.SUBJECTASSIGN.NOSUBJECTS=At least one subject has to be assigned to the grade
REF.UI.STUDENT.SUBJECTASSIGN.NOSTUDENTS=At least one student has to be assigned to the grade class
REF.UI.STUDENT.MARKASSIGN.NULL.CLASS=Class details are not entered. Please enter class details from the Reference Module
REF.UI.STUDENT.MARKASSIGN.INVALIDMARK=The term mark values should be a number between 0 and the maximum mark.
REF.UI.STUDENT.MARKASSIGN.NORECORD=No Record to save
REF.UI.STUDENT.MARKASSIGN.INVALIDGRADING=Please enter appropriate grading acronym
REF.UI.STUDENT.MARKASSIGN.NULL.CLASS.TERM=Class or Term details are not entered. Please enter the class or term details from the Reference Module
REF.UI.STUDENT.MARKASSIGN.NULL.CLASS=Class details are not entered. Please enter the class details from the Reference Module
REF.UI.STUDENT.MARKASSIGN.EMPTYMARK=The term mark values cannot be empty
REF.UI.SUBJECTTEACHER.ALREADY.EXIST=This teacher already assign for this grade subject. Please use edit button to change class list
REF.UI.STUDENT.TERMMARKCOMPLETED=Sorry term marks entries are already completed
REF.UI.STUDENT.ADVANCE.SEARCH=Please select at least one search criteria
REF.UI.STUDENTLEAVE.DELETE=Sorry this Leave cannot be deleted
STUDENT.LEAVE.ERROR=The Leave has already been applied
SUBJECTTEACHER.SEARCH.NO.RESULT=No results found for the given subject teacher criteria
#Admin module - student class tab
REF.UI.STUDENT.SUCCESSFULLY.ASSIGNED=Selected student(s) has been successfully assigned to the new class
REF.UI.STUDENT.SAMECLASS.ASSIGNED=Selected student(s) has been already assigned to the class
REF.UI.STUDENT.SUBJECTS.ASSIGNED=Different class subjects have been already assigned to the following student(s):
REF.UI.STUDENT.MARK.COMPLETION=This student cannot be assigned to this class. Marking completion has being done.
REF.UI.STUDENT.CLASS.NEW.STUDENT.NODATA=No new students available in the selected year.
REF.UI.STUDENT.CLASS.STUDENT.NODATA=No students available in the selected grade
# Student module - Academic life tab.
REF.UI.STUDENT.DUPLICATEPREFECTTYPE=Selected prefect type has been already assigned for this student
REF.UI.STUDENT.EPREFECT.TYPE.REQUIRED=Please select a valid prefect type
REF.UI.STUDENT.DUPLICATESCHOLARSHIP_TYPE=Selected scholarship type has been already assigned for this student
REF.UI.STUDENT.SCHOLARSHIP.REQUIRED=Please select a valid scholarship type
REF.UI.STUDENT.DUPLICATEACHIEVEMENT_TYPE=Achievement already exists for this student
# Student module - Faith life .
REF.UI.FIELD.TYPE.FAITHlIFE=Comment should be in alphanumeric only
REF.UI.FAITHLIFE.EXIST=Same Religious Activities already exist for this student
# Student module - Co_Curricular .
REF.UI.STUDENT.SPORT.DESCRIPTION.EXIST=This student is already participating in the event
REF.UI.STUDENT_CLUB_SOCIETY.EXIST=This student already assigned for the same club or society
REF.UI.STUDENT.ACHIEVEMENT.DESCRIPTION.EXIST=Same achievement already exist for this student
REF.UI.STUDENT.COCURRICULAR.DATE=Year should be selected
REF.UI.STUDENT.CLUB.DESCRIPTION.EXIST=This student already assigned for the same club or society
REF.UI.FIELD.TYPE.ACHIEVEMENT=Invalid character entered to Activity
REF.UI.FIELD.LENGTH=Field length is too long
REF.UI.DESCRIPTION.REQUIRED=Please fill all the mandatory field marked in asterisk
# SpecialEvent
REF.UI.SPECIAL.EVENT.DELETE=Sorry, this Special Event already has marked attendance
REF.UI.SPECIAL.EVENT.EDIT=Sorry, this Special Event already has marked attendance
REF.UI.FIELD.EXIST=Special Event Already Exists
# Sport
REF.UI.SPORT.DESCRIPTION.EXIST=Sport already exists. Please add another
REF.UI.SPORT.DELETE=Sorry this Sport cannot be deleted
REF.UI.FIELD.TYPE.SPORT=Description of the Sport should be alphabetical only
# SportCategory
REF.UI.SPORT.CATEGORY.DESCRIPTION.EXIST=Sport Category already exists. Please add another
REF.UI.SPORT.CATEGORY.DELETE=Sorry this Sport Category cannot be deleted
REF.UI.SPORTCATEGORY.REQUIRED=Please select all the mandatory fields marked in asterisk
# Holiday
REF.UI.HOLIDAY.EXIST=Holiday already exists. Please add another
REF.UI.HOLIDAY.DELETE=Sorry this Holiday cannot be deleted
REF.UI.FIELD.TYPE.HOLIDAY=Invalid characters
# DonationType
REF.UI.DOANTIONTYPE.DESCRIPTION.EXIST=Donation Type already exists. Please add another
REF.UI.DOANTIONTYPE.DELETE=Sorry this Donation Type cannot be deleted
REF.UI.FIELD.TYPE.DOANTIONTYPE=Description of the Donation Type mismatch
# PrefectType
REF.UI.PREFECT.FIELD.INVALID=Description of the Prefect should be alphabetical only and separated with dash.
# WarningLevel
REF.UI.WARNING.FIELD.INVALID=Invalid data type
# Exam
REF.UI.EXAM.FIELD.INVALID=Invalid data type
# SportSub
REF.UI.SPORTSUB.DESCRIPTION.EXIST=Sport Sub already exists. Please add another
REF.UI.SPORTSUB.DELETE=Sorry this Sports Age Group cannot be deleted
REF.UI.FIELD.TYPE.SPORTSUB=Description of the Sport Sub Should be in alphanumeric only.
# ClubSociety
REF.UI.CLUBSOCIETY.DESCRIPTION.EXIST=Club or Society already exists. Please add another
REF.UI.CLUBSOCIETY.DESCRIPTION.LENGTH=Description length is too long.
REF.UI.CLUB.DELETE=Sorry this Club or Society cannot be deleted
REF.UI.FIELD.TYPE.CLUB=Name of the Sport Category Should be alphabetical only.
# Forgot Password
REF.UI.FOG=FORGOT PASSWORD: New Login Password
FOG.ERROR=Error
FOG.ERROR.USERNAME=Please enter a valid Username
FOG.ERROR.USERNAME.EMAIL=Please enter a valid email
FOG.ERROR.ACCOUNTBLOCKED=Sorry your account has been locked
FOR.ERROR.SECURITYQUESTION.NOANSWER.ERROR=You need to login to the system first
COMMON.EXCEPTION.403=Access is forbidden to the requested page
COMMON.EXCEPTION.404=The server cannot find the requested page
COMMON.EXCEPTION.405=The method specified in the request is not allowed
COMMON.EXCEPTION.SMS=Error occurred while connecting to the server
STUDENT.PROFILE.CREATED=Student Profile has been successfully created
typeMismatch.lessThan=Invalid format for Less Than value
typeMismatch.greaterThan=Invalid format for Greater Than value
typeMismatch.inBetweenGreaterValue=Invalid format for "Marks In:" value
typeMismatch.inBetweenLessValue=Invalid format for In Between Less Than value
# Email related errors.
EMAIL.ERROR=Error occurred while sending email.
EMAIL.AUTHENTICATION.ERROR=Email authentication error.
EMAIL.SEND.ERROR=Email sending failed.Please try again.
EMAIL.STAFF.NO_EMAIL_FOUND=Sorry! No email address found for the Teacher
EMAIL.STAFF.NO_CC_EMAIL_FOUND=Sorry! Copied email address not found
EMAIL.STAFF.SUCCESS_SEND=Your message has been successfully sent to the teacher
# Staff Extra Curricular Tab
STAFF.EXTRA.CURRICULAR.EXIST=This is already assigned to this staff member
# Reference module - Manage Seminar specific error messages
REF.UI.SEMINAR.DESCRIPTION.EXIST=Seminar already exists. Please add another
REF.UI.SEMINAR.DELETE=Sorry this seminar cannot be deleted
EMAIL.ERROR=Error occurred while sending email
#User Management related errors.
USER.UI.DELETE.ACTIVE.USER.ERROR=User cannot delete an active user
USER.UI.NO.RESULT.FOUND.USER.ERROR=No results found for the searched criteria
USER.UI.DELETE.CURRENT.USER.ERROR=User cannot delete current logged user
USER.UI.DISABLE.CURRENT.USER.ERROR=User cannot disable current login user
USER.UI.PAST.USER.ENABLE.ERROR=User cannot enable a past user
USER.UI.DELETE.ERROR=User cannot be deleted
#User Security Questions related errors.
USER.UI.MANDATORY.FIELD.REQUIRED=Please fill all the mandatory fields marked in asterisk
USER.UI.WRAPPERSECURITYQUESTION.INVALID.ANSWER=Please enter a valid answer
USER.UI.WRAPPERSECURITYQUESTION.INCORRECT.ANSWER=Please enter correct answer
USER.SECURITY.QUESTIONS.EXIST=You have already answered security questions
USER.UI.SAME.QUESTION.ERROR=Please select two different questions
#User Security Questions related errors.
USER.CHANGEPASSWORD.ERROR.OLDPASSWORD=Please enter correct password
USER.CHANGEPASSWORD.ERROR.COMPAREPASSWORD=New Password and Confirm Password should be equal
#attendance module
ATTENDANCE.SEARCH.NO.RESULT=No students found for the given search criteria
ATTENDANCE.SEARCH.NO.STAFF=No staff found for the given search criteria
ATTENDANCE.SAVE.FAIL=Error in save
# Staff Leave Tab
STAFF.LEAVE.LEAVEDAY.EXIST=Sorry, this Leave is already applied
STAFF.LEAVE.LEAVEDAY.JOINDATE=Cannot apply Leave before the Join Date
STAFF.LEAVE.LEAVEDAY.DAYTYPE=Sorry, cannot apply leave more than one day for Half Day.
STAFF.LEAVE.LEAVEDAY.HOLIDAY=Sorry, applied dates are Holidays.
STAFF.LEAVE.LEAVE.SUCCESS=Staff Leave has been added successfully and leave has been sent for the approval.
STAFF.LEAVE.LEAVE.UNSUCCESS=Unable to send the approval mail due to server error.
STAFF.LEAVE.LEAVE.UPDATED=Staff Leave has been updated.
STAFF.LEAVE.LEAVE.SUCCESS.MAIL=Staff Leave has been added successfully.
# Study Medium
REF.UI.STUDY.MEDIUM.DELETE=Sorry this Study Medium cannot be deleted
REF.UI.STUDY.MEDIUM.EXISTS=Medium already exists. Please add another
REF.UI.NO.SEARCH.RESULT.MEDIUM=No results found for the given Study Medium criteria
STUDENT.TRAVEL.METHOD.OPTION2=The Travel Method already selected. Please select another Travel Method for option 2
STUDENT.TRAVEL.METHOD.OPTION3=The travel method already selected. Please select another Travel Method for option 3
#Teacher Subject Allocation
STAFF.TEACHER.SUBJECT.ALLOCATION=Teacher Subject Allocation processed successfully
STAFF.TEACHER.SUBJECT.UPDATE=Teacher Subject Allocation updated successfully
STAFF.REGISTRATION.FIELD.TYPE=A valid Registration Number can have only numbers and letters
#General messages
COMMON.MESSAGE.SUCCESSFULLY.ADDED=Successfully Added
COMMON.MESSAGE.SUCCESSFULLY.UPDATED=Successfully Updated
# Exam Subject releated error messages.
REF.UI.EXAMSUBJECT.DELETE=This exam subject cannot be deleted
REF.UI.EXAMSUBJECT.EXIST=This subject cannot be removed
EXAM.SUBJECT.EXIST=Exam subject already exists
# Exam Mark releated error messages.
EXAM.MARK.SEARCH.CRITERIA=Please assign subjects to exam or assign students to class.
EXAM.MARK.SELECT.EXAMINATION=Please select the Examination
EXAM.MARK.SELECT.EXAMINATION.GRADE=Please select Grade and Examination
EXAM.MARK.ADMISSION.EXISTS=Exam Admission already exists
EXAM.MARKS.ALL.TEXT=Please fill all the text fields
EXAM.MARKS.GRADING.ACRONYM=Grading acronym is incorrect
EXAM.MARKS.MADATORY.CORE=N/A is applicable only for optional subjects
EXAM.MARKS.MAX.MARKS=Marks must be less than 100 and greater than 0
EXAM.MARKS.SUCCESS=Successfully Added
EXAM.MARKS.ROLE.UPDATE=Cannot update
EXAM.MARK.UI.ADMISSION=Please provide index number before entering marks
# User Role management messages
USER.ROLE.MANAGE.USER.ROLE.DUPLICATE=Sorry this record already exists. Please add another
USER.ROLE.MANAGE.USER.ROLE.DELETE=User Role cannot be deleted
USER.ROLE.MANAGE.USER.ROLE.INVALID=Role contains invalid characters
USER.ROLE.MANAGE.USER.ROLE.SUCCESS=User Role Successfully Added
USER.ROLE.MANAGE.USER.ROLE.UPDATED=User Role Successfully updated
STUDENT.ASSIGNMENT.MARKS.INVALID=The assignment mark values should be a number between 0 and 100
STUDENT.ASSIGNMENT.MARKS.EMPTY=Please enter assignment mark values between 0 and 100
STUDENT.ASSIGNMENT.MARKS.NO.RESULTS=No results found for the searched criteria
#ExamResultsReport messages
EXAM.RESULTS.REPORT.NO_RESULTS=No Exam Results found for given Exam Index No
EXAM.RESULTS.REPORT.INVALID=Invalid Exam Index No
EXAM.RESULTS.REPORT.NOT.EXISTS=Exam Index No does not exist
REPORT.PRIZE.MARKING.NOT.COMPLETED=Confirmation required within the "Student Mark Entry" to generate the report
#StaffProfileReport messages
STAFF.PROFILE.REPORT.REGNO.NOT.EXISTS=Staff Registration Number does not exist
typeMismatch.staffLeaveType.maxStaffLeaves=
#Staff Service Category error messages
REF.UI.STAFF.SERVICE.CATEGORY.DELETE=Sorry this Staff Service Category cannot be deleted.
#Common Message when any object type is exist.
REF.UI.RECORD.EXIST.ERROR=Sorry this record is already exists. Please add another.
REF.STAFFLEAVETYPE.MAXSTAFFLEAVES.ERROR=Maximum No. Of Staff Leaves can not be less than zero.
#Reference Module - Manage Employment Status
REF.UI.EMPLOYMENTSTATUS.DELETE=Sorry this Employment Status cannot be deleted.
STAFF.LEAVE.TYPE.NOT.AVAILABLE=Sorry There are no available leaves for the selected Staff Leave Type
#Staff Departure Details error messages
REF.UI.STAFF.DEPARTURE_DETAILS.SUCCESSFULLY_ADDED=Successfully Added
REF.UI.STAFF.DEPARTURE_DETAILS.ERROR=Sorry this Staff Member cannot be terminated
REF.UI.STUDENT.SUSPEND_STUDENT.SUCCESSFULLY_ADDED=Student has been successfully suspended
REF.UI.STUDENT.SUCESS_EMAIL=email has been sent to the parent
REF.UI.STUDENT.FAILURE_EMAIL_PARENT_EMAIL=Unable to send the email due to parent email address cannot be found.
REF.UI.STUDENT.FAILURE_FAILURE_EMAIL_PARENT_EXIXTS=Unable to send the email due to parent details are not exists.
REF.UI.STUDENT.FAILURE_SERVER_ERROR=Unable to send the email due to server error.
REF.UI.SUSPEND.DATE.FIELD.INCORRECT_DATE=From Date should be greater than Admission Date
REF.UI.STUDENT.FAILURE_EMAIL_PARENT_NOTEXIST=Unable to send the mail due to parent not exists.
STUDENT.SUSPEND.ALREADY.ON.LEAVE=Leave is already applied.
#Student Departure Details error messages
REF.UI.STUDENT.DEPARTURE_DETAILS.SUCCESSFULLY_ADDED=Successfully Added
REF.UI.STUDENT.DEPARTURE_DETAILS.SUCCESSFULLY_TERMINATED=Successfully Terminated
REF.UI.STUDENT.DEPARTURE_DETAILS.ERROR=Sorry this Student cannot be terminated
REF.UI.DATE.FIELD.INCORRECT_DATE=Departure Date should be greater than Admission Date
REF.UI.TEMPLEAVE.DATE.FIELD.INCORRECT_DATE=From Date should be greater than Admission Date
REF.UI.TEMPLEAVE.STUDENT.SUCCESSFULLY_ADDED=Student has been successfully granted temporary leave.
REF.UI.SUSPEND.REASON=This student is suspended.
#Student class - tooltip  
STUDENT.CLASS.ALLOCATION.EXIST=Future Year Class Allocation Exist
STUDENT.CLASS.ALLOCATION.NOT.EXIST=Future Year Class Allocation Not Exist
CURRENT.STUDENT.CLASS.ALLOCATION.EXIST=Current Year Class Allocation Exist
CURRENT.STUDENT.CLASS.ALLOCATION.NOT.EXIST=Current Year Class Allocation Not Exist
#Student Suspended Details error messages
STUDENT.REJOIN.SUCCESS.MESSAGE=Suspended Student has been successfully Activated.
REF.UI.STUDENT.SUSPEDED_DETAILS.ERROR=Sorry fill Activate date correctly
STUDENT.REJOIN.INVALID.DATE.MESSAGE=Please enter a valid date for activate.
STUDENT.REJOIN.UPDATE.STUDENT.ERROR=Student member update fails due to error in DB.
STUDENT.REJOIN.REASON.EMPTY.MESSAGE=Sorry, Curtailed/Extended Reason Can't be Empty.
STUDENT.UPDATE.STUDENT.ERROR=Error throws while updating a student member.
STUDENT.REJOIN.PARSE.DATE.TO.STRING.ERROR=Error throws while parsing date string to date object.
STUDENT.REJOIN.SAMEDATE.ERROR=Sorry, Suspended Student Can't Rejoin Same Day.
#Staff Member Rejoin error messages
STAFF.REJOIN.JOINDATE.ERROR=Departure date is greater than Rejoined date.
STAFF.REJOIN.UPDATE.STAFF.ERROR=Staff member update fails due to error in DB.
STAFF.REJOIN.INVALID.DATE.MESSAGE=Please enter a valid date for rejoining.
STAFF.REJOIN.SUCCESS.MESSAGE=Staff Member has been successfully rejoined.
#user service create user error messages
USER.CREATE.USER.UNIQUE.UNAME.ERROR=Sorry this User Name is already exists. Please add another.
USER.CREATE.USER.UNIQUE.EMAIL.ERROR=Sorry this Email is already exists. Please add another.
USER.CREATE.USER.IDENTIFICATION.NO.INVALID.ERROR=Sorry this Identification No is invalid. Please add another.
USER.CREATE.USER.IDENTIFICATION.NO.EXIST.ERROR=Sorry, for this identification number has a user account, please add another
USER.CREATE.USER.PAST.ERROR=Sorry this user is a past user. Please add another.
USER.CREATE.USER.STUDENT.NON.CURRENT=Sorry, can't create user accounts for non-current students.
#student re join - temporary leave page
STUDENT.REJOIN.EXTENDED.SUCCESS.MESSAGE=Temporary Leave Extended
STUDENT.REJOIN.EXTENDED.LEAVE.MESSAGE=The temporary leave is extended successfully
STUDENT.REJOIN.CURTAILED.LEAVE.MESSAGE=The temporary leave is curtailed successfully
STUDENT.REJOIN.ACTIVATE.SUCCESS.MESSAGE=Successfully Activated
STUDENT.REJOIN.ACTIVATE.ERROR.MESSAGE=Sorry this Student can not be activated
STUDENT.TEMPEPORY.LEAVE.EXTEND.TO.DATE.ERROR.MESSAGE=To Date can not be null.
STUDENT.TEMPEPORY.LEAVE.SUBJECT.ASSIGN.GRADE.ERROR.MESSAGE=At least one core subject has to be assigned to the grade
STUDENT.TEMPEPORY.LEAVE.ASSIGN.PREVIOUS.GRADE.ERROR.MESSAGE=The student can be assigned to any class within the same grade as previously assigned
STUDENT.TEMPEPORY.LEAVE.MARKING.COMPLETED.ASSIGN.PREVIOUS.GRADE.ERROR.MESSAGE=Student needs to be assigned to the same class as previously assigned. Marking completion has being done.
STUDENT.TEMPEPORY.LEAVE.ACTIVATE.SUCCESS.MESSAGE=Student is activated successfully
STUDENT.TEMPEPORY.LEAVE.ASSIGN.CURRENT.YEAR.ERROR.MESSAGE=Student should be assigned to current year
STUDENT.TEMPEPORY.LEAVE.MARKING.COMPLETED.ASSIGN.NEXT.YEAR.ERROR.MESSAGE=Marking Completed; Student should be assigned to next year
TERM.MARK.ENTRY.COMPLETION=Marking is not done completely to generate this report.
#student first day at school error with the relevant date.
STUDENT.FIRST.DATE.DISCIPLINARY.ERROR=Sorry, you cannot take a disciplinary action before student enrolling.
STUDENT.FIRST.DATE.LEAVE.ERROR=Sorry, you cannot apply a leave before student enrolling.
STUDENT.LEAVE.DATE.PRESENT.ERROR=Sorry, you cannot apply a leave for a present day.
STUDENT.FIRST.DATE.ATTENDANCE.ERROR=Sorry, no attendance results found before student enrolling.
STUDENT.LEAVE.DATE.DISCIPLINARY.ERROR=Sorry, you cannot add disciplinary actions for holidays and leaved days.
#error message for future assignment of the student.
STUDENT.FUTURE.ASSIGNMENT.ERROR=First date at the school is after the selected assign date.
#leaveApprovalProcess specific error messages
LEVAPPRVAL.EMAIL.ADDRESS.NOT.EXIST=Unable to send the email due to staff member email address not exists.
LEVAPPRVAL.UPDATED.SUCCESSFULLY=Staff leave has been updated successfully.
LEVAPPRVAL.EMAIL.SEND.SUCCESS=Email sent to the staff member successfully.
LEVAPPRVAL.EMAIL.SEND.UNSUCCESS=Unable to send the email due to server error.
#beststudentattendance report specific error messages
BESTSTUDENT.REPORT.WORKING.DAYS.ERROR=Selected month has no 10 academic days to display the report
BESTSTUDENT.REPORT.MINIMUM.ATTENDANCE.ERROR=Selected criteria has no minimum attendance to generate the report
#attendanceDashboard specific error messages
ATTENDANCE.ATTENDANCEDASHBOARD.NO.DATA=No matching data to display the result.
ATTENDANCE.ATTENDANCEDASHBOARD.WORKING.DAYS.ERROR=Selected month has no 10 academic days to display the result.
ATTENDANCE.ATTENDANCEDASHBOARD.MINIMUM.ATTENDANCE.ERROR=Selected criteria has no minimum attendance to display the result.
ATTENDANCE.ATTENDANCEDASHBOARD.WORKING.DAYS.ERROR=Selected month has no 10 academic days to display the result.
#attendance report specific error messages
REPORT.UI.NO.DATA=No data to generate the report
REPORT.UI.NO.STUDENT.CLASS.DATA=There are no student exists in the selected grade or class
#phone number validation
STAFF.PHONE.COUNTRY.NO.MATCH=Phone number format is not recognized. Check the country and number.
PHONENUMBER.ERROR=Phone number format is not recognized.
STUDENT.SCHOLARSHIP.REPORT.ERROR=Please select a Scholarship Name.
REF.UI.KPI.DUPLICATE=Duplicate Record
REF.UI.KPI.ANP.ERROR=Record is against the hierarchical manner!!!.. Target starting value (From ANP) must be greater the target finishing value (To ANP) of the previously created grading level.
REF.UI.KPI.FYC.ERROR=Record is against the hierarchical manner!!!.. Target starting value (From FYC) must be greater the target finishing value (To FYC) of the previously created grading level.
REF.UI.KPI.ANP.LESS.ERROR=Record is against the hierarchical manner!!!.. Target starting value (To ANP) must be less the target finishing value (To ANP) of the previously created grading level.
REF.UI.KPI.FYC.LESS.ERROR=Record is against the hierarchical manner!!!.. Target starting value (To FYC) must be less the target finishing value (To FYC) of the previously created grading level.
