#
# LOLC Motor Claim Management System Variables
# The system initialises the variables at startup
#



#-----------
# Database
#-----------
DatabaseDriver=com.mysql.jdbc.Driver
DatabaseURL=jdbc:mysql://
DBServer=***********:3306
DatabaseName=/lolc_mcms_prd
DbUserName=mcms
DbPassword=misyn8b
MaxConnJDBC=100
QueryTimeOutMinute=10
redis-host=redis
#-----------
# Contact
#-----------
MailHost=************
MailAddress=<EMAIL>
EmailUserName=<EMAIL>
EmailUser=<EMAIL>
EmailPassword=1111111


#-----------
# Template File Path
#-----------
Path=/appl/MCMS/
TempFolder=Temp/
TempFileDirectory=/appl/MCMS/temp_dir/



#------------------
# Record List setting
#------------------
NoRecordPerPage=100
NoOfImages=10
CompanyTitle=M I Synergy (Pvt) Ltd

#------------------------
# Sftp details
#------------------------
SftpFileDirectory=/home/<USER>/document/
SftpUserName=misynergy
SftpPassword=misyn8b
SftpHost=localhost
SftpPort=22
PrivateKey=/home/<USER>/.ssh/id_rsa
ActiveMQBorker=tcp://***********:61616
ActiveMQUser=admin
ActiveMQPassword=admin
MailSendUser=LOLC-MCMS
MailClaimPath=http://lolcdc2cmsuat:8080/mcms/CallCenter/viewClaim
AppUrl=http://:lolcdc2cmsuat:8080/mcms/
SyncAppUrl=http://localhost:9080/mcms_sync/
InternalAppUrl=http://lolcdc2cmsuat:8080/mcms/
SftpType=1
ImageResizePercent=0.65
ImageCompressionQualityFactor=0.5
TheftClaimPeriod=180
DocumentNotificationTimeout=3
AriNotificationTimeout=3
Profile=${env}


KeycloakServerUrl=https://uwmotuat.hnbgeneral.com
KeycloakRealm=hnb-general
KeycloakClientId=mcms-webapp-client
KeycloakClientSecret=YuU1IPaToKghkQb9AqFZxAYs8XFfDz5Z

AuthServiceLogoutUrl=https://hnbmcmsauth.hnbgeneral.com/api/logout
DocumentServiceApiUrl=http://storage-service-api:8080/api
ClaimDocumentDirectory=claim-documents

# OnMiSite keycloak token details
TokenClientId=mcms-onmisite-webapp
TokenClientSecret=TthLWNNE4oJnsgTvvAVimgs1fMuQlAAe
TokenContentType=application/x-www-form-urlencoded
TokenGrantType=password
TokenUsername=rtechexe
TokenPassword=123456$
TokenUrl=https://uwmotuat.hnbgeneral.com/realms/hnb-general/protocol/openid-connect/token

# OnMiSite service call details
SaveEndpointUrl=https://hnbonmisite.misynergy.com/misyn/api/v1/jobs/save
SaveEndpointContentType=application/json
AdminEndpointUrl=https://hnbonmisite.misynergy.com/misyn/admin-view/

