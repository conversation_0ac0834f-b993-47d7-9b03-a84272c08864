ROOT_LOCATION=/usr/local/tomcat/logs/MCMS/

log4j.rootLogger=DEBUG, stdout, file
log4j.logger.com.misyn.mcms = DEBUG, LOGFILE

# Console appender
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%-6p] [%t] %C.%M(%F:%L) - %m%n

# File appender
log4j.appender.file=org.apache.log4j.FileAppender
log4j.appender.file.File=${ROOT_LOCATION}HNB_MCMS_SYS.log
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%-6p] [%t] %C.%M(%F:%L) - %m%n


## Define the root logger with appender file
#log4j.rootLogger = DEBUG, LOGFILE
#log4j.logger.com.misyn.mcms = DEBUG, LOGFILE
#
#
## LOGFILE is set to be a DailyRollingFileAppender appender using a PatternLayout.
#log4j.appender.LOGFILE = org.apache.log4j.DailyRollingFileAppender
#log4j.appender.LOGFILE.File=${ROOT_LOCATION}HNB_MCMS_SYS.log
#log4j.appender.LOGFILE.Append = true
##log4j.appender.LOGFILE.Threshold=INFO
#log4j.appender.LOGFILE.DatePattern = 'BK_'yyy-MM-dd
#
#
#
#log4j.appender.LOGFILE.layout = org.apache.log4j.PatternLayout
#log4j.appender.LOGFILE.layout.ConversionPattern = %d{yyyy-MM-dd HH:mm:ss} [%-6p] [%t] %C.%M(%F:%L) - %m%n
#
