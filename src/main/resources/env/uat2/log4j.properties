ROOT_LOCATION=/appl/MCMS/logs/

# Define the root logger with appender file
#log4j.rootLogger = DEBUG, LOGFILE
log4j.logger.com.misyn.mcms = DEBUG, LOGFILE


# LOGFILE is set to be a DailyRollingFileAppender appender using a PatternLayout.
log4j.appender.LOGFILE = org.apache.log4j.DailyRollingFileAppender
log4j.appender.LOGFILE.File=${ROOT_LOCATION}LOLC_MCMS_SYS.log
log4j.appender.LOGFILE.Append = true
#log4j.appender.LOGFILE.Threshold=INFO
log4j.appender.LOGFILE.DatePattern = 'BK_'yyy-MM-dd

#log4j.appender.LOGFILE.filter.a=org.apache.log4j.varia.LevelMatchFilter
#log4j.appender.LOGFILE.filter.a.LevelToMatch=WARN
#log4j.appender.LOGFILE.filter.a.AcceptOnMatch=true

log4j.appender.LOGFILE.layout = org.apache.log4j.PatternLayout
log4j.appender.LOGFILE.layout.ConversionPattern = %d{yyyy-MM-dd HH:mm:ss} [%-6p] [%t] %C.%M(%F:%L) - %m%n
